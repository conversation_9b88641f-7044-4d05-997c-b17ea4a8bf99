from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QPushButton,
                            QLabel, QLineEdit, QTableWidget, QTableWidgetItem,
                            QFormLayout, QTextEdit, QHeaderView, QMessageBox,
                            QDialog, QComboBox, QGroupBox, QDateEdit, QDoubleSpinBox,
                            QSizePolicy, QFrame, QTextBrowser, QMenu, QAction, QFileDialog, QScrollArea)
from PyQt5.QtCore import Qt, pyqtSignal, QDate, QTime, QTimer
from PyQt5.QtGui import QIcon, QFont, QTextDocument, QColor, QPainter, QPixmap, QBrush, QPen, QRadialGradient
from PyQt5.QtPrintSupport import QPrinter, QPrintDialog

from database import Expense, Supplier
from utils import (show_error_message, show_info_message, show_confirmation_message,
                    qdate_to_datetime, datetime_to_qdate, format_currency)
import datetime

from ui.unified_styles import (UnifiedStyles, StyledButton, StyledGroupBox,
                                StyledTable, StyledLabel)
from ui.common_dialogs import WarningDialog
from ui.title_bar_utils import TitleBarStyler
from ui.multi_selection_mixin import MultiSelectionMixin
from sqlalchemy import func


class DeleteExpenseDialog(QDialog):
    """نافذة حذف المصروف مشابهة لنافذة حذف العميل"""

    def __init__(self, parent=None, expense=None):
        super().__init__(parent)
        self.expense = expense
        self.parent_widget = parent
        self.setup_ui()

    def setup_ui(self):
        """إعداد واجهة النافذة مطابقة لنافذة حذف العميل"""
        self.setWindowTitle("💸 حذف - نظام إدارة المصروفات المتطور والشامل")
        self.setWindowFlags(Qt.Dialog | Qt.WindowTitleHint | Qt.WindowCloseButtonHint)
        self.customize_title_bar()
        self.setModal(True)
        self.resize(400, 250)

        # خلفية النافذة مطابقة لنوافذ البرنامج
        self.setStyleSheet("""
            QDialog {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #334155,
                    stop:0.3 #475569, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                    stop:0.6 #60A5FA, stop:0.7 #8B5CF6, stop:0.8 #7C3AED,
                    stop:0.9 #6D28D9, stop:1 #5B21B6);
                border: none;
                border-radius: 15px;
            }
        """)

        layout = QVBoxLayout(self)
        layout.setContentsMargins(15, 15, 15, 15)
        layout.setSpacing(8)

        # عنوان النافذة الداخلي
        title_label = QLabel("💸 حذف المصروف")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                font-size: 16px;
                font-weight: bold;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(239, 68, 68, 0.2),
                    stop:0.5 rgba(220, 38, 38, 0.3),
                    stop:1 rgba(185, 28, 28, 0.2));
                border: 2px solid rgba(239, 68, 68, 0.5);
                border-radius: 8px;
                padding: 6px;
                margin: 3px 0;
                text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
            }
        """)
        layout.addWidget(title_label)

        # معلومات المصروف مضغوطة
        if self.expense:
            info_text = f"💸 {self.expense.title[:15]}{'...' if len(self.expense.title) > 15 else ''}"
            if self.expense.amount:
                info_text += f" | 💰 {self.expense.amount:.0f} ج"

            info_label = QLabel(info_text)
            info_label.setAlignment(Qt.AlignCenter)
            info_label.setStyleSheet("""
                QLabel {
                    color: #ffffff;
                    font-size: 12px;
                    font-weight: bold;
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 rgba(255, 255, 255, 0.1),
                        stop:0.5 rgba(248, 250, 252, 0.15),
                        stop:1 rgba(241, 245, 249, 0.1));
                    border: 1px solid rgba(255, 255, 255, 0.3);
                    border-radius: 6px;
                    padding: 6px;
                    margin: 3px 0;
                    text-shadow: 1px 1px 1px rgba(0, 0, 0, 0.5);
                }
            """)
            layout.addWidget(info_label)

        # سؤال التأكيد
        question_label = QLabel("⚠️ متأكد من الحذف؟")
        question_label.setAlignment(Qt.AlignCenter)
        question_label.setStyleSheet("""
            QLabel {
                color: #fbbf24;
                font-size: 14px;
                font-weight: bold;
                margin: 6px 0;
                text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
            }
        """)
        layout.addWidget(question_label)

        # الأزرار
        buttons_layout = QHBoxLayout()
        buttons_layout.setSpacing(10)

        cancel_button = QPushButton("❌ إلغاء")
        self.style_advanced_button(cancel_button, 'info')
        cancel_button.clicked.connect(self.reject)

        confirm_button = QPushButton("💸 حذف")
        self.style_advanced_button(confirm_button, 'danger')
        confirm_button.clicked.connect(self.accept)

        buttons_layout.addWidget(cancel_button)
        buttons_layout.addWidget(confirm_button)
        layout.addLayout(buttons_layout)

    def style_advanced_button(self, button, button_type):
        """تطبيق تصميم متطور على الأزرار"""
        try:
            if self.parent_widget and hasattr(self.parent_widget, 'style_advanced_button'):
                self.parent_widget.style_advanced_button(button, button_type)
            else:
                colors = {
                    'danger': '#ef4444',
                    'info': '#3b82f6'
                }
                color = colors.get(button_type, '#6b7280')
                button.setStyleSheet(f"""
                    QPushButton {{
                        background-color: {color};
                        color: white;
                        border: none;
                        border-radius: 8px;
                        padding: 12px 30px;
                        font-size: 16px;
                        font-weight: bold;
                        min-width: 120px;
                    }}
                    QPushButton:hover {{
                        background-color: {color}dd;
                        transform: translateY(-2px);
                    }}
                    QPushButton:pressed {{
                        background-color: {color}bb;
                        transform: translateY(0px);
                    }}
                """)
        except Exception as e:
            print(f"خطأ في تصميم الزر: {e}")

    def customize_title_bar(self):
        """تخصيص شريط العنوان"""
        try:
            pixmap = QPixmap(48, 48)
            pixmap.fill(Qt.transparent)
            painter = QPainter(pixmap)
            painter.setRenderHint(QPainter.Antialiasing)

            gradient = QRadialGradient(24, 24, 20)
            gradient.setColorAt(0, QColor(239, 68, 68))
            gradient.setColorAt(0.7, QColor(220, 38, 38))
            gradient.setColorAt(1, QColor(185, 28, 28))

            painter.setBrush(QBrush(gradient))
            painter.setPen(QPen(QColor(255, 255, 255, 180), 2))
            painter.drawEllipse(4, 4, 40, 40)
            painter.setPen(QPen(QColor(255, 255, 255), 3))
            painter.setFont(QFont("Arial", 16, QFont.Bold))
            painter.drawText(12, 30, "💸")
            painter.end()

            self.setWindowIcon(QIcon(pixmap))
            self.apply_advanced_title_bar_styling()
        except Exception as e:
            print(f"خطأ في تخصيص شريط العنوان: {e}")

    def apply_advanced_title_bar_styling(self):
        """تطبيق تصميم شريط العنوان"""
        TitleBarStyler.apply_advanced_title_bar_styling(self)


class ExpenseDialog(QDialog):
    """نافذة حوار لإضافة أو تعديل مصروف - مطابقة للتصميم الموحد"""

    def __init__(self, parent=None, expense=None, session=None):
        super().__init__(parent)
        self.expense = expense
        self.session = session
        self.parent_widget = parent
        self.init_ui()

    def init_ui(self):
        # إعداد نافذة الحوار مطابق للتصميم الموحد
        if self.expense:
            self.setWindowTitle("💸 تعديل مصروف - نظام إدارة المصروفات المتطور والشامل")
        else:
            self.setWindowTitle("💸 إضافة مصروف جديد - نظام إدارة المصروفات المتطور والشامل")

        # إزالة علامة الاستفهام وتحسين شريط العنوان
        self.setWindowFlags(Qt.Dialog | Qt.WindowTitleHint | Qt.WindowCloseButtonHint)

        # تخصيص شريط العنوان (إذا كانت الدالة متاحة)
        if hasattr(self, 'customize_title_bar'):
            self.customize_title_bar()

        # خلفية النافذة مطابقة للنافذة الرئيسية
        self.setStyleSheet("""
            QDialog {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #334155,
                    stop:0.3 #475569, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                    stop:0.6 #60A5FA, stop:0.7 #8B5CF6, stop:0.8 #7C3AED,
                    stop:0.9 #6D28D9, stop:1 #5B21B6);
                border: none;
                border-radius: 15px;
            }
        """)

        self.setModal(True)
        self.resize(650, 650)

        # إنشاء التخطيط الرئيسي مطابق للعملاء والموردين
        main_layout = QVBoxLayout()
        main_layout.setContentsMargins(30, 30, 30, 30)
        main_layout.setSpacing(20)

        # عنوان النافذة مطابق للعملاء والموردين
        title_text = "تعديل بيانات المصروف" if self.expense else "إضافة مصروف جديد"
        title_label = QLabel(f"💸 {title_text}")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #334155,
                    stop:0.3 #475569, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                    stop:0.6 #60A5FA, stop:0.7 #8B5CF6, stop:0.8 #7C3AED,
                    stop:0.9 #6D28D9, stop:1 #5B21B6);
                border: 3px solid #000000;
                border-radius: 10px;
                padding: 15px;
                margin: 10px;
                font-weight: bold;
                font-size: 20px;
                text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
            }
        """)
        main_layout.addWidget(title_label)

        # إنشاء النموذج مطابق للعملاء والموردين
        form_layout = QFormLayout()
        form_layout.setSpacing(15)

        # إنشاء دالة لتصميم النصوص مع عرض مقلل - مطابقة للعملاء والموردين
        def create_styled_label(text, icon, required=False):
            label = QLabel(f"{icon} {text}")
            if required:
                label.setText(f"{icon} {text} *")
            label.setStyleSheet("""
                QLabel {
                    color: #ffffff;
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                        stop:0 rgba(59, 130, 246, 0.8),
                        stop:0.3 rgba(96, 165, 250, 0.7),
                        stop:0.7 rgba(139, 92, 246, 0.7),
                        stop:1 rgba(124, 58, 237, 0.8));
                    border: 2px solid rgba(96, 165, 250, 0.9);
                    border-radius: 5px;
                    padding: 8px 12px;
                    font-weight: bold;
                    font-size: 16px;
                    min-width: 120px;
                    max-width: 120px;
                    text-align: center;
                    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.7);
                    box-shadow: 0 2px 6px rgba(96, 165, 250, 0.4);
                }
            """)
            label.setAlignment(Qt.AlignCenter)
            return label

        # حقل العنوان مطابق للعملاء والموردين
        self.title_edit = QLineEdit()
        self.title_edit.setPlaceholderText("أدخل عنوان المصروف...")
        if self.expense:
            self.title_edit.setText(self.expense.title)
        self.title_edit.setStyleSheet("""
            QLineEdit {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.95),
                    stop:0.2 rgba(248, 250, 252, 0.98),
                    stop:0.4 rgba(241, 245, 249, 0.95),
                    stop:0.6 rgba(248, 250, 252, 0.98),
                    stop:0.8 rgba(255, 255, 255, 0.95),
                    stop:1 rgba(226, 232, 240, 0.9));
                border: 3px solid rgba(96, 165, 250, 0.7);
                border-radius: 12px;
                padding: 12px 20px;
                font-size: 16px;
                font-weight: bold;
                color: #1f2937;
                min-height: 30px;
                min-width: 350px;
                box-shadow: 0 4px 12px rgba(96, 165, 250, 0.2);
                transition: all 0.3s ease;
            }
            QLineEdit:focus {
                border: 4px solid rgba(96, 165, 250, 0.95);
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(240, 249, 255, 0.98),
                    stop:0.2 rgba(224, 242, 254, 0.95),
                    stop:0.4 rgba(186, 230, 253, 0.9),
                    stop:0.6 rgba(224, 242, 254, 0.95),
                    stop:0.8 rgba(240, 249, 255, 0.98),
                    stop:1 rgba(255, 255, 255, 0.95));
                box-shadow: 0 6px 16px rgba(96, 165, 250, 0.4);
                transform: scale(1.01);
            }
        """)
        form_layout.addRow(create_styled_label("العنوان", "💸", True), self.title_edit)

        # حقل المبلغ مطابق للعملاء والموردين
        self.amount_edit = QDoubleSpinBox()
        self.amount_edit.setRange(0, 1000000)
        self.amount_edit.setDecimals(0)
        self.amount_edit.setSingleStep(100)
        self.amount_edit.setSuffix(" جنيه")
        if self.expense:
            self.amount_edit.setValue(self.expense.amount)
        self.amount_edit.setStyleSheet("""
            QDoubleSpinBox {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.95),
                    stop:0.2 rgba(248, 250, 252, 0.98),
                    stop:0.4 rgba(241, 245, 249, 0.95),
                    stop:0.6 rgba(248, 250, 252, 0.98),
                    stop:0.8 rgba(255, 255, 255, 0.95),
                    stop:1 rgba(226, 232, 240, 0.9));
                border: 3px solid rgba(96, 165, 250, 0.7);
                border-radius: 12px;
                padding: 12px 20px;
                font-size: 16px;
                font-weight: bold;
                color: #1f2937;
                min-height: 30px;
                min-width: 350px;
                box-shadow: 0 4px 12px rgba(96, 165, 250, 0.2);
                transition: all 0.3s ease;
            }
            QDoubleSpinBox:focus {
                border: 4px solid rgba(96, 165, 250, 0.95);
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(240, 249, 255, 0.98),
                    stop:0.2 rgba(224, 242, 254, 0.95),
                    stop:0.4 rgba(186, 230, 253, 0.9),
                    stop:0.6 rgba(224, 242, 254, 0.95),
                    stop:0.8 rgba(240, 249, 255, 0.98),
                    stop:1 rgba(255, 255, 255, 0.95));
                box-shadow: 0 6px 16px rgba(96, 165, 250, 0.4);
                transform: scale(1.01);
            }
        """)
        form_layout.addRow(create_styled_label("المبلغ", "💰", True), self.amount_edit)

        # حقل التاريخ مطابق للعملاء والموردين
        self.date_edit = QDateEdit()
        self.date_edit.setDate(QDate.currentDate())
        self.date_edit.setCalendarPopup(True)
        self.date_edit.setDisplayFormat("yyyy-MM-dd")
        if self.expense:
            self.date_edit.setDate(datetime_to_qdate(self.expense.date))
        self.date_edit.setStyleSheet("""
            QDateEdit {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.95),
                    stop:0.2 rgba(248, 250, 252, 0.98),
                    stop:0.4 rgba(241, 245, 249, 0.95),
                    stop:0.6 rgba(248, 250, 252, 0.98),
                    stop:0.8 rgba(255, 255, 255, 0.95),
                    stop:1 rgba(226, 232, 240, 0.9));
                border: 3px solid rgba(96, 165, 250, 0.7);
                border-radius: 12px;
                padding: 12px 20px;
                font-size: 16px;
                font-weight: bold;
                color: #1f2937;
                min-height: 30px;
                min-width: 350px;
                box-shadow: 0 4px 12px rgba(96, 165, 250, 0.2);
                transition: all 0.3s ease;
            }
            QDateEdit:focus {
                border: 4px solid rgba(96, 165, 250, 0.95);
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(240, 249, 255, 0.98),
                    stop:0.2 rgba(224, 242, 254, 0.95),
                    stop:0.4 rgba(186, 230, 253, 0.9),
                    stop:0.6 rgba(224, 242, 254, 0.95),
                    stop:0.8 rgba(240, 249, 255, 0.98),
                    stop:1 rgba(255, 255, 255, 0.95));
                box-shadow: 0 6px 16px rgba(96, 165, 250, 0.4);
                transform: scale(1.01);
            }
        """)
        form_layout.addRow(create_styled_label("التاريخ", "📅", True), self.date_edit)

        # حقل الفئة مطابق للمشاريع والعقارات بدون إطارات
        self.category_combo = QComboBox()
        # خيارات متعددة للفئات مطابقة للمشاريع والعقارات
        categories = [
            "مواد خام", "أدوات", "صيانة", "إيجار", "مرافق",
            "رواتب", "تسويق", "نقل", "وقود", "كهرباء", "مياه",
            "اتصالات", "تأمين", "ضرائب", "أخرى"
        ]
        self.category_combo.addItems(categories)
        if self.expense:
            index = self.category_combo.findText(self.expense.category)
            if index >= 0:
                self.category_combo.setCurrentIndex(index)
        self.category_combo.setStyleSheet("""
            QComboBox {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.95),
                    stop:0.2 rgba(248, 250, 252, 0.98),
                    stop:0.4 rgba(241, 245, 249, 0.95),
                    stop:0.6 rgba(248, 250, 252, 0.98),
                    stop:0.8 rgba(255, 255, 255, 0.95),
                    stop:1 rgba(226, 232, 240, 0.9));
                border: none;
                border-radius: 10px;
                padding: 14px 18px;
                font-family: 'Segoe UI', 'Tahoma', 'Arial', sans-serif;
                font-size: 14px;
                font-weight: 500;
                color: #1f2937;
                selection-background-color: rgba(96, 165, 250, 0.3);
                box-shadow: 0 4px 15px rgba(96, 165, 250, 0.25);
            }
            QComboBox:hover {
                transform: translateY(-1px);
                box-shadow: 0 6px 20px rgba(96, 165, 250, 0.3);
            }
            QComboBox:focus {
                box-shadow: 0 8px 25px rgba(96, 165, 250, 0.4);
                transform: scale(1.02);
            }
        """)
        form_layout.addRow(create_styled_label("الفئة", "📂", True), self.category_combo)

        # حقل المورد مطابق للعملاء والموردين
        self.supplier_combo = QComboBox()
        self.supplier_combo.addItem("لا يوجد", None)
        # تحميل الموردين من قاعدة البيانات
        if self.session:
            from database import Supplier
            suppliers = self.session.query(Supplier).all()
            for supplier in suppliers:
                self.supplier_combo.addItem(supplier.name, supplier.id)

        if self.expense and self.expense.supplier_id:
            for i in range(self.supplier_combo.count()):
                if self.supplier_combo.itemData(i) == self.expense.supplier_id:
                    self.supplier_combo.setCurrentIndex(i)
                    break

        self.supplier_combo.setStyleSheet("""
            QComboBox {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.95),
                    stop:0.2 rgba(248, 250, 252, 0.98),
                    stop:0.4 rgba(241, 245, 249, 0.95),
                    stop:0.6 rgba(248, 250, 252, 0.98),
                    stop:0.8 rgba(255, 255, 255, 0.95),
                    stop:1 rgba(226, 232, 240, 0.9));
                border: 3px solid rgba(96, 165, 250, 0.7);
                border-radius: 12px;
                padding: 12px 20px;
                font-size: 16px;
                font-weight: bold;
                color: #1f2937;
                min-height: 30px;
                min-width: 350px;
                box-shadow: 0 4px 12px rgba(96, 165, 250, 0.2);
                transition: all 0.3s ease;
            }
            QComboBox:focus {
                border: 4px solid rgba(96, 165, 250, 0.95);
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(240, 249, 255, 0.98),
                    stop:0.2 rgba(224, 242, 254, 0.95),
                    stop:0.4 rgba(186, 230, 253, 0.9),
                    stop:0.6 rgba(224, 242, 254, 0.95),
                    stop:0.8 rgba(240, 249, 255, 0.98),
                    stop:1 rgba(255, 255, 255, 0.95));
                box-shadow: 0 6px 16px rgba(96, 165, 250, 0.4);
                transform: scale(1.01);
            }
            QComboBox::drop-down {
                border: none;
                width: 30px;
            }
            QComboBox::down-arrow {
                image: none;
                border: 2px solid #3b82f6;
                width: 8px;
                height: 8px;
                border-top: none;
                border-left: none;
                transform: rotate(45deg);
            }
        """)
        form_layout.addRow(create_styled_label("المورد", "🏢"), self.supplier_combo)

        # حقل الملاحظات مطابق للعملاء والموردين
        self.notes_edit = QTextEdit()
        self.notes_edit.setPlaceholderText("أدخل أي ملاحظات إضافية...")
        self.notes_edit.setMaximumHeight(80)
        if self.expense:
            self.notes_edit.setText(self.expense.notes or "")
        self.notes_edit.setStyleSheet("""
            QTextEdit {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.95),
                    stop:0.2 rgba(248, 250, 252, 0.98),
                    stop:0.4 rgba(241, 245, 249, 0.95),
                    stop:0.6 rgba(248, 250, 252, 0.98),
                    stop:0.8 rgba(255, 255, 255, 0.95),
                    stop:1 rgba(226, 232, 240, 0.9));
                border: 3px solid rgba(96, 165, 250, 0.7);
                border-radius: 12px;
                padding: 12px 20px;
                font-size: 16px;
                font-weight: bold;
                color: #1f2937;
                min-height: 60px;
                min-width: 350px;
                box-shadow: 0 4px 12px rgba(96, 165, 250, 0.2);
                transition: all 0.3s ease;
            }
            QTextEdit:focus {
                border: 4px solid rgba(96, 165, 250, 0.95);
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(240, 249, 255, 0.98),
                    stop:0.2 rgba(224, 242, 254, 0.95),
                    stop:0.4 rgba(186, 230, 253, 0.9),
                    stop:0.6 rgba(224, 242, 254, 0.95),
                    stop:0.8 rgba(240, 249, 255, 0.98),
                    stop:1 rgba(255, 255, 255, 0.95));
                box-shadow: 0 6px 16px rgba(96, 165, 250, 0.4);
                transform: scale(1.01);
            }
        """)
        form_layout.addRow(create_styled_label("ملاحظات", "📝"), self.notes_edit)

        main_layout.addLayout(form_layout)

        # أزرار التحكم - مطابقة للعملاء والموردين (ترتيب صحيح)
        button_layout = QHBoxLayout()

        # زر الحفظ مطابق للعملاء والموردين
        save_button = QPushButton("💾 حفظ")
        if hasattr(self.parent_widget, 'style_advanced_button'):
            self.parent_widget.style_advanced_button(save_button, 'emerald')
        save_button.clicked.connect(self.accept)

        # زر الإلغاء مطابق للعملاء والموردين
        cancel_button = QPushButton("❌ إلغاء")
        if hasattr(self.parent_widget, 'style_advanced_button'):
            self.parent_widget.style_advanced_button(cancel_button, 'danger')
        cancel_button.clicked.connect(self.reject)

        # ترتيب صحيح: الإلغاء أولاً ثم الحفظ
        button_layout.addWidget(cancel_button)
        button_layout.addWidget(save_button)
        main_layout.addLayout(button_layout)

        self.setLayout(main_layout)

    def get_data(self):
        """الحصول على بيانات المصروف من النموذج"""
        title = self.title_edit.text().strip()
        amount = self.amount_edit.value()
        date = qdate_to_datetime(self.date_edit.date())
        category = self.category_combo.currentText()
        supplier_id = self.supplier_combo.currentData()
        notes = self.notes_edit.toPlainText().strip()

        # التحقق من صحة البيانات
        if not title:
            show_error_message("خطأ", "يجب إدخال عنوان المصروف")
            return None

        if amount <= 0:
            show_error_message("خطأ", "يجب أن يكون المبلغ أكبر من صفر")
            return None

        return {
            'title': title,
            'amount': amount,
            'date': date,
            'category': category,
            'supplier_id': supplier_id,
            'notes': notes
        }

    def customize_title_bar(self):
        """تخصيص شريط العنوان مطابق للتصميم الموحد"""
        try:
            # إنشاء أيقونة مخصصة للمصروفات
            pixmap = QPixmap(48, 48)
            pixmap.fill(Qt.transparent)

            painter = QPainter(pixmap)
            painter.setRenderHint(QPainter.Antialiasing)
            painter.setRenderHint(QPainter.SmoothPixmapTransform)

            # إنشاء تدرج للأيقونة
            from PyQt5.QtGui import QRadialGradient
            gradient = QRadialGradient(24, 24, 20)
            gradient.setColorAt(0.0, QColor(15, 23, 42))
            gradient.setColorAt(0.2, QColor(30, 41, 59))
            gradient.setColorAt(0.4, QColor(37, 99, 235))
            gradient.setColorAt(0.6, QColor(59, 130, 246))
            gradient.setColorAt(0.8, QColor(96, 165, 250))
            gradient.setColorAt(1.0, QColor(147, 197, 253))

            brush = QBrush(gradient)
            painter.setBrush(brush)

            # إضافة حدود ذهبية
            from PyQt5.QtGui import QPen
            pen = QPen(QColor(251, 191, 36), 3)
            pen.setStyle(Qt.SolidLine)
            painter.setPen(pen)

            # رسم دائرة
            painter.drawEllipse(3, 3, 42, 42)

            # إضافة تأثير داخلي
            inner_gradient = QRadialGradient(24, 24, 15)
            inner_gradient.setColorAt(0.0, QColor(255, 255, 255, 80))
            inner_gradient.setColorAt(1.0, QColor(255, 255, 255, 0))

            painter.setBrush(QBrush(inner_gradient))
            painter.setPen(Qt.NoPen)
            painter.drawEllipse(8, 8, 32, 32)

            # إضافة رمز المصروف
            painter.setPen(QColor(255, 255, 255))
            font = painter.font()
            font.setPointSize(20)
            font.setBold(True)
            painter.setFont(font)
            painter.drawText(pixmap.rect(), Qt.AlignCenter, "💸")

            painter.end()

            icon = QIcon(pixmap)
            self.setWindowIcon(icon)

            # تطبيق تصميم شريط العنوان
            self.apply_advanced_title_bar_styling()

        except Exception as e:
            print(f"تحذير: فشل في تخصيص شريط العنوان: {e}")

    def apply_advanced_title_bar_styling(self):
        """تطبيق تصميم شريط العنوان"""
        TitleBarStyler.apply_advanced_title_bar_styling(self)

class ExpensesWidget(QWidget, MultiSelectionMixin):
    """واجهة إدارة المصروفات مع التحديد المتعدد"""

    def __init__(self, session):
        super().__init__()
        self.session = session
        self.selected_items = []
        self.init_ui()
        # تأجيل تحميل البيانات لتحسين الأداء
        QTimer.singleShot(100, self.refresh_data)

    def safe_call_method(self, method_name):
        """استدعاء آمن للدوال مع فحص وجودها"""
        try:
            if hasattr(self, method_name):
                method = getattr(self, method_name)
                if callable(method):
                    method()
                    return True

            # إذا لم توجد الدالة، عرض رسالة
            show_info_message("قريباً", f"ميزة {method_name} قيد التطوير")
            return False

        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ في تنفيذ العملية: {str(e)}")
            return False

    def init_ui(self):
        # إنشاء التخطيط الرئيسي مع تقليل المساحات لاستغلال الجدول
        main_layout = QVBoxLayout()
        main_layout.setContentsMargins(2, 2, 2, 2)  # تقليل الهوامش من 10 إلى 2
        main_layout.setSpacing(2)  # تقليل المسافات من 8 إلى 2

        # إضافة العنوان الرئيسي المطور والمحسن مطابق للفواتير
        title_label = QLabel("💰 إدارة المصروفات المتطورة - نظام شامل ومتقدم لإدارة المصروفات مع أدوات احترافية للبحث والتحليل")
        title_label.setFont(QFont("Segoe UI", 18, QFont.Bold))  # خط أكبر وأوضح
        title_label.setAlignment(Qt.AlignCenter)  # توسيط النص في المنتصف
        title_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #334155,
                    stop:0.3 #475569, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                    stop:0.6 #60A5FA, stop:0.7 #8B5CF6, stop:0.8 #7C3AED,
                    stop:0.9 #6D28D9, stop:1 #5B21B6);
                border: 3px solid #000000;
                border-radius: 10px;
                padding: 4px 10px;
                margin: 2px;
                font-weight: bold;
                max-height: 40px;
                min-height: 40px;
            }
        """)
        main_layout.addWidget(title_label)

        # إنشاء إطار علوي محسن بنفس الأسلوب القديم (صف واحد) مطابق للموردين
        top_frame = QFrame()
        top_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #ffffff,
                    stop:0.3 #f8fafc,
                    stop:0.7 #e2e8f0,
                    stop:1 #cbd5e0);
                border: 3px solid #000000;
                border-radius: 10px;
                margin: 1px;
                padding: 0px;
                max-height: 65px;
                min-height: 60px;
            }
        """)

        # تخطيط أفقي واحد محسن (الطريقة القديمة)
        search_layout = QHBoxLayout()
        search_layout.setContentsMargins(0, 0, 0, 0)  # إزالة الهوامش للتوسيط الدقيق
        search_layout.setSpacing(4)  # مسافات متوازنة

        # إنشاء حاوي عمودي للتوسيط الحقيقي
        top_container = QVBoxLayout()
        top_container.setContentsMargins(6, 0, 6, 0)  # هوامش جانبية فقط
        top_container.setSpacing(0)

        # إضافة مساحة فارغة أعلى للتوسيط
        top_container.addStretch(1)

        # إضافة التخطيط الأفقي في المنتصف
        top_container.addLayout(search_layout)

        # إضافة مساحة فارغة أسفل للتوسيط
        top_container.addStretch(1)

        # تسمية البحث محسنة مع ألوان موحدة مطابقة للفواتير
        search_label = QLabel("🔍 بحث:")
        search_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                font-size: 16px;
                font-weight: 900;
                padding: 8px 12px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(37, 99, 235, 0.8),
                    stop:0.2 rgba(59, 130, 246, 0.7),
                    stop:0.4 rgba(96, 165, 250, 0.6),
                    stop:0.6 rgba(139, 92, 246, 0.7),
                    stop:0.8 rgba(124, 58, 237, 0.8),
                    stop:1 rgba(109, 40, 217, 0.7));
                border: 3px solid rgba(96, 165, 250, 0.8);
                border-radius: 15px;
                min-width: 70px;
                max-width: 70px;
                max-height: 38px;
                min-height: 34px;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
                transition: all 0.3s ease;
                text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
            }
            QLabel:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(59, 130, 246, 0.9),
                    stop:0.2 rgba(96, 165, 250, 0.8),
                    stop:0.4 rgba(139, 92, 246, 0.7),
                    stop:0.6 rgba(124, 58, 237, 0.8),
                    stop:0.8 rgba(109, 40, 217, 0.9),
                    stop:1 rgba(91, 33, 182, 0.8));
                border: 4px solid rgba(96, 165, 250, 0.95);
                transform: translateY(-1px);
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.25);
            }
        """)
        search_label.setAlignment(Qt.AlignCenter)  # توسيط النص داخل التسمية

        self.search_edit = QLineEdit()
        self.search_edit.setPlaceholderText("🔎 ابحث بالعنوان، الفئة، المورد أو الملاحظات...")
        self.search_edit.textChanged.connect(self.filter_expenses)
        self.search_edit.setStyleSheet("""
            QLineEdit {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.9),
                    stop:0.2 rgba(248, 250, 252, 0.95),
                    stop:0.4 rgba(241, 245, 249, 0.9),
                    stop:0.6 rgba(248, 250, 252, 0.95),
                    stop:0.8 rgba(255, 255, 255, 0.9),
                    stop:1 rgba(226, 232, 240, 0.85));
                border: 3px solid rgba(96, 165, 250, 0.8);
                border-radius: 15px;
                padding: 8px 15px;
                font-size: 16px;
                font-weight: 900;
                color: #1f2937;
                max-height: 38px;
                min-height: 34px;
                selection-background-color: rgba(96, 165, 250, 0.3);
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
                transition: all 0.3s ease;
            }
            QLineEdit:focus {
                border: 4px solid rgba(96, 165, 250, 0.95);
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(240, 249, 255, 0.95),
                    stop:0.2 rgba(224, 242, 254, 0.9),
                    stop:0.4 rgba(186, 230, 253, 0.85),
                    stop:0.6 rgba(224, 242, 254, 0.9),
                    stop:0.8 rgba(240, 249, 255, 0.95),
                    stop:1 rgba(255, 255, 255, 0.9));
                box-shadow: 0 4px 12px rgba(96, 165, 250, 0.3);
            }
            QLineEdit:hover {
                border: 4px solid rgba(96, 165, 250, 0.9);
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(250, 251, 255, 0.95),
                    stop:0.2 rgba(241, 245, 249, 0.9),
                    stop:0.4 rgba(226, 232, 240, 0.85),
                    stop:0.6 rgba(241, 245, 249, 0.9),
                    stop:0.8 rgba(250, 251, 255, 0.95),
                    stop:1 rgba(255, 255, 255, 0.9));
                box-shadow: 0 3px 10px rgba(96, 165, 250, 0.2);
            }
        """)

        search_button = QPushButton("🔍")
        search_button.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(37, 99, 235, 0.8),
                    stop:0.2 rgba(59, 130, 246, 0.7),
                    stop:0.4 rgba(96, 165, 250, 0.6),
                    stop:0.6 rgba(139, 92, 246, 0.7),
                    stop:0.8 rgba(124, 58, 237, 0.8),
                    stop:1 rgba(109, 40, 217, 0.7));
                color: #ffffff;
                border: 2px solid rgba(96, 165, 250, 0.7);
                border-radius: 15px;
                padding: 8px;
                font-size: 22px;
                font-weight: 900;
                min-width: 50px;
                max-width: 50px;
                max-height: 38px;
                min-height: 34px;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
                transition: all 0.3s ease;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(59, 130, 246, 0.9),
                    stop:0.2 rgba(96, 165, 250, 0.8),
                    stop:0.4 rgba(139, 92, 246, 0.7),
                    stop:0.6 rgba(124, 58, 237, 0.8),
                    stop:0.8 rgba(109, 40, 217, 0.9),
                    stop:1 rgba(91, 33, 182, 0.8));
                border: 3px solid rgba(96, 165, 250, 0.9);
                transform: translateY(-1px);
                box-shadow: 0 4px 12px rgba(96, 165, 250, 0.3);
            }
            QPushButton:pressed {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(29, 78, 216, 0.9),
                    stop:0.2 rgba(37, 99, 235, 0.8),
                    stop:0.4 rgba(59, 130, 246, 0.7),
                    stop:0.6 rgba(96, 165, 250, 0.8),
                    stop:0.8 rgba(124, 58, 237, 0.9),
                    stop:1 rgba(109, 40, 217, 0.8));
                border: 3px solid rgba(96, 165, 250, 0.95);
                transform: translateY(1px);
                box-shadow:
                    0 0 15px rgba(96, 165, 250, 0.4),
                    0 2px 8px rgba(0, 0, 0, 0.4),
                    inset 0 1px 2px rgba(255, 255, 255, 0.3);
            }
        """)
        search_button.clicked.connect(self.filter_expenses)
        search_button.setToolTip("بحث متقدم")
        search_button.setSizePolicy(QSizePolicy.Fixed, QSizePolicy.Fixed)
        search_button.setContentsMargins(0, 0, 0, 0)

        # تسمية التصفية مطورة بألوان احترافية مطابقة للفواتير
        filter_label = QLabel("🎯 حالة:")
        filter_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                font-size: 16px;
                font-weight: 900;
                padding: 8px 12px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(37, 99, 235, 0.8),
                    stop:0.2 rgba(59, 130, 246, 0.7),
                    stop:0.4 rgba(96, 165, 250, 0.6),
                    stop:0.6 rgba(139, 92, 246, 0.7),
                    stop:0.8 rgba(124, 58, 237, 0.8),
                    stop:1 rgba(109, 40, 217, 0.7));
                border: 2px solid rgba(96, 165, 250, 0.7);
                border-radius: 15px;
                min-width: 65px;
                max-width: 65px;
                max-height: 38px;
                min-height: 34px;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
                transition: all 0.3s ease;
                text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
            }
            QLabel:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(59, 130, 246, 0.9),
                    stop:0.2 rgba(96, 165, 250, 0.8),
                    stop:0.4 rgba(139, 92, 246, 0.7),
                    stop:0.6 rgba(124, 58, 237, 0.8),
                    stop:0.8 rgba(109, 40, 217, 0.9),
                    stop:1 rgba(91, 33, 182, 0.8));
                border: 3px solid rgba(96, 165, 250, 0.9);
                transform: translateY(-1px);
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.25);
            }
        """)
        filter_label.setAlignment(Qt.AlignCenter)  # توسيط النص داخل التسمية

        # إنشاء قائمة تصفية مخصصة ومطورة مطابقة للفواتير
        self.create_custom_status_filter()

        # إضافة جميع العناصر للصف الواحد مع استغلال العرض الكامل داخل الإطار
        search_layout.addWidget(search_label, 0, Qt.AlignVCenter)
        search_layout.addWidget(self.search_edit, 2, Qt.AlignVCenter)  # يأخذ مساحة أكبر
        search_layout.addWidget(search_button, 0, Qt.AlignVCenter)
        search_layout.addWidget(filter_label, 0, Qt.AlignVCenter)
        search_layout.addWidget(self.status_filter_frame, 1, Qt.AlignVCenter)

        # تعيين التخطيط للإطار العلوي - استخدام الحاوي العمودي للتوسيط
        top_frame.setLayout(top_container)

        # إنشاء جدول المصروفات المتطور والمحسن
        self.create_advanced_expenses_table()

        main_layout.addWidget(top_frame)
        main_layout.addWidget(self.expenses_table, 1)  # إعطاء الجدول أولوية في التمدد

        # إنشاء إطار سفلي للأزرار متساوي مع الجدول وارتفاع أقل مطابق للموردين
        bottom_frame = QFrame()
        bottom_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #ffffff,
                    stop:0.3 #f8fafc,
                    stop:0.7 #e2e8f0,
                    stop:1 #cbd5e0);
                border: 3px solid #000000;
                border-radius: 10px;
                margin: 1px;
                padding: 0px;
                max-height: 75px;
                min-height: 70px;
            }
        """)
        actions_layout = QHBoxLayout()
        actions_layout.setContentsMargins(0, 0, 0, 0)  # إزالة الهوامش للتوسيط الدقيق
        actions_layout.setSpacing(4)  # مسافة أكبر بين الأزرار لتوزيع أفضل

        # إنشاء حاوي عمودي للتوسيط الحقيقي
        bottom_container = QVBoxLayout()
        bottom_container.setContentsMargins(6, 0, 6, 0)  # هوامش جانبية فقط
        bottom_container.setSpacing(0)

        # إضافة مساحة فارغة أعلى للتوسيط
        bottom_container.addStretch(1)

        # إضافة التخطيط الأفقي في المنتصف
        bottom_container.addLayout(actions_layout)

        # إضافة مساحة فارغة أسفل للتوسيط
        bottom_container.addStretch(1)

        # إنشاء الأزرار بأحجام متناسقة حسب الترتيب الجديد مطابق للموردين

        # المجموعة الأولى - العمليات الأساسية مع ألوان متنوعة مطابقة للفواتير
        self.add_button = QPushButton("➕ إضافة مصروف")
        self.style_advanced_button(self.add_button, 'emerald', has_menu=True)  # أخضر زمردي مميز مع قائمة
        self.add_button.clicked.connect(self.add_expense)
        self.add_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        self.edit_button = QPushButton("✏️ تعديل")
        self.style_advanced_button(self.edit_button, 'info')  # أزرق سماوي متطور مطابق للفواتير
        self.edit_button.clicked.connect(self.edit_expense)
        self.edit_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        self.delete_button = QPushButton("🗑️ حذف")
        self.style_advanced_button(self.delete_button, 'danger')  # أحمر تحذيري
        self.delete_button.clicked.connect(self.delete_expense)
        self.delete_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        # زر التحديث بسيط بدون قائمة منسدلة مطابق للفواتير
        self.refresh_button = QPushButton("🔄 تحديث")
        self.style_advanced_button(self.refresh_button, 'modern_teal')  # تصميم حديث ومتطور
        self.refresh_button.clicked.connect(self.refresh_data)
        self.refresh_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        # المجموعة الثانية - العمليات المتقدمة مطابقة للفواتير
        self.view_button = QPushButton("👁️ عرض التفاصيل")
        self.style_advanced_button(self.view_button, 'indigo', has_menu=True)  # بنفسجي للتفاصيل
        self.view_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        # ربط زر العرض بالوظيفة الأساسية
        self.view_button.clicked.connect(self.view_expense)

        self.export_button = QPushButton("📤 تصدير ▼")
        self.style_advanced_button(self.export_button, 'info', has_menu=True)  # لون متسق مع نظام الألوان
        self.export_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)



        # إنشاء قائمة منسدلة للتصدير المتقدمة مطابقة تماماً للعملاء
        export_menu = QMenu(self)
        export_menu.setStyleSheet("""
            QMenu {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #334155,
                    stop:0.3 #475569, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                    stop:0.6 #60A5FA, stop:0.7 #8B5CF6, stop:0.8 #7C3AED,
                    stop:0.9 #6D28D9, stop:1 #5B21B6);
                border: none;
                border-radius: 12px;
                padding: 8px;
                color: #ffffff;
                font-family: 'Segoe UI', 'Tahoma', 'Arial', sans-serif;
                font-weight: 900;
                font-size: 13px;
                box-shadow: 0 10px 25px rgba(0, 0, 0, 0.4),
                           0 5px 15px rgba(59, 130, 246, 0.2),
                           inset 0 1px 0 rgba(255, 255, 255, 0.1);
                min-width: 200px;
            }
            QMenu::item {
                background: transparent;
                padding: 10px 5px 10px 5px;
                margin: 2px;
                border: none;
                border-radius: 8px;
                color: #ffffff;
                font-weight: 700;
                font-size: 13px;
                text-align: left;
                min-height: 20px;
                text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.7);
                padding-left: 110px !important;
            }
            QMenu::item:selected {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(59, 130, 246, 0.8),
                    stop:0.3 rgba(96, 165, 250, 0.7),
                    stop:0.7 rgba(139, 92, 246, 0.6),
                    stop:1 rgba(124, 58, 237, 0.7));
                color: #ffffff;
                font-weight: 900;
                border: 1px solid rgba(255, 255, 255, 0.4);
                border-radius: 8px;
                box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4),
                           0 0 15px rgba(96, 165, 250, 0.3),
                           inset 0 1px 0 rgba(255, 255, 255, 0.2);
                text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
                transform: scale(1.02);
            }
            QMenu::item:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(96, 165, 250, 0.3),
                    stop:0.5 rgba(139, 92, 246, 0.25),
                    stop:1 rgba(124, 58, 237, 0.3));
                border: 1px solid rgba(255, 255, 255, 0.3);
                border-radius: 8px;
                box-shadow: 0 2px 8px rgba(96, 165, 250, 0.3);
                text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.6);
            }
            QMenu::separator {
                height: 2px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 rgba(255, 255, 255, 0.2),
                    stop:0.5 rgba(96, 165, 250, 0.4),
                    stop:1 rgba(255, 255, 255, 0.2));
                margin: 6px 12px;
                border: none;
                border-radius: 1px;
            }
        """)

        # قسم التصدير الأساسي - ربط مؤجل للدوال
        excel_action = QAction("📊 تصدير Excel متقدم", self)
        excel_action.triggered.connect(lambda: self.safe_call_method('export_excel_advanced'))
        export_menu.addAction(excel_action)

        csv_action = QAction("📄 تصدير CSV شامل", self)
        csv_action.triggered.connect(lambda: self.safe_call_method('export_csv_advanced'))
        export_menu.addAction(csv_action)

        pdf_action = QAction("📋 تصدير PDF تفصيلي", self)
        pdf_action.triggered.connect(lambda: self.safe_call_method('export_pdf_advanced'))
        export_menu.addAction(pdf_action)

        # فاصل
        export_menu.addSeparator()

        # قسم التقارير المتقدمة
        detailed_action = QAction("📊 تقرير تفصيلي", self)
        detailed_action.triggered.connect(lambda: self.safe_call_method('export_detailed_report'))
        export_menu.addAction(detailed_action)

        balance_action = QAction("💰 تقرير الأرصدة", self)
        balance_action.triggered.connect(lambda: self.safe_call_method('export_balance_report'))
        export_menu.addAction(balance_action)

        # فاصل
        export_menu.addSeparator()

        # قسم التصدير المخصص - ربط مؤجل للدوال
        custom_action = QAction("⚙️ تصدير مخصص", self)
        custom_action.triggered.connect(lambda: self.safe_call_method('export_custom'))
        export_menu.addAction(custom_action)

        backup_action = QAction("💾 إنشاء نسخة احتياطية", self)
        backup_action.triggered.connect(lambda: self.safe_call_method('export_backup'))
        export_menu.addAction(backup_action)

        restore_action = QAction("📥 استعادة نسخة احتياطية", self)
        restore_action.triggered.connect(lambda: self.safe_call_method('restore_backup'))
        export_menu.addAction(restore_action)

        # تخصيص موضع وعرض القائمة
        def show_export_menu():
            """عرض قائمة التصدير فوق الزر مباشرة بنفس العرض"""
            # الحصول على موضع الزر (فوق الزر)
            button_pos = self.export_button.mapToGlobal(self.export_button.rect().topLeft())

            # تحديد عرض القائمة مع تكبير ربع درجة
            button_width = self.export_button.width()
            export_menu.setFixedWidth(max(button_width, 190))

            # ترحيل القائمة نصف درجة لليسار
            button_pos.setX(button_pos.x() - 10)

            # حساب ارتفاع القائمة لرفعها فوق الزر
            menu_height = export_menu.sizeHint().height()
            button_pos.setY(button_pos.y() - menu_height)

            # عرض القائمة في الموضع المحدد
            export_menu.exec_(button_pos)

        # ربط الزر بالدالة المخصصة بدلاً من setMenu
        self.export_button.clicked.connect(show_export_menu)

        self.statistics_button = QPushButton("📊 الإحصائيات")
        self.style_advanced_button(self.statistics_button, 'rose')  # وردي للإحصائيات
        self.statistics_button.clicked.connect(self.show_statistics)
        self.statistics_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        # زر إخفاء/إظهار الأعمدة
        self.columns_visibility_button = QPushButton("👁️ إدارة الأعمدة")
        self.style_advanced_button(self.columns_visibility_button, 'indigo')
        self.columns_visibility_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        # إنشاء قائمة إدارة الأعمدة
        self.create_columns_visibility_menu()

        # إجمالي المصروفات مطور ليتشابه مع الفواتير مع حفظ المقاسات والخط الداخلي
        self.total_label = QLabel("إجمالي المصروفات: 0.00")
        self.total_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                font-size: 16px;
                font-weight: bold;
                font-family: 'Segoe UI', 'Roboto', 'Helvetica Neue', 'Arial', sans-serif;
                padding: 8px 16px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #7f1d1d,
                    stop:0.1 #991b1b,
                    stop:0.9 #b91c1c,
                    stop:1 #ef4444);
                border: 5px solid #ef4444;
                border-radius: 20px;
                min-height: 34px;
                max-height: 38px;
                text-shadow: 3px 3px 6px rgba(0, 0, 0, 0.9),
                           2px 2px 4px rgba(0, 0, 0, 0.7),
                           1px 1px 2px rgba(0, 0, 0, 0.5);
                box-shadow: 0 8px 20px rgba(239, 68, 68, 0.6),
                           inset 0 3px 0 rgba(255, 255, 255, 0.4),
                           inset 0 -3px 0 rgba(0, 0, 0, 0.4),
                           0 0 25px rgba(239, 68, 68, 0.6),
                           0 0 40px rgba(255, 255, 255, 0.1);
                letter-spacing: 0.5px;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
            }
        """)
        self.total_label.setAlignment(Qt.AlignCenter)

        # إضافة الأزرار للتخطيط
        actions_layout.addWidget(self.add_button)
        actions_layout.addWidget(self.edit_button)
        actions_layout.addWidget(self.delete_button)
        actions_layout.addWidget(self.refresh_button)
        actions_layout.addWidget(self.view_button)
        actions_layout.addWidget(self.export_button)

        actions_layout.addWidget(self.statistics_button)
        actions_layout.addWidget(self.columns_visibility_button)
        actions_layout.addWidget(self.total_label)

        # تعيين التخطيط للإطار السفلي
        bottom_frame.setLayout(bottom_container)

        # تجميع التخطيط النهائي
        main_layout.addWidget(bottom_frame)

        self.setLayout(main_layout)

    def create_advanced_expenses_table(self):
        """إنشاء جدول المصروفات المتطور والمحسن مطابق للموردين"""
        styled_table = StyledTable()
        self.expenses_table = styled_table.table
        self.expenses_table.setColumnCount(7)
        # عناوين محسنة مع أيقونات متطورة وجذابة مطابقة للفواتير
        headers = [
            "🔢 ID",
            "📝 الوصف",
            "💰 المبلغ",
            "📅 التاريخ",
            "📂 الفئة",
            "🏢 المورد",
            "📋 ملاحظات"
        ]
        self.expenses_table.setHorizontalHeaderLabels(headers)

        # إعدادات عرض الأعمدة مع التكيف التلقائي مطابقة للعملاء
        header = self.expenses_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.Fixed)  # ID
        header.setSectionResizeMode(1, QHeaderView.Fixed)  # الوصف
        header.setSectionResizeMode(2, QHeaderView.Stretch)  # الفئة
        header.setSectionResizeMode(3, QHeaderView.Stretch)  # المبلغ
        header.setSectionResizeMode(4, QHeaderView.Stretch)  # التاريخ
        header.setSectionResizeMode(5, QHeaderView.Stretch)  # طريقة الدفع
        header.setSectionResizeMode(6, QHeaderView.Stretch)  # الملاحظات

        # تحديد عرض الأعمدة الثابتة مطابق للعملاء
        self.expenses_table.setColumnWidth(0, 120)  # ID
        self.expenses_table.setColumnWidth(1, 300)  # الوصف

        self.expenses_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.expenses_table.setSelectionMode(QTableWidget.ExtendedSelection)
        self.expenses_table.setEditTriggers(QTableWidget.NoEditTriggers)
        self.expenses_table.setAlternatingRowColors(True)

        # إعداد التحديد المتعدد
        self.init_multi_selection(self.expenses_table)

        # إخفاء شريط التمرير المرئي مع الحفاظ على التمرير بالماوس
        self.expenses_table.setVerticalScrollBarPolicy(Qt.ScrollBarAlwaysOff)

        # ضبط إعدادات التمرير للتحكم الدقيق
        try:
            scrollbar = self.expenses_table.verticalScrollBar()
            if scrollbar:
                scrollbar.setSingleStep(50)  # ارتفاع الصف الواحد
                scrollbar.setPageStep(200)   # 4 صفوف للصفحة
        except Exception:
            pass

        # تطبيق تصميم متطور جداً وأنيق للجدول مطابق للفواتير
        self.expenses_table.setStyleSheet("""
            QTableWidget {
                gridline-color: rgba(44, 62, 80, 0.2);
                background: #e2e8f0;
                border: 3px solid #000000;
                border-radius: 20px;
                font-family: 'Segoe UI', 'Roboto', 'Helvetica Neue', 'Arial', sans-serif;
                font-size: 14px;
                font-weight: 500;
                selection-background-color: rgba(102, 126, 234, 0.15);
                alternate-background-color: rgba(203, 213, 225, 0.3);
                outline: none;
                padding: 5px;
            }

            QTableWidget::item {
                padding: 10px 12px;
                border: 2px solid rgba(102, 126, 234, 0.12);
                border-left: 5px solid rgba(102, 126, 234, 0.5);
                border-right: 5px solid rgba(102, 126, 234, 0.5);
                border-top: 2px solid rgba(102, 126, 234, 0.2);
                border-bottom: 3px solid rgba(102, 126, 234, 0.3);
                text-align: center;
                min-height: 30px;
                max-height: 45px;
                font-weight: 600;
                font-size: 14px;
                border-radius: 15px;
                margin: 3px;
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgba(248, 250, 252, 1.0),
                    stop:0.3 rgba(241, 245, 249, 1.0),
                    stop:0.7 rgba(226, 232, 240, 1.0),
                    stop:1 rgba(203, 213, 225, 1.0));
                /* color: #1e293b; */ /* تم إزالة اللون الثابت للسماح بألوان مخصصة */
                font-family: 'Segoe UI', 'Roboto', 'Arial', sans-serif;
            }

            QTableWidget::item:selected {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(79, 70, 229, 0.9),
                    stop:0.2 rgba(99, 102, 241, 0.9),
                    stop:0.4 rgba(129, 140, 248, 0.9),
                    stop:0.6 rgba(165, 180, 252, 0.9),
                    stop:0.8 rgba(196, 181, 253, 0.9),
                    stop:1 rgba(221, 214, 254, 0.9)) !important;
                color: white !important;
                border: 4px solid rgba(255, 255, 255, 0.9) !important;
                border-left: 6px solid #fbbf24 !important;
                border-right: 6px solid #fbbf24 !important;
                border-top: 3px solid rgba(255, 255, 255, 0.7) !important;
                border-bottom: 5px solid rgba(255, 255, 255, 0.8) !important;
                border-radius: 18px !important;
                font-weight: bold !important;
                font-size: 15px !important;
                text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3) !important;
            }

            QTableWidget::item:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgba(102, 126, 234, 0.15),
                    stop:0.3 rgba(129, 140, 248, 0.2),
                    stop:0.7 rgba(165, 180, 252, 0.25),
                    stop:1 rgba(196, 181, 253, 0.3)) !important;
                border: 3px solid rgba(102, 126, 234, 0.7) !important;
                border-left: 6px solid #06b6d4 !important;
                border-right: 6px solid #06b6d4 !important;
                border-top: 2px solid rgba(102, 126, 234, 0.5) !important;
                border-bottom: 4px solid rgba(102, 126, 234, 0.6) !important;
                border-radius: 16px !important;
                color: #0f172a !important;
                font-weight: bold !important;
                font-size: 14px !important;
                transform: translateY(-1px) !important;
            }

            QTableWidget::item:selected:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(79, 172, 254, 0.9),
                    stop:0.5 rgba(0, 242, 254, 0.9),
                    stop:1 rgba(102, 126, 234, 0.9)) !important;
                border: 4px solid rgba(255, 255, 255, 0.9) !important;
                border-left: 6px solid #ffd700 !important;
                border-right: 6px solid #ffd700 !important;
                box-shadow: 0px 8px 20px rgba(102, 126, 234, 0.5) !important;
            }
        """)

        # تطبيق تنسيق العناوين المتطور مطابق للفواتير
        header = self.expenses_table.horizontalHeader()
        header.setStyleSheet("")  # إزالة أي تنسيق سابق

        # ألوان مشابهة للعنوان الرئيسي لكن مطورة وهادئة مع خط بولد قوي
        new_header_style = """
            QHeaderView::section {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #1E40AF,
                    stop:0.3 #1D4ED8, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                    stop:0.6 #6366F1, stop:0.7 #7C3AED, stop:0.8 #6D28D9,
                    stop:0.9 #5B21B6, stop:1 #4C1D95) !important;
                color: #FFFFFF !important;
                padding: 12px 16px !important;
                margin: 0px !important;
                font-weight: 900 !important;
                font-size: 16px !important;
                font-family: 'Segoe UI', 'Roboto', 'Helvetica Neue', 'Arial', sans-serif !important;
                border: 3px solid rgba(255, 255, 255, 0.6) !important;
                border-bottom: 4px solid rgba(255, 255, 255, 0.8) !important;
                border-left: 2px solid rgba(255, 255, 255, 0.4) !important;
                border-right: 2px solid rgba(255, 255, 255, 0.4) !important;
                border-radius: 12px 12px 0 0 !important;
                text-align: center !important;
                vertical-align: middle !important;
                line-height: 31px !important;
                letter-spacing: 1.3px !important;
                position: relative !important;
                text-transform: uppercase !important;
            }
            QHeaderView::section:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #1E293B, stop:0.1 #334155, stop:0.2 #475569,
                    stop:0.3 #64748B, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                    stop:0.6 #6366F1, stop:0.7 #7C3AED, stop:0.8 #6D28D9,
                    stop:0.9 #5B21B6, stop:1 #4C1D95) !important;
                transform: translateY(-2px) scale(1.02) !important;
                box-shadow: 0 6px 18px rgba(0, 0, 0, 0.5),
                           inset 0 2px 0 rgba(255, 255, 255, 0.4),
                           inset 0 -1px 0 rgba(0, 0, 0, 0.4),
                           0 0 25px rgba(134, 158, 234, 0.4) !important;
                border: 4px solid rgba(255, 255, 255, 0.8) !important;
                border-bottom: 5px solid #d4d4aa !important;
                text-shadow: 2px 2px 5px rgba(0, 0, 0, 0.9),
                           1px 1px 2px rgba(0, 0, 0, 0.7) !important;
                letter-spacing: 1.5px !important;
                font-size: 15px !important;
                color: #ffffff !important;
                font-weight: 900 !important;
            }
            QHeaderView::section:pressed {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #334155,
                    stop:0.3 #475569, stop:0.4 #1E40AF, stop:0.5 #1D4ED8,
                    stop:0.6 #2563EB, stop:0.7 #5B21B6, stop:0.8 #4C1D95,
                    stop:0.9 #3730A3, stop:1 #312E81) !important;
                transform: translateY(1px) scale(0.98) !important;
                box-shadow: inset 0 3px 6px rgba(0, 0, 0, 0.6),
                           0 2px 4px rgba(0, 0, 0, 0.4),
                           inset 0 0 12px rgba(0, 0, 0, 0.4) !important;
                border: 3px solid rgba(255, 255, 255, 0.9) !important;
                border-top: 2px solid rgba(255, 255, 255, 0.6) !important;
                text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.9),
                           0px 0px 1px rgba(0, 0, 0, 0.7) !important;
                letter-spacing: 1.2px !important;
                color: #f0f0f0 !important;
                font-weight: 900 !important;
            }
        """

        # تطبيق التنسيق الجديد
        header.setStyleSheet(new_header_style)

        # تحسين ارتفاع الصفوف مطابق للفواتير
        self.expenses_table.verticalHeader().setDefaultSectionSize(45)
        self.expenses_table.verticalHeader().setVisible(False)

        # تحسين رأس الجدول بشكل احترافي مع تأثيرات
        header.setFixedHeight(55)
        header.setDefaultAlignment(Qt.AlignCenter)
        header.setMinimumSectionSize(120)

        # إضافة العلامة المائية للجدول مطابقة للفواتير
        self.add_watermark_to_expenses_table()

        # إضافة معالج التمرير المخصص (يحاكي سلوك الأسهم)
        def wheelEvent(event):
            try:
                # التمرير العمودي بالماوس
                delta = event.angleDelta().y()

                # تجاهل الحركات الصغيرة جداً
                if abs(delta) < 120:
                    event.accept()
                    return

                # الحصول على شريط التمرير
                scrollbar = self.expenses_table.verticalScrollBar()
                if not scrollbar:
                    event.accept()
                    return

                # محاكاة سلوك الأسهم - خطوة واحدة في كل مرة
                if delta > 0:
                    # التمرير لأعلى - مثل الضغط على السهم العلوي
                    scrollbar.triggerAction(QAbstractSlider.SliderSingleStepSub)
                else:
                    # التمرير لأسفل - مثل الضغط على السهم السفلي
                    scrollbar.triggerAction(QAbstractSlider.SliderSingleStepAdd)

                event.accept()

            except Exception:
                # في حالة الخطأ، استخدم التمرير الافتراضي
                QTableWidget.wheelEvent(self.expenses_table, event)

        self.expenses_table.wheelEvent = wheelEvent

        # ربط خاصية النقر المزدوج للتعديل مطابق للعملاء
        self.expenses_table.cellDoubleClicked.connect(self.on_cell_double_clicked)

    def on_cell_double_clicked(self, row, column):
        """معالج النقر المزدوج على خلية مطابق للعملاء"""
        try:
            self.edit_expense()
        except Exception as e:
            pass  # خطأ في النقر المزدوج

    def add_watermark_to_expenses_table(self):
        """إضافة علامة مائية للجدول مطابقة للعملاء"""
        def paint_watermark(painter, rect):
            painter.save()
            painter.setOpacity(0.08)
            painter.setPen(QColor(30, 41, 59))
            font = QFont("Segoe UI", 180, QFont.Bold)
            painter.setFont(font)
            center_rect = rect.adjusted(0, rect.height() // 4, 0, -rect.height() // 4)
            painter.drawText(center_rect, Qt.AlignCenter, "Smart Finish")
            painter.restore()

        original_paint = self.expenses_table.paintEvent
        def new_paint_event(event):
            try:
                original_paint(event)
                painter = QPainter(self.expenses_table.viewport())
                paint_watermark(painter, self.expenses_table.viewport().rect())
                painter.end()
            except Exception:
                pass

        self.expenses_table.paintEvent = new_paint_event
        # إجبار إعادة الرسم
        self.expenses_table.viewport().update()
        self.expenses_table.repaint()

    def refresh_data(self):
        """تحديث بيانات المصروفات في الجدول مع حماية من الضغط المتكرر"""
        try:
            # منع الضغط المتكرر على الزر
            if hasattr(self, '_is_refreshing') and self._is_refreshing:
                return

            # تعيين حالة التحديث
            self._is_refreshing = True

            # تعطيل زر التحديث مؤقتاً
            if hasattr(self, 'refresh_button'):
                self.refresh_button.setEnabled(False)
                self.refresh_button.setText("🔄 جاري التحديث...")

            # الحصول على المصروفات مع تحديد حد أقصى لتحسين الأداء
            expenses = self.session.query(Expense).limit(1000).all()
            self.populate_table(expenses)
            self.update_total(expenses)

        except Exception as e:
            print(f"خطأ في تحديث بيانات المصروفات: {str(e)}")
        finally:
            # إعادة تفعيل زر التحديث وإعادة تعيين النص
            if hasattr(self, 'refresh_button'):
                self.refresh_button.setEnabled(True)
                self.refresh_button.setText("🔄 تحديث")

            # إعادة تعيين حالة التحديث
            self._is_refreshing = False

    def populate_table(self, expenses):
        """ملء جدول المصروفات بالبيانات مع أيقونات متطورة مطابقة للفواتير"""
        try:
            # تعطيل تحديث الجدول مؤقتًا لتحسين الأداء
            self.expenses_table.setUpdatesEnabled(False)

            # مسح الجدول
            self.expenses_table.setRowCount(0)

            # إضافة الصفوف مع تنسيق محسن وأيقونات متطورة
            for row, expense in enumerate(expenses):
                try:
                    self.expenses_table.insertRow(row)

                    # 1. الرقم التسلسلي مع أيقونة ثابتة مطابق للعملاء
                    # المصروفات دائماً تستخدم أيقونة 🔢 لأنها لا تحتوي على رصيد
                    id_item = QTableWidgetItem(f"🔢 {expense.id}")
                    id_item.setTextAlignment(Qt.AlignCenter)
                    id_item.setForeground(QColor("#000000"))  # لون أسود للرقم مطابق للعملاء
                    self.expenses_table.setItem(row, 0, id_item)

                    # 2. عنوان المصروف مع أيقونة - مطابق للعملاء
                    def create_item(icon, text, default="No Data"):
                        display_text = text if text and text.strip() else default
                        item = QTableWidgetItem(f"{icon} {display_text}")
                        item.setTextAlignment(Qt.AlignCenter)
                        if display_text == default:
                            item.setForeground(QColor("#ef4444"))
                        return item

                    self.expenses_table.setItem(row, 1, create_item("📝", expense.title))

                    # 3. المبلغ مع تنسيق مالي - مطابق للعملاء
                    try:
                        if expense.amount and expense.amount > 0:
                            amount_formatted = f"{int(expense.amount):,}".replace(',', '٬')
                            amount_display = f"{amount_formatted} جنيه"
                        else:
                            amount_display = None
                    except Exception:
                        amount_display = None

                    self.expenses_table.setItem(row, 2, create_item("💰", amount_display))

                    # 4. التاريخ - مطابق للعملاء
                    try:
                        if expense.date:
                            date_display = expense.date.strftime('%Y-%m-%d')
                        else:
                            date_display = None
                    except Exception:
                        date_display = None

                    self.expenses_table.setItem(row, 3, create_item("📅", date_display))

                    # 5. الفئة مع أيقونة مناسبة
                    category = expense.category or "غير محدد"
                    category_icons = {
                        "مكتب": "🏢",
                        "سفر": "✈️",
                        "طعام": "🍽️",
                        "مواصلات": "🚗",
                        "اتصالات": "📞",
                        "كهرباء": "⚡",
                        "مياه": "💧",
                        "إيجار": "🏠",
                        "صيانة": "🔧",
                        "تسويق": "📢",
                        "أخرى": "📦"
                    }
                    category_icon = category_icons.get(category, "📂")
                    # استخدام create_item للتوحيد مع العملاء
                    self.expenses_table.setItem(row, 4, create_item("📂", category if category != "غير محدد" else None))

                    # 6. المورد - مطابق للعملاء
                    try:
                        supplier_name = expense.supplier.name if expense.supplier else None
                    except Exception:
                        supplier_name = None

                    self.expenses_table.setItem(row, 5, create_item("🏢", supplier_name))

                    # 7. الملاحظات - مطابق للعملاء
                    notes = expense.notes or None
                    self.expenses_table.setItem(row, 6, create_item("📋", notes))

                except Exception as row_error:
                    # تجاهل الصف الذي به خطأ والاستمرار في العملية
                    print(f"خطأ في الصف {row}: {str(row_error)}")
                    continue

            # إعادة تمكين تحديث الجدول
            self.expenses_table.setUpdatesEnabled(True)

        except Exception as e:
            # إعادة تمكين تحديث الجدول في حالة حدوث خطأ
            self.expenses_table.setUpdatesEnabled(True)
            show_error_message("خطأ", f"حدث خطأ أثناء تحديث جدول المصروفات: {str(e)}")

    def update_total(self, expenses):
        """تحديث إجمالي المصروفات"""
        total = sum(expense.amount for expense in expenses)
        self.total_label.setText(f"إجمالي المصروفات: {format_currency(total)}")

    def filter_expenses(self):
        """تصفية المصروفات بناءً على نص البحث والفئة"""
        search_text = self.search_edit.text().strip().lower()
        category = getattr(self, 'current_filter_value', None)

        # بناء الاستعلام
        query = self.session.query(Expense)

        # تطبيق تصفية النص
        if search_text:
            query = query.filter(
                Expense.title.like(f"%{search_text}%") |
                Expense.notes.like(f"%{search_text}%")
            )

        # تطبيق تصفية الفئة
        if category is not None:
            query = query.filter(Expense.category == category)

        # تنفيذ الاستعلام
        expenses = query.all()

        # تحديث الجدول والإجمالي
        self.populate_table(expenses)
        self.update_total(expenses)

    def add_expense(self):
        """إضافة مصروف جديد"""
        dialog = ExpenseDialog(self, session=self.session)
        if dialog.exec_() == QDialog.Accepted:
            data = dialog.get_data()
            if data:
                # إنشاء مصروف جديد في قاعدة البيانات
                expense = Expense(**data)
                self.session.add(expense)
                self.session.commit()

                show_info_message("تم", "تمت إضافة المصروف بنجاح")
                self.refresh_data()

    def edit_expense(self):
        """تعديل بيانات مصروف"""
        from utils import safe_edit_item
        from database import Expense
        safe_edit_item(self, self.expenses_table, Expense, ExpenseDialog, self.session, "مصروف")

    def delete_selected_items(self):
        """حذف المصروفات المحددة"""
        try:
            self.update_selected_items()
            if not self.selected_items:
                return

            count = len(self.selected_items)
            if count == 1:
                self.delete_expense()
            else:
                if show_confirmation_message("تأكيد الحذف", f"هل تريد حذف {count} مصروف؟"):
                    for item_id in self.selected_items:
                        expense = self.session.query(Expense).get(item_id)
                        if expense:
                            self.session.delete(expense)
                    self.session.commit()
                    self.load_expenses()
        except Exception as e:
            print(f"خطأ في حذف المصروفات: {e}")

    def show_context_menu(self, position):
        """عرض القائمة السياقية للمصروفات"""
        try:
            menu = QMenu(self)

            single_actions = [
                ("✏️ تعديل", self.edit_expense),
                ("👁️ عرض التفاصيل", self.view_expense_details),
                ("🗑️ حذف", self.delete_expense)
            ]

            multi_actions = [
                ("🗑️ حذف {count} مصروف", self.delete_selected_items)
            ]

            self.create_context_menu_actions(menu, single_actions, multi_actions)
            menu.exec_(self.expenses_table.mapToGlobal(position))
        except Exception as e:
            print(f"خطأ في القائمة السياقية: {e}")

    def delete_expense(self):
        """حذف مصروف مع نافذة تأكيد متطورة"""
        try:
            selected_row = self.expenses_table.currentRow()
            if selected_row < 0:
                self.show_warning_message("الرجاء اختيار مصروف من القائمة")
                return

            # استخراج الرقم من النص (إزالة الأيقونات)
            id_text = self.expenses_table.item(selected_row, 0).text()
            # إزالة الأيقونات والمسافات والحصول على الرقم فقط
            expense_id = int(''.join(filter(str.isdigit, id_text)))
            expense = self.session.query(Expense).get(expense_id)

            if not expense:
                self.show_error_message("لم يتم العثور على المصروف")
                return

            # إنشاء نافذة حذف متطورة مشابهة للعملاء
            dialog = DeleteExpenseDialog(self, expense)
            if dialog.exec_() == QDialog.Accepted:
                try:
                    # حذف المصروف من قاعدة البيانات
                    self.session.delete(expense)
                    self.session.commit()

                    # إظهار رسالة نجاح متطورة
                    self.show_success_message(f"تم حذف المصروف '{expense.title}' بنجاح")

                    # تحديث الجدول
                    self.refresh_data()

                except Exception as e:
                    self.session.rollback()
                    self.show_error_message(f"فشل في حذف المصروف: {str(e)}")
        except Exception as e:
            self.show_error_message(f"خطأ في حذف المصروف: {str(e)}")

    def show_warning_message(self, message):
        """إظهار رسالة تحذير متطورة مشابهة لنوافذ البرنامج"""
        dialog = WarningDialog(self, message)
        dialog.exec_()

    def show_success_message(self, message):
        """إظهار رسالة نجاح متطورة"""
        from PyQt5.QtWidgets import QMessageBox
        msg = QMessageBox(self)
        msg.setIcon(QMessageBox.Information)
        msg.setWindowTitle("✅ نجح")
        msg.setText(message)
        msg.exec_()

    def show_error_message(self, message):
        """إظهار رسالة خطأ متطورة"""
        from PyQt5.QtWidgets import QMessageBox
        msg = QMessageBox(self)
        msg.setIcon(QMessageBox.Critical)
        msg.setWindowTitle("❌ خطأ")
        msg.setText(message)
        msg.exec_()

    def view_expense(self):
        """عرض تفاصيل المصروف في نافذة متطورة"""
        try:
            selected_row = self.expenses_table.currentRow()
            if selected_row < 0:
                show_error_message("خطأ", "الرجاء اختيار مصروف من القائمة")
                return

            # استخراج معرف المصروف مع التعامل مع الرموز التعبيرية
            id_text = self.expenses_table.item(selected_row, 0).text()
            import re
            expense_id = int(re.sub(r'[^\d]', '', id_text))

            expense = self.session.query(Expense).get(expense_id)

            if not expense:
                show_error_message("خطأ", "لم يتم العثور على المصروف")
                return

            # إنشاء نافذة المعلومات المتطورة
            info_dialog = ExpenseInfoDialog(self, expense)
            info_dialog.exec_()
        except Exception as e:
            print(f"خطأ في عرض تفاصيل المصروف: {str(e)}")
            show_error_message("خطأ", f"حدث خطأ في عرض تفاصيل المصروف: {str(e)}")

    def create_custom_status_filter(self):
        """إنشاء قائمة تصفية مخصصة ومطورة مطابقة للفواتير"""
        # إنشاء إطار للقائمة المخصصة
        self.status_filter_frame = QFrame()
        self.status_filter_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.9),
                    stop:0.2 rgba(248, 250, 252, 0.95),
                    stop:0.4 rgba(241, 245, 249, 0.9),
                    stop:0.6 rgba(248, 250, 252, 0.95),
                    stop:0.8 rgba(255, 255, 255, 0.9),
                    stop:1 rgba(226, 232, 240, 0.85));
                border: 3px solid rgba(96, 165, 250, 0.8);
                border-radius: 15px;
                padding: 6px 15px;
                min-width: 500px;
                max-width: 500px;
                min-height: 33px;
                max-height: 37px;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
                transition: all 0.3s ease;
                cursor: pointer;
            }
            QFrame:hover {
                border: 4px solid rgba(96, 165, 250, 0.9);
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(250, 251, 255, 0.95),
                    stop:0.2 rgba(241, 245, 249, 0.9),
                    stop:0.4 rgba(226, 232, 240, 0.85),
                    stop:0.6 rgba(241, 245, 249, 0.9),
                    stop:0.8 rgba(250, 251, 255, 0.95),
                    stop:1 rgba(255, 255, 255, 0.9));
                transform: translateY(-1px);
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
            }
            QFrame:focus {
                border: 4px solid rgba(96, 165, 250, 0.95);
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(240, 249, 255, 0.95),
                    stop:0.2 rgba(224, 242, 254, 0.9),
                    stop:0.4 rgba(186, 230, 253, 0.85),
                    stop:0.6 rgba(224, 242, 254, 0.9),
                    stop:0.8 rgba(240, 249, 255, 0.95),
                    stop:1 rgba(255, 255, 255, 0.9));
                box-shadow: 0 4px 12px rgba(96, 165, 250, 0.3);
            }
        """)

        # إنشاء تخطيط أفقي للإطار
        filter_layout = QHBoxLayout(self.status_filter_frame)
        filter_layout.setContentsMargins(5, 0, 5, 0)
        filter_layout.setSpacing(8)

        # سهم يسار (مشابه للسهم الأيمن)
        self.left_arrow = QPushButton("▼")
        self.left_arrow.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(37, 99, 235, 0.8),
                    stop:0.2 rgba(59, 130, 246, 0.7),
                    stop:0.4 rgba(96, 165, 250, 0.6),
                    stop:0.6 rgba(139, 92, 246, 0.7),
                    stop:0.8 rgba(124, 58, 237, 0.8),
                    stop:1 rgba(109, 40, 217, 0.7));
                border: 2px solid rgba(96, 165, 250, 0.6);
                border-radius: 12px;
                color: #ffffff;
                font-size: 16px;
                font-weight: bold;
                min-width: 35px;
                max-width: 35px;
                min-height: 28px;
                max-height: 28px;
                padding: 0px;
                box-shadow:
                    0 2px 6px rgba(0, 0, 0, 0.15),
                    inset 0 1px 1px rgba(255, 255, 255, 0.2);
                transition: all 0.3s ease;
                text-shadow: 0 1px 1px rgba(0, 0, 0, 0.3);
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(59, 130, 246, 0.9),
                    stop:0.2 rgba(96, 165, 250, 0.8),
                    stop:0.4 rgba(139, 92, 246, 0.7),
                    stop:0.6 rgba(124, 58, 237, 0.8),
                    stop:0.8 rgba(109, 40, 217, 0.9),
                    stop:1 rgba(91, 33, 182, 0.8));
                border: 3px solid rgba(96, 165, 250, 0.8);
                transform: translateY(-1px) scale(1.05);
                box-shadow:
                    0 4px 12px rgba(0, 0, 0, 0.25),
                    0 0 15px rgba(96, 165, 250, 0.4),
                    inset 0 1px 2px rgba(255, 255, 255, 0.3);
            }
            QPushButton:pressed {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(29, 78, 216, 0.9),
                    stop:0.2 rgba(37, 99, 235, 0.8),
                    stop:0.4 rgba(59, 130, 246, 0.7),
                    stop:0.6 rgba(96, 165, 250, 0.8),
                    stop:0.8 rgba(124, 58, 237, 0.9),
                    stop:1 rgba(109, 40, 217, 0.8));
                border: 3px solid rgba(96, 165, 250, 0.9);
                transform: translateY(1px) scale(0.98);
                box-shadow:
                    0 0 15px rgba(96, 165, 250, 0.4),
                    0 2px 8px rgba(0, 0, 0, 0.4),
                    inset 0 1px 2px rgba(255, 255, 255, 0.3);
            }
        """)

        # النص الحالي
        self.current_filter_label = QLabel("جميع الفئات")
        self.current_filter_label.setAlignment(Qt.AlignCenter)  # توسيط النص أفقياً وعمودياً
        self.current_filter_label.setStyleSheet("""
            QLabel {
                color: #1f2937;
                font-size: 16px;
                font-weight: 900;
                background: transparent;
                border: none;
                padding: 0px 12px;
                text-align: center;
                max-width: 435px;
                min-width: 435px;
                text-shadow: 0 1px 1px rgba(255, 255, 255, 0.5);
                cursor: pointer;
            }
        """)

        # زر القائمة (سهم يمين)
        self.filter_menu_button = QPushButton("▼")
        self.filter_menu_button.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(37, 99, 235, 0.8),
                    stop:0.2 rgba(59, 130, 246, 0.7),
                    stop:0.4 rgba(96, 165, 250, 0.6),
                    stop:0.6 rgba(139, 92, 246, 0.7),
                    stop:0.8 rgba(124, 58, 237, 0.8),
                    stop:1 rgba(109, 40, 217, 0.7));
                border: 2px solid rgba(96, 165, 250, 0.6);
                border-radius: 12px;
                color: #ffffff;
                font-size: 16px;
                font-weight: bold;
                min-width: 35px;
                max-width: 35px;
                min-height: 28px;
                max-height: 28px;
                padding: 0px;
                box-shadow:
                    0 2px 6px rgba(0, 0, 0, 0.15),
                    inset 0 1px 1px rgba(255, 255, 255, 0.2);
                transition: all 0.3s ease;
                text-shadow: 0 1px 1px rgba(0, 0, 0, 0.3);
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(59, 130, 246, 0.9),
                    stop:0.2 rgba(96, 165, 250, 0.8),
                    stop:0.4 rgba(139, 92, 246, 0.7),
                    stop:0.6 rgba(124, 58, 237, 0.8),
                    stop:0.8 rgba(109, 40, 217, 0.9),
                    stop:1 rgba(91, 33, 182, 0.8));
                border: 3px solid rgba(96, 165, 250, 0.8);
                transform: translateY(-1px) scale(1.05);
                box-shadow:
                    0 4px 12px rgba(0, 0, 0, 0.25),
                    0 0 15px rgba(96, 165, 250, 0.4),
                    inset 0 1px 2px rgba(255, 255, 255, 0.3);
            }
            QPushButton:pressed {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(29, 78, 216, 0.9),
                    stop:0.2 rgba(37, 99, 235, 0.8),
                    stop:0.4 rgba(59, 130, 246, 0.7),
                    stop:0.6 rgba(96, 165, 250, 0.8),
                    stop:0.8 rgba(124, 58, 237, 0.9),
                    stop:1 rgba(109, 40, 217, 0.8));
                border: 3px solid rgba(96, 165, 250, 0.9);
                transform: translateY(1px) scale(0.98);
                box-shadow:
                    0 0 15px rgba(96, 165, 250, 0.4),
                    0 2px 8px rgba(0, 0, 0, 0.4),
                    inset 0 1px 2px rgba(255, 255, 255, 0.3);
            }
        """)

        # إضافة العناصر للتخطيط - سهم يسار، النص في المنتصف، سهم يمين
        filter_layout.addWidget(self.left_arrow, 0)
        filter_layout.addWidget(self.current_filter_label, 1)
        filter_layout.addWidget(self.filter_menu_button, 0)

        # إنشاء القائمة المنسدلة
        self.filter_menu = QMenu(self)
        self.filter_menu.setStyleSheet("""
            QMenu {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.9),
                    stop:0.2 rgba(248, 250, 252, 0.95),
                    stop:0.4 rgba(241, 245, 249, 0.9),
                    stop:0.6 rgba(248, 250, 252, 0.95),
                    stop:0.8 rgba(255, 255, 255, 0.9),
                    stop:1 rgba(226, 232, 240, 0.85));
                border: 3px solid rgba(96, 165, 250, 0.8);
                border-radius: 4px;
                padding: 8px;
                color: #1f2937;
                font-weight: 900;
                font-size: 16px;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
                min-width: 515px;
                max-width: 515px;
            }
            QMenu::item {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.9),
                    stop:0.2 rgba(248, 250, 252, 0.95),
                    stop:0.4 rgba(241, 245, 249, 0.9),
                    stop:0.6 rgba(248, 250, 252, 0.95),
                    stop:0.8 rgba(255, 255, 255, 0.9),
                    stop:1 rgba(226, 232, 240, 0.85));
                border: 2px solid rgba(96, 165, 250, 0.3);
                padding: 12px 0px;
                border-radius: 15px;
                margin: 3px;
                min-height: 32px;
                max-height: 32px;
                max-width: 495px;
                min-width: 495px;
                color: #1f2937;
                font-weight: 900;
                font-size: 17px;
                font-family: 'Courier New', 'Consolas', monospace;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
                text-align: center;
                font-smooth: never;
                -webkit-font-smoothing: none;
                -moz-osx-font-smoothing: auto;
                text-rendering: optimizeSpeed;
            }
            QMenu::item:selected {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(96, 165, 250, 0.4),
                    stop:0.2 rgba(139, 92, 246, 0.3),
                    stop:0.4 rgba(124, 58, 237, 0.25),
                    stop:0.6 rgba(139, 92, 246, 0.3),
                    stop:0.8 rgba(96, 165, 250, 0.4),
                    stop:1 rgba(59, 130, 246, 0.35));
                border: 3px solid rgba(96, 165, 250, 0.7);
                color: #1f2937;
                font-weight: 900;
                font-size: 17px;
                font-family: 'Courier New', 'Consolas', monospace;
                font-smooth: never;
                -webkit-font-smoothing: none;
                -moz-osx-font-smoothing: auto;
                text-rendering: optimizeSpeed;
                box-shadow:
                    0 4px 12px rgba(96, 165, 250, 0.3),
                    0 0 15px rgba(96, 165, 250, 0.2),
                    inset 0 1px 2px rgba(255, 255, 255, 0.5);
                transform: scale(1.02);
            }
            QMenu::item:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(250, 251, 255, 0.95),
                    stop:0.2 rgba(241, 245, 249, 0.9),
                    stop:0.4 rgba(226, 232, 240, 0.85),
                    stop:0.6 rgba(241, 245, 249, 0.9),
                    stop:0.8 rgba(250, 251, 255, 0.95),
                    stop:1 rgba(255, 255, 255, 0.9));
                border: 4px solid rgba(96, 165, 250, 0.9);
                color: #1f2937;
                font-weight: 900;
                font-size: 17px;
                font-family: 'Courier New', 'Consolas', monospace;
                font-smooth: never;
                -webkit-font-smoothing: none;
                -moz-osx-font-smoothing: auto;
                text-rendering: optimizeSpeed;
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
                transform: translateY(-1px);
            }
        """)

        # إضافة خيارات التصفية مع أيقونات مطابقة للعملاء
        filter_options = [
            ("جميع الفئات", None),
            ("🧱 مواد خام", "مواد خام"),
            ("🔧 أدوات", "أدوات"),
            ("⚙️ صيانة", "صيانة"),
            ("🏠 إيجار", "إيجار"),
            ("💡 مرافق", "مرافق"),
            ("💰 رواتب", "رواتب"),
            ("📢 تسويق", "تسويق"),
            ("🚚 نقل", "نقل"),
            ("📦 أخرى", "أخرى")
        ]

        for text, value in filter_options:
            # إنشاء عنصر مع توسيط النص المثالي
            centered_text = f"{text:^35}"  # توسيط النص في مساحة 35 حرف للعرض الجديد
            action = QAction(centered_text, self)
            action.setData(value)
            action.triggered.connect(lambda checked, v=value, t=text: self.set_filter(v, t))
            self.filter_menu.addAction(action)

        # ربط الأزرار بالقائمة
        self.filter_menu_button.clicked.connect(self.show_filter_menu)
        self.left_arrow.clicked.connect(self.show_filter_menu)

        # إضافة ميزة الضغط على أي مكان في الإطار
        self.status_filter_frame.mousePressEvent = self.frame_mouse_press_event
        self.current_filter_label.mousePressEvent = self.frame_mouse_press_event

    def show_filter_menu(self):
        """عرض القائمة المنسدلة للتصفية"""
        # تحديد موقع القائمة تحت الإطار
        button_rect = self.status_filter_frame.geometry()
        menu_pos = self.status_filter_frame.mapToGlobal(button_rect.bottomLeft())
        menu_pos.setY(menu_pos.y() + 5)  # إضافة مسافة صغيرة

        self.filter_menu.exec_(menu_pos)

    def frame_mouse_press_event(self, event):
        """التعامل مع الضغط على الإطار"""
        if event.button() == Qt.LeftButton:
            self.show_filter_menu()

    def set_filter(self, filter_value, filter_text):
        """تعيين التصفية وتحديث النص"""
        self.current_filter_label.setText(filter_text)
        self.current_filter_value = filter_value
        self.filter_expenses()



    def show_statistics(self):
        """عرض نافذة إحصائيات المصروفات"""
        try:
            dialog = ExpensesStatisticsDialog(self.session, self)
            dialog.exec_()
        except Exception as e:
            print(f"خطأ في عرض إحصائيات المصروفات: {e}")
            QMessageBox.critical(self, "خطأ", f"حدث خطأ في عرض الإحصائيات: {str(e)}")

    def create_columns_visibility_menu(self):
        """إنشاء قائمة إدارة إخفاء/إظهار الأعمدة"""
        # إنشاء قائمة إدارة الأعمدة
        self.columns_menu = QMenu(self)

        # تحديد عرض القائمة مع نفس ألوان وخلفية نافذة الإحصائيات
        self.columns_menu.setStyleSheet("""
            QMenu {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #334155,
                    stop:0.3 #475569, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                    stop:0.6 #60A5FA, stop:0.7 #8B5CF6, stop:0.8 #7C3AED,
                    stop:0.9 #6D28D9, stop:1 #5B21B6);
                border: none;
                border-radius: 8px;
                padding: 4px;
                color: #ffffff;
                font-family: 'Segoe UI', 'Tahoma', 'Arial', sans-serif;
                font-weight: 900;
                font-size: 14px;
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3),
                           0 2px 8px rgba(59, 130, 246, 0.2),
                           inset 0 1px 0 rgba(255, 255, 255, 0.1);
                min-width: 160px;
            }
            QMenu::item {
                background: transparent;
                padding: 6px 25px 6px 15px;
                margin: 1px;
                border: none;
                border-radius: 6px;
                color: #ffffff;
                font-weight: 700;
                font-size: 14px;
                text-align: left;
                min-height: 20px;
                min-width: 140px;
                text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.7);
                white-space: nowrap;
                overflow: visible;
            }
            QMenu::item:selected {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(59, 130, 246, 0.8),
                    stop:0.3 rgba(96, 165, 250, 0.7),
                    stop:0.7 rgba(139, 92, 246, 0.6),
                    stop:1 rgba(124, 58, 237, 0.7));
                color: #ffffff;
                font-weight: 900;
                border: 1px solid rgba(255, 255, 255, 0.4);
                border-radius: 8px;
                box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4),
                           0 0 15px rgba(96, 165, 250, 0.3),
                           inset 0 1px 0 rgba(255, 255, 255, 0.2);
                text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
            }
            QMenu::item:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(96, 165, 250, 0.3),
                    stop:0.5 rgba(139, 92, 246, 0.25),
                    stop:1 rgba(124, 58, 237, 0.3));
                border: 1px solid rgba(255, 255, 255, 0.3);
                border-radius: 8px;
                box-shadow: 0 2px 8px rgba(96, 165, 250, 0.3);
                text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.6);
            }
            QMenu::separator {
                height: 1px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 rgba(255, 255, 255, 0.2),
                    stop:0.5 rgba(96, 165, 250, 0.4),
                    stop:1 rgba(255, 255, 255, 0.2));
                margin: 3px 8px;
                border: none;
                border-radius: 1px;
            }
            QMenu::indicator {
                width: 16px;
                height: 16px;
                margin-left: 0px;
                margin-right: 8px;
                border: 2px solid rgba(255, 255, 255, 0.3);
                border-radius: 3px;
                background: transparent;
                subcontrol-position: right center;
                subcontrol-origin: padding;
            }
            QMenu::indicator:checked {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(34, 197, 94, 0.9),
                    stop:1 rgba(22, 163, 74, 0.9));
                border: 2px solid rgba(34, 197, 94, 0.8);
                image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTIiIGhlaWdodD0iMTIiIHZpZXdCb3g9IjAgMCAxMiAxMiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTEwIDNMNC41IDguNUwyIDYiIHN0cm9rZT0id2hpdGUiIHN0cm9rZS13aWR0aD0iMiIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIi8+Cjwvc3ZnPgo=);
            }
        """)

        # قائمة الأعمدة مع أيقوناتها
        self.column_headers = [
            ("🔢 ID", 0),
            ("📝 الوصف", 1),
            ("💰 المبلغ", 2),
            ("📅 التاريخ", 3),
            ("📂 الفئة", 4),
            ("🏢 المورد", 5),
            ("📋 ملاحظات", 6)
        ]

        # إضافة عناصر القائمة لكل عمود
        for header_text, column_index in self.column_headers:
            action = QAction(header_text, self)
            action.setCheckable(True)
            action.setChecked(True)  # جميع الأعمدة مرئية افتراضياً
            action.triggered.connect(lambda checked, col=column_index: self.toggle_column_visibility(col, checked))
            self.columns_menu.addAction(action)

        # إضافة فاصل
        self.columns_menu.addSeparator()

        # إضافة خيارات إضافية
        show_all_action = QAction("👁️ إظهار جميع الأعمدة", self)
        show_all_action.triggered.connect(self.show_all_columns)
        self.columns_menu.addAction(show_all_action)

        hide_all_action = QAction("🙈 إخفاء جميع الأعمدة", self)
        hide_all_action.triggered.connect(self.hide_all_columns)
        self.columns_menu.addAction(hide_all_action)

        # تخصيص موضع وعرض القائمة
        def show_columns_menu():
            """عرض قائمة إدارة الأعمدة فوق الزر مباشرة بنفس العرض"""
            # الحصول على موضع الزر (فوق الزر)
            button_pos = self.columns_visibility_button.mapToGlobal(self.columns_visibility_button.rect().topLeft())

            # تحديد عرض القائمة لتكون مناسبة للنصوص
            button_width = self.columns_visibility_button.width()
            menu_width = max(button_width, 160)  # عرض أدنى 160 بكسل
            self.columns_menu.setFixedWidth(menu_width)

            # حساب ارتفاع القائمة لرفعها فوق الزر
            menu_height = self.columns_menu.sizeHint().height()
            button_pos.setY(button_pos.y() - menu_height)

            # عرض القائمة في الموضع المحدد
            self.columns_menu.exec_(button_pos)

        # ربط الزر بالدالة المخصصة
        self.columns_visibility_button.clicked.connect(show_columns_menu)

    def toggle_column_visibility(self, column_index, visible):
        """تبديل إظهار/إخفاء عمود محدد"""
        try:
            if hasattr(self, 'expenses_table') and self.expenses_table:
                if visible:
                    self.expenses_table.showColumn(column_index)
                else:
                    self.expenses_table.hideColumn(column_index)

                # تحديث حالة العنصر في القائمة
                for action in self.columns_menu.actions():
                    if action.data() == column_index:
                        action.setChecked(visible)
                        break

        except Exception as e:
            print(f"خطأ في تبديل إظهار العمود: {e}")

    def show_all_columns(self):
        """إظهار جميع الأعمدة"""
        try:
            if hasattr(self, 'expenses_table') and self.expenses_table:
                for i in range(self.expenses_table.columnCount()):
                    self.expenses_table.showColumn(i)

                # تحديث حالة جميع العناصر في القائمة
                for action in self.columns_menu.actions():
                    if action.isCheckable():
                        action.setChecked(True)

        except Exception as e:
            print(f"خطأ في إظهار جميع الأعمدة: {e}")

    def hide_all_columns(self):
        """إخفاء جميع الأعمدة"""
        try:
            if hasattr(self, 'expenses_table') and self.expenses_table:
                for i in range(self.expenses_table.columnCount()):  # إخفاء جميع الأعمدة بما في ذلك ID
                    self.expenses_table.hideColumn(i)

                # تحديث حالة جميع العناصر في القائمة
                for action in self.columns_menu.actions():
                    if action.isCheckable():
                        action.setChecked(False)

        except Exception as e:
            print(f"خطأ في إخفاء الأعمدة: {e}")

    def export_to_excel(self):
        """تصدير المصروفات إلى Excel"""
        try:
            import csv
            from PyQt5.QtWidgets import QFileDialog

            file_path, _ = QFileDialog.getSaveFileName(
                self, "حفظ ملف Excel", "المصروفات.csv", "CSV Files (*.csv)"
            )

            if file_path:
                expenses = self.session.query(Expense).all()

                with open(file_path, 'w', newline='', encoding='utf-8-sig') as csvfile:
                    writer = csv.writer(csvfile)

                    # كتابة رؤوس الأعمدة
                    writer.writerow(['الرقم', 'العنوان', 'المبلغ', 'التاريخ', 'الفئة', 'المورد', 'الملاحظات'])

                    # كتابة البيانات
                    for expense in expenses:
                        date_str = expense.date.strftime("%Y-%m-%d") if expense.date else ""
                        supplier_name = expense.supplier.name if expense.supplier else ""
                        writer.writerow([
                            expense.id,
                            expense.title,
                            expense.amount,
                            date_str,
                            expense.category or "",
                            supplier_name,
                            expense.notes or ""
                        ])

                show_info_message("تم", f"تم تصدير المصروفات بنجاح إلى:\n{file_path}")

        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ في التصدير: {str(e)}")

    def export_to_pdf(self):
        """تصدير المصروفات إلى PDF"""
        try:

            expenses = self.session.query(Expense).all()

            if not expenses:
                show_info_message("تصدير PDF", "لا توجد مصروفات للتصدير")
                return

            # إنشاء محتوى HTML
            html_content = f"""
            <html dir="rtl">
            <head>
                <meta charset="utf-8">
                <title>تقرير المصروفات</title>
                <style>
                    body {{ font-family: Arial, sans-serif; margin: 20px; }}
                    h1 {{ color: #e74c3c; text-align: center; }}
                    table {{ width: 100%; border-collapse: collapse; margin-top: 20px; }}
                    th, td {{ border: 1px solid #ddd; padding: 8px; text-align: right; }}
                    th {{ background-color: #f2f2f2; }}
                    .total {{ font-weight: bold; background-color: #e8f5e8; }}
                </style>
            </head>
            <body>
                <h1>📋 تقرير المصروفات</h1>
                <p><strong>تاريخ التقرير:</strong> {QDate.currentDate().toString('yyyy-MM-dd')}</p>
                <p><strong>وقت الإنشاء:</strong> {QTime.currentTime().toString('hh:mm:ss')}</p>

                <table>
                    <tr>
                        <th>الرقم</th>
                        <th>العنوان</th>
                        <th>المبلغ</th>
                        <th>التاريخ</th>
                        <th>الفئة</th>
                        <th>المورد</th>
                        <th>الملاحظات</th>
                    </tr>
            """

            total_amount = 0
            for expense in expenses:
                date_str = expense.date.strftime("%Y-%m-%d") if expense.date else ""
                supplier_name = expense.supplier.name if expense.supplier else ""
                total_amount += expense.amount

                html_content += f"""
                    <tr>
                        <td>{expense.id}</td>
                        <td>{expense.title}</td>
                        <td>{int(expense.amount):,} جنيه</td>
                        <td>{date_str}</td>
                        <td>{expense.category or ""}</td>
                        <td>{supplier_name}</td>
                        <td>{expense.notes or ""}</td>
                    </tr>
                """

            html_content += f"""
                    <tr class="total">
                        <td colspan="2"><strong>الإجمالي</strong></td>
                        <td><strong>{int(total_amount):,} جنيه</strong></td>
                        <td colspan="4"></td>
                    </tr>
                </table>
            </body>
            </html>
            """

            # حفظ ملف PDF مباشرة
            file_path, _ = QFileDialog.getSaveFileName(
                self, "حفظ تقرير المصروفات", "تقرير_المصروفات.pdf", "PDF Files (*.pdf)"
            )

            if file_path:
                # إنشاء طابعة PDF
                printer = QPrinter(QPrinter.HighResolution)
                printer.setOutputFormat(QPrinter.PdfFormat)
                printer.setOutputFileName(file_path)
                printer.setPageSize(QPrinter.A4)
                printer.setPageMargins(20, 20, 20, 20, QPrinter.Millimeter)

                # إنشاء مستند وطباعته إلى PDF
                document = QTextDocument()
                document.setHtml(html_content)
                document.print_(printer)

                show_info_message("تم", f"تم تصدير المصروفات إلى PDF بنجاح:\n{file_path}")

        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ في تصدير PDF: {str(e)}")

    def print_expenses(self):
        """طباعة المصروفات مباشرة"""
        try:
            expenses = self.session.query(Expense).all()

            if not expenses:
                show_info_message("طباعة", "لا توجد مصروفات للطباعة")
                return

            # إنشاء محتوى HTML للطباعة
            html_content = f"""
            <html dir="rtl">
            <head>
                <meta charset="utf-8">
                <title>تقرير المصروفات</title>
                <style>
                    body {{ font-family: Arial, sans-serif; margin: 20px; }}
                    h1 {{ color: #e74c3c; text-align: center; }}
                    table {{ width: 100%; border-collapse: collapse; margin-top: 20px; }}
                    th, td {{ border: 1px solid #ddd; padding: 8px; text-align: right; }}
                    th {{ background-color: #f2f2f2; }}
                    .total {{ font-weight: bold; background-color: #e8f5e8; }}
                </style>
            </head>
            <body>
                <h1>📋 تقرير المصروفات</h1>
                <p><strong>تاريخ التقرير:</strong> {QDate.currentDate().toString('yyyy-MM-dd')}</p>
                <p><strong>وقت الإنشاء:</strong> {QTime.currentTime().toString('hh:mm:ss')}</p>

                <table>
                    <tr>
                        <th>الرقم</th>
                        <th>العنوان</th>
                        <th>المبلغ</th>
                        <th>التاريخ</th>
                        <th>الفئة</th>
                        <th>المورد</th>
                        <th>الملاحظات</th>
                    </tr>
            """

            total_amount = 0
            for expense in expenses:
                date_str = expense.date.strftime("%Y-%m-%d") if expense.date else ""
                supplier_name = expense.supplier.name if expense.supplier else ""
                total_amount += expense.amount

                html_content += f"""
                    <tr>
                        <td>{expense.id}</td>
                        <td>{expense.title}</td>
                        <td>{int(expense.amount):,} جنيه</td>
                        <td>{date_str}</td>
                        <td>{expense.category or ""}</td>
                        <td>{supplier_name}</td>
                        <td>{expense.notes or ""}</td>
                    </tr>
                """

            html_content += f"""
                    <tr class="total">
                        <td colspan="2"><strong>الإجمالي</strong></td>
                        <td><strong>{int(total_amount):,} جنيه</strong></td>
                        <td colspan="4"></td>
                    </tr>
                </table>
            </body>
            </html>
            """

            # إنشاء مستند وطباعته
            document = QTextDocument()
            document.setHtml(html_content)

            printer = QPrinter()
            print_dialog = QPrintDialog(printer, self)

            if print_dialog.exec_() == QPrintDialog.Accepted:
                document.print_(printer)
                show_info_message("تم", "تم إرسال المصروفات للطباعة بنجاح")

        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ في الطباعة: {str(e)}")

    def export_to_csv(self):
        """تصدير المصروفات إلى CSV"""
        self.export_to_excel()  # نفس الوظيفة

    def export_to_json(self):
        """تصدير المصروفات إلى JSON"""
        try:
            import json
            from PyQt5.QtWidgets import QFileDialog

            file_path, _ = QFileDialog.getSaveFileName(
                self, "حفظ ملف JSON", "المصروفات.json", "JSON Files (*.json)"
            )

            if file_path:
                expenses = self.session.query(Expense).all()

                expenses_data = []
                for expense in expenses:
                    expense_data = {
                        'id': expense.id,
                        'title': expense.title,
                        'amount': expense.amount,
                        'date': expense.date.strftime("%Y-%m-%d") if expense.date else "",
                        'category': expense.category or "",
                        'supplier_name': expense.supplier.name if expense.supplier else "",
                        'supplier_id': expense.supplier_id,
                        'notes': expense.notes or ""
                    }
                    expenses_data.append(expense_data)

                export_data = {
                    "export_info": {
                        "export_date": QDate.currentDate().toString('yyyy-MM-dd'),
                        "export_time": QTime.currentTime().toString('hh:mm:ss'),
                        "total_expenses": len(expenses_data),
                        "total_amount": sum(expense.amount for expense in expenses),
                        "exported_by": "نظام إدارة المصروفات"
                    },
                    "expenses": expenses_data
                }

                with open(file_path, 'w', encoding='utf-8') as jsonfile:
                    json.dump(export_data, jsonfile, ensure_ascii=False, indent=2)

                show_info_message("تم", f"تم تصدير المصروفات بنجاح إلى:\n{file_path}")

        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ في التصدير: {str(e)}")

    def save_report_to_file(self, content, filename):
        """حفظ التقرير إلى ملف نصي"""
        try:
            from PyQt5.QtWidgets import QFileDialog

            file_path, _ = QFileDialog.getSaveFileName(
                self, "حفظ التقرير", f"{filename}.txt", "Text Files (*.txt)"
            )

            if file_path:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                show_info_message("تم", f"تم حفظ التقرير بنجاح إلى:\n{file_path}")

        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ في حفظ التقرير: {str(e)}")

    def print_report(self, text_browser):
        """طباعة التقرير"""
        try:
            from PyQt5.QtPrintSupport import QPrinter, QPrintDialog

            printer = QPrinter()
            print_dialog = QPrintDialog(printer, self)

            if print_dialog.exec_() == QPrintDialog.Accepted:
                text_browser.print_(printer)
                show_info_message("تم", "تم إرسال التقرير للطباعة بنجاح")

        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ في الطباعة: {str(e)}")

    def style_advanced_button(self, button, button_type, has_menu=False):
        """تطبيق تصميم متطور وجذاب على الأزرار مع ألوان متنوعة ومميزة"""
        try:
            # تحديد الألوان المتنوعة والمميزة حسب نوع الزر - مطابق للفواتير
            colors = {
                'primary': {
                    'bg_start': '#1a1a2e', 'bg_mid': '#16213e', 'bg_end': '#0f3460', 'bg_bottom': '#533483',
                    'hover_start': '#2a2a3e', 'hover_mid': '#26314e', 'hover_end': '#1f4470', 'hover_bottom': '#634493',
                    'hover_border': '#4f46e5', 'pressed_start': '#0a0a1e', 'pressed_mid': '#06112e',
                    'pressed_end': '#052450', 'pressed_bottom': '#332473', 'pressed_border': '#3730a3',
                    'border': '#4f46e5', 'text': '#ffffff', 'shadow': 'rgba(79, 70, 229, 0.5)'
                },
                'emerald': {
                    'bg_start': '#064e3b', 'bg_mid': '#047857', 'bg_end': '#065f46', 'bg_bottom': '#10b981',
                    'hover_start': '#10b981', 'hover_mid': '#34d399', 'hover_end': '#6ee7b7', 'hover_bottom': '#a7f3d0',
                    'hover_border': '#10b981', 'pressed_start': '#022c22', 'pressed_mid': '#064e3b',
                    'pressed_end': '#014737', 'pressed_bottom': '#047857', 'pressed_border': '#065f46',
                    'border': '#10b981', 'text': '#ffffff', 'shadow': 'rgba(16, 185, 129, 0.6)'
                },
                'danger': {
                    'bg_start': '#7f1d1d', 'bg_mid': '#991b1b', 'bg_end': '#b91c1c', 'bg_bottom': '#dc2626',
                    'hover_start': '#dc2626', 'hover_mid': '#ef4444', 'hover_end': '#f87171', 'hover_bottom': '#fca5a5',
                    'hover_border': '#dc2626', 'pressed_start': '#450a0a', 'pressed_mid': '#7f1d1d',
                    'pressed_end': '#991b1b', 'pressed_bottom': '#b91c1c', 'pressed_border': '#991b1b',
                    'border': '#dc2626', 'text': '#ffffff', 'shadow': 'rgba(220, 38, 38, 0.6)'
                },
                'info': {
                    'bg_start': '#0c4a6e', 'bg_mid': '#075985', 'bg_end': '#0369a1', 'bg_bottom': '#0284c7',
                    'hover_start': '#0284c7', 'hover_mid': '#0ea5e9', 'hover_end': '#38bdf8', 'hover_bottom': '#7dd3fc',
                    'hover_border': '#0ea5e9', 'pressed_start': '#082f49', 'pressed_mid': '#0c4a6e',
                    'pressed_end': '#075985', 'pressed_bottom': '#0369a1', 'pressed_border': '#075985',
                    'border': '#0ea5e9', 'text': '#ffffff', 'shadow': 'rgba(14, 165, 233, 0.6)'
                },
                'modern_teal': {
                    'bg_start': '#042f2e', 'bg_mid': '#134e4a', 'bg_end': '#0f766e', 'bg_bottom': '#0d9488',
                    'hover_start': '#0d9488', 'hover_mid': '#14b8a6', 'hover_end': '#2dd4bf', 'hover_bottom': '#5eead4',
                    'hover_border': '#14b8a6', 'pressed_start': '#042f2e', 'pressed_mid': '#134e4a',
                    'pressed_end': '#0f766e', 'pressed_bottom': '#0d9488', 'pressed_border': '#0f766e',
                    'border': '#14b8a6', 'text': '#ffffff', 'shadow': 'rgba(20, 184, 166, 0.6)'
                },
                'cyan': {
                    'bg_start': '#083344', 'bg_mid': '#164e63', 'bg_end': '#0e7490', 'bg_bottom': '#0891b2',
                    'hover_start': '#0891b2', 'hover_mid': '#06b6d4', 'hover_end': '#22d3ee', 'hover_bottom': '#67e8f9',
                    'hover_border': '#06b6d4', 'pressed_start': '#083344', 'pressed_mid': '#164e63',
                    'pressed_end': '#0e7490', 'pressed_bottom': '#0891b2', 'pressed_border': '#0e7490',
                    'border': '#06b6d4', 'text': '#ffffff', 'shadow': 'rgba(6, 182, 212, 0.6)'
                },
                'rose': {
                    'bg_start': '#500724', 'bg_mid': '#831843', 'bg_end': '#9d174d', 'bg_bottom': '#be185d',
                    'hover_start': '#be185d', 'hover_mid': '#ec4899', 'hover_end': '#f472b6', 'hover_bottom': '#f9a8d4',
                    'hover_border': '#ec4899', 'pressed_start': '#500724', 'pressed_mid': '#831843',
                    'pressed_end': '#9d174d', 'pressed_bottom': '#be185d', 'pressed_border': '#9d174d',
                    'border': '#ec4899', 'text': '#ffffff', 'shadow': 'rgba(236, 72, 153, 0.6)'
                },
                'indigo': {
                    'bg_start': '#312e81', 'bg_mid': '#3730a3', 'bg_end': '#4338ca', 'bg_bottom': '#4f46e5',
                    'hover_start': '#4f46e5', 'hover_mid': '#6366f1', 'hover_end': '#818cf8', 'hover_bottom': '#a5b4fc',
                    'hover_border': '#6366f1', 'pressed_start': '#312e81', 'pressed_mid': '#3730a3',
                    'pressed_end': '#4338ca', 'pressed_bottom': '#4f46e5', 'pressed_border': '#4338ca',
                    'border': '#6366f1', 'text': '#ffffff', 'shadow': 'rgba(99, 102, 241, 0.6)'
                }
            }

            # الحصول على نظام الألوان المحدد
            color_scheme = colors.get(button_type, colors['primary'])

            # تطبيق التصميم المتطور مطابق للفواتير
            style = f"""
                QPushButton {{
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 {color_scheme['bg_start']},
                        stop:0.3 {color_scheme['bg_mid']},
                        stop:0.7 {color_scheme['bg_end']},
                        stop:1 {color_scheme['bg_bottom']});
                    color: {color_scheme['text']};
                    border: 3px solid {color_scheme['border']};
                    border-radius: 20px;
                    padding: 12px 20px;
                    font-size: 16px;
                    font-weight: bold;
                    font-family: 'Segoe UI', 'Roboto', 'Helvetica Neue', 'Arial', sans-serif;
                    min-height: 34px;
                    max-height: 38px;
                    min-width: 120px;
                    text-align: center;
                    text-shadow: 3px 3px 6px rgba(0, 0, 0, 0.9),
                               2px 2px 4px rgba(0, 0, 0, 0.7),
                               1px 1px 2px rgba(0, 0, 0, 0.5);
                    box-shadow: 0 8px 20px {color_scheme['shadow']},
                               inset 0 3px 0 rgba(255, 255, 255, 0.4),
                               inset 0 -3px 0 rgba(0, 0, 0, 0.4),
                               0 0 25px rgba(255, 255, 255, 0.1);
                }}
                QPushButton:hover {{
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 {color_scheme['hover_start']},
                        stop:0.3 {color_scheme['hover_mid']},
                        stop:0.7 {color_scheme['hover_end']},
                        stop:1 {color_scheme['hover_bottom']});
                    border: 3px solid {color_scheme['hover_border']};
                    transform: translateY(-2px);
                    box-shadow: 0 12px 25px {color_scheme['shadow']},
                               inset 0 3px 0 rgba(255, 255, 255, 0.5),
                               inset 0 -3px 0 rgba(0, 0, 0, 0.3),
                               0 0 30px rgba(255, 255, 255, 0.2);
                }}
                QPushButton:pressed {{
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 {color_scheme['pressed_start']},
                        stop:0.3 {color_scheme['pressed_mid']},
                        stop:0.7 {color_scheme['pressed_end']},
                        stop:1 {color_scheme['pressed_bottom']});
                    border: 3px solid {color_scheme['pressed_border']};
                    transform: translateY(1px);
                    box-shadow: 0 4px 10px {color_scheme['shadow']},
                               inset 0 2px 0 rgba(0, 0, 0, 0.3),
                               inset 0 -2px 0 rgba(255, 255, 255, 0.2);
                }}
            """

            button.setStyleSheet(style)

        except Exception as e:
            print(f"خطأ في تطبيق تصميم الزر: {str(e)}")
            # تطبيق تصميم افتراضي في حالة الخطأ
            button.setStyleSheet("""
                QPushButton {
                    background: #3b82f6;
                    color: white;
                    border: 3px solid #1d4ed8;
                    border-radius: 20px;
                    padding: 12px 20px;
                    font-size: 16px;
                    font-weight: bold;
                    min-height: 34px;
                    max-height: 38px;
                }
                QPushButton:hover {
                    background: #2563eb;
                }
                QPushButton:pressed {
                    background: #1d4ed8;
                }
            """)


class ExpenseInfoDialog(QDialog):
    """نافذة عرض تفاصيل المصروف - مطابقة تماماً للنموذج المرجعي"""

    def __init__(self, parent=None, expense=None):
        super().__init__(parent)
        self.expense = expense
        self.parent_widget = parent
        self.setup_ui()

    @staticmethod
    def get_reference_styling():
        """الحصول على التصميم المرجعي الموحد"""
        return """
            QDialog {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #334155,
                    stop:0.3 #475569, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                    stop:0.6 #60A5FA, stop:0.7 #8B5CF6, stop:0.8 #7C3AED,
                    stop:0.9 #6D28D9, stop:1 #5B21B6);
                border: none;
                border-radius: 15px;
            }
        """

    def setup_ui(self):
        """إعداد واجهة النافذة - مطابق تماماً للنموذج المرجعي"""
        self.setWindowTitle(f"💰 {self.expense.title if self.expense.title else 'مصروف'}")
        self.setWindowFlags(Qt.Dialog | Qt.WindowTitleHint | Qt.WindowCloseButtonHint)
        self.setModal(True)
        self.resize(900, 700)

        # تخصيص شريط العنوان
        self.customize_title_bar()

        # خلفية متطابقة مع النموذج المرجعي
        self.setStyleSheet(self.get_reference_styling())

        # تخطيط رئيسي
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)

        # شريط العنوان الداخلي مطابق للنموذج المرجعي
        title_frame = QFrame()
        title_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(34, 197, 94, 0.25),
                    stop:0.2 rgba(59, 130, 246, 0.3),
                    stop:0.5 rgba(139, 92, 246, 0.28),
                    stop:0.8 rgba(168, 85, 247, 0.25),
                    stop:1 rgba(236, 72, 153, 0.2));
                border: 3px solid rgba(255, 255, 255, 0.3);
                border-radius: 8px;
                margin: 1px 0px 5px 0px;
                padding: 2px;
                max-height: 50px;
                min-height: 45px;
            }
        """)

        title_layout = QHBoxLayout(title_frame)
        title_layout.setContentsMargins(8, 5, 8, 11)
        title_layout.setSpacing(10)

        # نص العنوان في المنتصف
        title_text = QLabel(f"💰 تفاصيل المصروف: {self.expense.title}")
        title_text.setAlignment(Qt.AlignCenter)
        title_text.setStyleSheet("""
            QLabel {
                color: #FFFFFF;
                font-size: 20px;
                font-weight: bold;
                text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
                background: transparent;
                border: none;
                padding: 4px 15px;
                margin: 0px;
            }
        """)

        title_layout.addStretch()
        title_layout.addWidget(title_text)
        title_layout.addStretch()
        layout.addWidget(title_frame)

        # إنشاء منطقة التمرير المحسنة للمعلومات - مطابقة تماماً للنموذج المرجعي
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setStyleSheet("""
            QScrollArea {
                background: transparent;
                border: none;
                margin: 0px;
                padding: 0px;
            }
            QScrollBar:vertical {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 rgba(71, 85, 105, 0.4),
                    stop:1 rgba(100, 116, 139, 0.3));
                width: 16px;
                border-radius: 8px;
                border: 1px solid rgba(255, 255, 255, 0.1);
                margin: 2px;
            }
            QScrollBar::handle:vertical {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 rgba(59, 130, 246, 0.6),
                    stop:0.5 rgba(139, 92, 246, 0.5),
                    stop:1 rgba(34, 197, 94, 0.4));
                border: 1px solid rgba(255, 255, 255, 0.2);
                border-radius: 7px;
                min-height: 30px;
                margin: 1px;
            }
            QScrollBar::handle:vertical:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 rgba(59, 130, 246, 0.8),
                    stop:0.5 rgba(139, 92, 246, 0.7),
                    stop:1 rgba(34, 197, 94, 0.6));
                border: 2px solid rgba(255, 255, 255, 0.3);
            }
            QScrollBar::handle:vertical:pressed {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 rgba(37, 99, 235, 0.9),
                    stop:0.5 rgba(124, 58, 237, 0.8),
                    stop:1 rgba(21, 128, 61, 0.7));
            }
            QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {
                background: transparent;
                height: 0px;
            }
            QScrollBar::add-page:vertical, QScrollBar::sub-page:vertical {
                background: transparent;
            }
        """)

        # محتوى المعلومات المحسن - مطابق تماماً للنموذج المرجعي
        info_widget = QWidget()
        info_widget.setStyleSheet("""
            QWidget {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(15, 23, 42, 0.1),
                    stop:0.2 rgba(30, 41, 59, 0.08),
                    stop:0.5 rgba(51, 65, 85, 0.06),
                    stop:0.8 rgba(71, 85, 105, 0.08),
                    stop:1 rgba(100, 116, 139, 0.1));
                border-radius: 12px;
                padding: 10px;
            }
        """)

        info_layout = QVBoxLayout(info_widget)
        info_layout.setContentsMargins(15, 15, 15, 15)
        info_layout.setSpacing(25)

        # إضافة معلومات المصروف
        self.add_expense_info(info_layout)

        scroll_area.setWidget(info_widget)
        layout.addWidget(scroll_area)

        # أزرار التحكم
        self.create_control_buttons(layout)

        # تطبيق تصميم شريط العنوان
        self.apply_advanced_title_bar_styling()

    def add_expense_info(self, layout):
        """إضافة معلومات المصروف إلى التخطيط - مطابق تماماً للنموذج المرجعي"""
        if not self.expense:
            return

        # قسم المعلومات الأساسية
        basic_info = [
            ("🔢 المعرف الفريد", f"#{str(self.expense.id).zfill(8)}"),
            ("📝 عنوان المصروف", self.expense.title or "غير محدد"),
            ("🏷️ الفئة", self.expense.category or "غير محدد"),
            ("📊 حالة المصروف", self.get_expense_status()),
            ("📋 مستوى البيانات", self.get_data_completeness())
        ]
        self.add_info_section(layout, "📋 المعلومات الأساسية", basic_info)

        # قسم المعلومات المالية المحسن
        amount_color = self.get_amount_color()
        amount_text = f"{self.expense.amount:.0f} جنيه" if self.expense.amount else "0 جنيه"
        self.add_info_section(layout, "💰 المعلومات المالية", [
            ("💵 المبلغ", f"{amount_color} {amount_text}"),
            ("📊 تصنيف المبلغ", self.get_amount_category()),
            ("💳 نوع المصروف", self.get_expense_type()),
            ("📈 الأهمية المالية", self.get_financial_importance())
        ])

        # قسم معلومات التاريخ والتوقيت
        self.add_info_section(layout, "📅 معلومات التاريخ والتوقيت", [
            ("📅 تاريخ المصروف", self.get_expense_date()),
            ("⏰ وقت الإدخال", self.get_entry_time()),
            ("📊 العمر", self.get_expense_age()),
            ("🗓️ الفترة", self.get_period_info())
        ])

        # قسم معلومات المورد
        supplier_name = self.expense.supplier.name if self.expense.supplier else "غير محدد"
        self.add_info_section(layout, "🏭 معلومات المورد", [
            ("🏭 اسم المورد", supplier_name),
            ("📞 معلومات الاتصال", self.get_supplier_contact()),
            ("📊 تقييم المورد", self.get_supplier_rating()),
            ("💼 نوع التعامل", self.get_supplier_type())
        ])

        # قسم الملاحظات والتفاصيل الإضافية
        self.add_info_section(layout, "📝 ملاحظات وتفاصيل إضافية", [
            ("📝 الملاحظات", self.expense.notes or "لا توجد ملاحظات"),
            ("🔍 معلومات إضافية", self.get_additional_info()),
            ("📋 ملخص المصروف", self.get_expense_summary())
        ])

    def add_info_section(self, layout, title, items):
        """إضافة قسم معلومات محسن مع تصميم متطور - مطابق تماماً للنموذج المرجعي"""
        # إطار القسم الرئيسي
        section_frame = QFrame()
        section_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.08),
                    stop:0.3 rgba(248, 250, 252, 0.12),
                    stop:0.7 rgba(241, 245, 249, 0.10),
                    stop:1 rgba(226, 232, 240, 0.08));
                border: 2px solid rgba(255, 255, 255, 0.15);
                border-radius: 15px;
                margin: 8px 0px;
                padding: 0px;
            }
        """)

        section_layout = QVBoxLayout(section_frame)
        section_layout.setContentsMargins(15, 15, 15, 15)
        section_layout.setSpacing(12)

        # عنوان القسم المحسن
        section_title = QLabel(title)
        section_title.setAlignment(Qt.AlignCenter)
        section_title.setStyleSheet("""
            QLabel {
                color: #ffffff;
                font-size: 18px;
                font-weight: bold;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(59, 130, 246, 0.3),
                    stop:0.5 rgba(139, 92, 246, 0.25),
                    stop:1 rgba(34, 197, 94, 0.2));
                border: 2px solid rgba(255, 255, 255, 0.25);
                border-radius: 12px;
                padding: 12px 20px;
                margin-bottom: 10px;
                text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
            }
        """)
        section_layout.addWidget(section_title)

        # عناصر القسم المحسنة
        for i, (label, value) in enumerate(items):
            item_widget = QWidget()
            item_layout = QHBoxLayout(item_widget)
            item_layout.setContentsMargins(12, 8, 12, 8)
            item_layout.setSpacing(15)

            # خلفية متدرجة للعناصر
            item_widget.setStyleSheet(f"""
                QWidget {{
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                        stop:0 rgba(255, 255, 255, {0.03 + (i % 2) * 0.02}),
                        stop:0.5 rgba(248, 250, 252, {0.05 + (i % 2) * 0.02}),
                        stop:1 rgba(241, 245, 249, {0.03 + (i % 2) * 0.02}));
                    border: 1px solid rgba(255, 255, 255, 0.12);
                    border-radius: 10px;
                    margin: 2px 0px;
                }}
                QWidget:hover {{
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                        stop:0 rgba(59, 130, 246, 0.08),
                        stop:0.5 rgba(139, 92, 246, 0.06),
                        stop:1 rgba(34, 197, 94, 0.05));
                    border: 1px solid rgba(255, 255, 255, 0.2);
                }}
            """)

            # التسمية المحسنة - نصوص أوضح
            label_widget = QLabel(label)
            label_widget.setStyleSheet("""
                QLabel {
                    color: #FFFFFF;
                    font-size: 16px;
                    font-weight: 800;
                    min-width: 180px;
                    max-width: 180px;
                    padding: 12px 15px;
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 rgba(71, 85, 105, 0.6),
                        stop:1 rgba(100, 116, 139, 0.5));
                    border: 2px solid rgba(255, 255, 255, 0.25);
                    border-radius: 4px;
                    text-shadow: 2px 2px 3px rgba(0, 0, 0, 0.7);
                }
            """)

            # القيمة المحسنة مع ألوان مخصصة
            value_widget = QLabel(str(value))

            # تحديد لون القيمة حسب المحتوى
            value_color = self.get_value_color(label, str(value))

            value_widget.setStyleSheet(f"""
                QLabel {{
                    color: {value_color};
                    font-size: 15px;
                    font-weight: 600;
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 rgba(255, 255, 255, 0.08),
                        stop:0.5 rgba(248, 250, 252, 0.12),
                        stop:1 rgba(241, 245, 249, 0.08));
                    border: 1px solid rgba(255, 255, 255, 0.18);
                    border-radius: 8px;
                    padding: 10px 15px;
                    text-shadow: 1px 1px 1px rgba(0, 0, 0, 0.3);
                }}
                QLabel:hover {{
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 rgba(59, 130, 246, 0.15),
                        stop:0.5 rgba(139, 92, 246, 0.12),
                        stop:1 rgba(34, 197, 94, 0.10));
                    border: 1px solid rgba(255, 255, 255, 0.25);
                }}
            """)
            value_widget.setWordWrap(True)

            item_layout.addWidget(label_widget)
            item_layout.addWidget(value_widget, 1)

            section_layout.addWidget(item_widget)

        layout.addWidget(section_frame)

    def get_value_color(self, label, value):
        """تحديد لون القيمة حسب المحتوى - ألوان أوضح وأكثر تباينًا"""
        colors = {
            'positive': '#00FF7F',      # أخضر نيون للإيجابي
            'negative': '#FF6B6B',      # أحمر نيون للسلبي
            'neutral': '#E2E8F0',       # رمادي فاتح للمحايد
            'warning': '#FFD700',       # ذهبي للتحذيرات
            'info': '#00BFFF',          # أزرق سماوي للمعلومات
            'success': '#32CD32',       # أخضر ليموني للنجاح
            'error': '#FF4500',         # برتقالي أحمر للأخطاء
            'special': '#DA70D6',       # بنفسجي نيون للمميز
            'default': '#FFFFFF'        # أبيض نقي افتراضي
        }

        # تحديد اللون حسب التسمية والقيمة
        if "مبلغ" in label.lower():
            if "🟢" in value:
                return colors['positive']
            elif "🔴" in value:
                return colors['negative']
            else:
                return colors['neutral']
        elif "حالة" in label.lower():
            if "مكتمل" in value or "✅" in value:
                return colors['success']
            elif "معلق" in value or "⏳" in value:
                return colors['warning']
            else:
                return colors['info']
        elif "تصنيف" in label.lower() or "أهمية" in label.lower():
            if "عالي" in value or "مهم" in value or "⭐" in value:
                return colors['special']
            elif "متوسط" in value:
                return colors['warning']
            else:
                return colors['neutral']
        elif "غير محدد" in value or "لا توجد" in value:
            return colors['neutral']
        else:
            return colors['default']

    def get_amount_color(self):
        """تحديد لون المبلغ"""
        if not self.expense.amount or self.expense.amount == 0:
            return "🟡"  # أصفر للصفر
        elif self.expense.amount > 1000:
            return "🔴"  # أحمر للمبلغ الكبير
        else:
            return "🟢"  # أخضر للمبلغ العادي

    def get_expense_status(self):
        """حالة المصروف"""
        if self.expense.amount and self.expense.amount > 0:
            return "مكتمل ✅"
        else:
            return "معلق ⏳"

    def get_data_completeness(self):
        """مستوى اكتمال البيانات"""
        fields = [self.expense.title, self.expense.category, self.expense.notes,
                 str(self.expense.amount) if self.expense.amount else None]
        filled = sum(1 for field in fields if field and str(field).strip())
        percentage = (filled / len(fields)) * 100
        if percentage == 100:
            return f"مكتمل {percentage:.0f}% ✅"
        elif percentage >= 75:
            return f"جيد {percentage:.0f}% 🟢"
        elif percentage >= 50:
            return f"متوسط {percentage:.0f}% 🟡"
        else:
            return f"ناقص {percentage:.0f}% 🔴"

    def get_amount_category(self):
        """تصنيف المبلغ"""
        amount = self.expense.amount if self.expense.amount else 0
        if amount > 5000:
            return "مصروف كبير 🔴"
        elif amount > 1000:
            return "مصروف متوسط 🟡"
        elif amount > 0:
            return "مصروف صغير 🟢"
        else:
            return "غير محدد ⚪"

    def get_expense_type(self):
        """نوع المصروف"""
        category = self.expense.category if self.expense.category else ""
        if "تشغيل" in category.lower():
            return "مصروف تشغيلي 🔧"
        elif "إداري" in category.lower():
            return "مصروف إداري 📋"
        elif "صيانة" in category.lower():
            return "مصروف صيانة 🔨"
        else:
            return "مصروف عام 📊"

    def get_financial_importance(self):
        """الأهمية المالية"""
        amount = self.expense.amount if self.expense.amount else 0
        if amount > 10000:
            return "أهمية عالية جداً ⭐⭐⭐"
        elif amount > 5000:
            return "أهمية عالية ⭐⭐"
        elif amount > 1000:
            return "أهمية متوسطة ⭐"
        else:
            return "أهمية منخفضة 📊"

    def get_expense_date(self):
        """تاريخ المصروف"""
        if self.expense.date:
            # التعامل مع datetime أو date
            if hasattr(self.expense.date, 'strftime'):
                return self.expense.date.strftime('%Y-%m-%d')
            else:
                return str(self.expense.date)
        else:
            return "غير محدد"

    def get_entry_time(self):
        """وقت الإدخال"""
        if hasattr(self.expense, 'created_at') and self.expense.created_at:
            return self.expense.created_at.strftime('%H:%M:%S')
        else:
            return "غير محدد"

    def get_expense_age(self):
        """عمر المصروف"""
        if self.expense.date:
            # التأكد من أن التاريخ من نوع date وليس datetime
            expense_date = self.expense.date.date() if hasattr(self.expense.date, 'date') else self.expense.date
            days = (datetime.datetime.now().date() - expense_date).days
            if days == 0:
                return "اليوم"
            elif days == 1:
                return "أمس"
            elif days < 30:
                return f"{days} يوم"
            elif days < 365:
                months = days // 30
                return f"{months} شهر"
            else:
                years = days // 365
                return f"{years} سنة"
        return "غير محدد"

    def get_period_info(self):
        """معلومات الفترة"""
        if self.expense.date:
            try:
                # التعامل مع datetime أو date
                if hasattr(self.expense.date, 'strftime'):
                    month_name = self.expense.date.strftime('%B')
                    year = self.expense.date.year
                    return f"{month_name} {year}"
                else:
                    return str(self.expense.date)
            except:
                return "غير محدد"
        return "غير محدد"

    def get_supplier_contact(self):
        """معلومات اتصال المورد"""
        if self.expense.supplier:
            contact_info = []
            if hasattr(self.expense.supplier, 'phone') and self.expense.supplier.phone:
                contact_info.append(f"📱 {self.expense.supplier.phone}")
            if hasattr(self.expense.supplier, 'email') and self.expense.supplier.email:
                contact_info.append(f"📧 {self.expense.supplier.email}")
            return " | ".join(contact_info) if contact_info else "غير متاح"
        return "غير متاح"

    def get_supplier_rating(self):
        """تقييم المورد"""
        return "جيد ⭐⭐⭐"

    def get_supplier_type(self):
        """نوع التعامل مع المورد"""
        return "مورد معتمد ✅"

    def get_additional_info(self):
        """معلومات إضافية"""
        info_parts = []
        if self.expense.date:
            # التأكد من أن التاريخ من نوع date وليس datetime
            expense_date = self.expense.date.date() if hasattr(self.expense.date, 'date') else self.expense.date
            days_ago = (datetime.datetime.now().date() - expense_date).days
            info_parts.append(f"مضى {days_ago} يوم على المصروف")

        if self.expense.amount and self.expense.amount > 1000:
            info_parts.append(f"مبلغ كبير: {self.expense.amount:.0f} جنيه")

        return " | ".join(info_parts) if info_parts else "لا توجد معلومات إضافية"

    def get_expense_summary(self):
        """ملخص المصروف"""
        amount = self.expense.amount if self.expense.amount else 0
        category = self.expense.category if self.expense.category else "عام"
        supplier = self.expense.supplier.name if self.expense.supplier else "غير محدد"
        return f"مصروف {category} بمبلغ {amount:.0f} جنيه من المورد {supplier}"

    def create_control_buttons(self, layout):
        """إنشاء أزرار التحكم المحسنة - مطابق تماماً للنموذج المرجعي"""
        buttons_frame = QFrame()
        buttons_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.08),
                    stop:0.5 rgba(248, 250, 252, 0.12),
                    stop:1 rgba(241, 245, 249, 0.08));
                border: 2px solid rgba(255, 255, 255, 0.15);
                border-radius: 12px;
                padding: 10px;
                margin: 5px 0;
                min-height: 65px;
                max-height: 70px;
            }
        """)

        buttons_layout = QHBoxLayout(buttons_frame)
        buttons_layout.setSpacing(10)

        # زر الإغلاق - في المقدمة
        close_btn = QPushButton("❌ إغلاق النافذة")
        close_btn.setMinimumWidth(200)
        close_btn.setMaximumHeight(45)
        self.style_advanced_button(close_btn, 'danger')
        close_btn.clicked.connect(self.close)

        # زر الطباعة
        print_btn = QPushButton("🖨️ طباعة التفاصيل")
        print_btn.setMinimumWidth(200)
        print_btn.setMaximumHeight(45)
        self.style_advanced_button(print_btn, 'emerald')
        print_btn.clicked.connect(self.print_info)

        # زر تصدير PDF
        export_pdf_btn = QPushButton("📄 تصدير PDF")
        export_pdf_btn.setMinimumWidth(200)
        export_pdf_btn.setMaximumHeight(45)
        self.style_advanced_button(export_pdf_btn, 'info')
        export_pdf_btn.clicked.connect(self.export_to_pdf)

        # زر إضافة ملاحظة
        note_btn = QPushButton("📝 إضافة ملاحظة")
        note_btn.setMinimumWidth(200)
        note_btn.setMaximumHeight(45)
        self.style_advanced_button(note_btn, 'orange')
        note_btn.clicked.connect(self.add_note)

        # ترتيب الأزرار
        buttons_layout.addWidget(close_btn)
        buttons_layout.addWidget(print_btn)
        buttons_layout.addWidget(export_pdf_btn)
        buttons_layout.addWidget(note_btn)

        layout.addWidget(buttons_frame)

    def style_advanced_button(self, button, button_type):
        """تطبيق تصميم متطور على الأزرار - مطابق للنموذج المرجعي"""
        try:
            if self.parent_widget and hasattr(self.parent_widget, 'style_advanced_button'):
                self.parent_widget.style_advanced_button(button, button_type)
            else:
                colors = {
                    'emerald': ('#10b981', '#34d399'),
                    'danger': ('#ef4444', '#f87171'),
                    'info': ('#3b82f6', '#60a5fa'),
                    'orange': ('#f97316', '#fb923c')
                }

                color_pair = colors.get(button_type, ('#6B7280', '#9CA3AF'))

                button.setStyleSheet(f"""
                    QPushButton {{
                        background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                            stop:0 {color_pair[0]}, stop:1 {color_pair[1]});
                        color: white;
                        border: none;
                        border-radius: 8px;
                        padding: 12px 24px;
                        font-size: 14px;
                        font-weight: bold;
                        min-height: 20px;
                    }}
                    QPushButton:hover {{
                        background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                            stop:0 {color_pair[0]}, stop:1 {color_pair[0]});
                        transform: translateY(-2px);
                    }}
                    QPushButton:pressed {{
                        transform: translateY(0px);
                    }}
                """)
        except Exception as e:
            print(f"خطأ في تطبيق تصميم الزر: {str(e)}")

    def print_info(self):
        """طباعة معلومات المصروف"""
        try:
            from PyQt5.QtPrintSupport import QPrinter, QPrintDialog
            from PyQt5.QtGui import QPainter, QFont

            printer = QPrinter()
            dialog = QPrintDialog(printer, self)

            if dialog.exec_() == QPrintDialog.Accepted:
                painter = QPainter(printer)

                title_font = QFont("Arial", 16, QFont.Bold)
                header_font = QFont("Arial", 14, QFont.Bold)
                normal_font = QFont("Arial", 12)

                y = 100

                # العنوان الرئيسي
                painter.setFont(title_font)
                painter.drawText(100, y, f"تقرير تفصيلي للمصروف: {self.expense.title}")
                y += 80

                # المعلومات الأساسية
                painter.setFont(header_font)
                painter.drawText(100, y, "المعلومات الأساسية:")
                y += 40

                painter.setFont(normal_font)
                basic_info = [
                    f"المعرف: #{str(self.expense.id).zfill(6)}",
                    f"العنوان: {self.expense.title}",
                    f"المبلغ: {self.expense.amount or 0} جنيه",
                    f"الفئة: {self.expense.category or 'غير محدد'}",
                    f"التاريخ: {self.expense.date.strftime('%Y-%m-%d') if self.expense.date else 'غير محدد'}",
                    f"المورد: {self.expense.supplier.name if self.expense.supplier else 'غير محدد'}"
                ]

                for line in basic_info:
                    painter.drawText(120, y, line)
                    y += 30

                painter.end()

        except Exception as e:
            show_error_message("خطأ", f"فشل في الطباعة: {str(e)}")

    def export_to_pdf(self):
        """تصدير معلومات المصروف إلى PDF"""
        try:
            from PyQt5.QtWidgets import QFileDialog
            from PyQt5.QtPrintSupport import QPrinter
            from PyQt5.QtGui import QPainter, QFont, QColor, QPen
            from PyQt5.QtCore import QRect

            filename, _ = QFileDialog.getSaveFileName(
                self, "تصدير معلومات المصروف إلى PDF",
                f"expense_{self.expense.title}_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}.pdf",
                "PDF Files (*.pdf)"
            )

            if not filename:
                return

            printer = QPrinter()
            printer.setOutputFormat(QPrinter.PdfFormat)
            printer.setOutputFileName(filename)
            printer.setPageSize(QPrinter.A4)

            painter = QPainter()
            painter.begin(printer)

            title_font = QFont("Arial", 18, QFont.Bold)
            subtitle_font = QFont("Arial", 14, QFont.Bold)
            content_font = QFont("Arial", 12)

            page_rect = printer.pageRect()
            margin = 50
            content_rect = QRect(margin, margin,
                               page_rect.width() - 2*margin,
                               page_rect.height() - 2*margin)

            y_pos = content_rect.top()

            # رسم العنوان الرئيسي
            painter.setFont(title_font)
            painter.setPen(QColor(0, 0, 0))
            title_text = f"💰 معلومات المصروف: {self.expense.title}"
            painter.drawText(content_rect.left(), y_pos, title_text)
            y_pos += 70

            # رسم خط فاصل
            painter.setPen(QPen(QColor(100, 100, 100), 2))
            painter.drawLine(content_rect.left(), y_pos, content_rect.right(), y_pos)
            y_pos += 50

            # المعلومات الأساسية
            painter.setFont(subtitle_font)
            painter.setPen(QColor(0, 0, 0))
            painter.drawText(content_rect.left(), y_pos, "المعلومات الأساسية:")
            y_pos += 40

            painter.setFont(content_font)
            painter.setPen(QColor(50, 50, 50))

            basic_info = [
                f"معرف المصروف: {self.expense.id}",
                f"العنوان: {self.expense.title}",
                f"المبلغ: {self.expense.amount or 0} جنيه",
                f"الفئة: {self.expense.category or 'غير محدد'}",
                f"التاريخ: {self.expense.date.strftime('%Y-%m-%d') if self.expense.date else 'غير محدد'}",
                f"المورد: {self.expense.supplier.name if self.expense.supplier else 'غير محدد'}"
            ]

            for line in basic_info:
                painter.drawText(content_rect.left() + 20, y_pos, line)
                y_pos += 30

            painter.end()

            show_info_message("نجح", f"تم تصدير معلومات المصروف إلى:\n{filename}")

        except Exception as e:
            show_error_message("خطأ", f"فشل في التصدير: {str(e)}")

    def add_note(self):
        """فتح نافذة إضافة ملاحظة متطورة"""
        try:
            dialog = AddExpenseNoteDialog(self, self.expense, self.parent_widget)
            if dialog.exec_() == QDialog.Accepted:
                self.refresh_expense_info()
        except Exception as e:
            show_error_message("خطأ", f"فشل في فتح نافذة الملاحظات: {str(e)}")

    def refresh_expense_info(self):
        """تحديث معلومات المصروف"""
        try:
            if self.parent_widget and hasattr(self.parent_widget, 'session'):
                updated_expense = self.parent_widget.session.query(Expense).get(self.expense.id)
                if updated_expense:
                    self.expense = updated_expense
                    self.setup_ui()
        except Exception as e:
            print(f"خطأ في تحديث معلومات المصروف: {str(e)}")

    def customize_title_bar(self):
        """تخصيص شريط العنوان - موحد مع باقي النوافذ"""
        try:
            # إنشاء أيقونة مخصصة متطورة مع تدرجات جديدة
            pixmap = QPixmap(48, 48)  # حجم أكبر للوضوح
            pixmap.fill(Qt.transparent)

            painter = QPainter(pixmap)
            painter.setRenderHint(QPainter.Antialiasing)

            # تدرج متطور جديد للمصروفات
            gradient = QRadialGradient(24, 24, 20)
            gradient.setColorAt(0, QColor(239, 68, 68))    # أحمر
            gradient.setColorAt(0.7, QColor(220, 38, 38))  # أحمر داكن
            gradient.setColorAt(1, QColor(185, 28, 28))    # أحمر أكثر قتامة

            painter.setBrush(QBrush(gradient))
            painter.setPen(QPen(QColor(255, 255, 255, 180), 2))
            painter.drawEllipse(4, 4, 40, 40)

            # رسم الأيقونة
            painter.setPen(QPen(QColor(255, 255, 255), 3))
            painter.setFont(QFont("Arial", 16, QFont.Bold))
            painter.drawText(12, 30, "💰")
            painter.end()

            # تعيين الأيقونة
            icon = QIcon(pixmap)
            self.setWindowIcon(icon)

            # تطبيق تصميم متطور على شريط العنوان
            self.apply_advanced_title_bar_styling()

        except Exception as e:
            print(f"تحذير: فشل في تخصيص شريط العنوان: {e}")

    def apply_advanced_title_bar_styling(self):
        """تطبيق تصميم شريط العنوان المتطور - موحد مع باقي النوافذ"""
        try:
            # استخدام نفس TitleBarStyler المستخدم في الموردين
            from ui.suppliers import TitleBarStyler
            TitleBarStyler.apply_advanced_title_bar_styling(self)
        except Exception as e:
            try:
                # محاولة بديلة
                from ui.title_bar_styler import TitleBarStyler
                TitleBarStyler.apply_advanced_title_bar_styling(self)
            except Exception as e2:
                print(f"خطأ في تطبيق تصميم شريط العنوان: {e2}")


class AddExpenseNoteDialog(QDialog):
    """نافذة ملاحظات المصروف - مطابقة تماماً للنموذج المرجعي"""

    def __init__(self, parent=None, expense=None, parent_widget=None):
        super().__init__(parent)
        self.expense = expense
        self.parent_widget = parent_widget
        self.setup_ui()

    def setup_ui(self):
        """إعداد نافذة بسيطة جداً - مطابق للنموذج المرجعي"""
        self.setWindowTitle(f"📝 {self.expense.title if self.expense.title else 'مصروف'}")
        self.setWindowFlags(Qt.Dialog | Qt.WindowTitleHint | Qt.WindowCloseButtonHint)
        self.setModal(True)
        self.resize(450, 350)

        # تخصيص شريط العنوان الخارجي ليكون أسود
        self.customize_title_bar()

        # خلفية متطابقة مع النموذج المرجعي
        self.setStyleSheet(ExpenseInfoDialog.get_reference_styling())

        # تخطيط بسيط
        layout = QVBoxLayout(self)
        layout.setContentsMargins(15, 15, 15, 15)
        layout.setSpacing(10)

        # شريط العنوان الداخلي مطابق للنموذج المرجعي
        title_frame = QFrame()
        title_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(34, 197, 94, 0.25),
                    stop:0.2 rgba(59, 130, 246, 0.3),
                    stop:0.5 rgba(139, 92, 246, 0.28),
                    stop:0.8 rgba(168, 85, 247, 0.25),
                    stop:1 rgba(236, 72, 153, 0.2));
                border: 3px solid rgba(255, 255, 255, 0.3);
                border-radius: 8px;
                margin: 1px 0px 5px 0px;
                padding: 2px;
                max-height: 50px;
                min-height: 45px;
            }
        """)

        title_layout = QHBoxLayout(title_frame)
        title_layout.setContentsMargins(8, 5, 8, 11)
        title_layout.setSpacing(10)

        # نص العنوان في المنتصف - مكبر ومرفوع
        title_text = QLabel(f"📝 ملاحظة للمصروف: {self.expense.title}")
        title_text.setAlignment(Qt.AlignCenter)
        title_text.setStyleSheet("""
            QLabel {
                color: #FFFFFF;
                font-size: 20px;
                font-weight: bold;
                text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
                background: transparent;
                border: none;
                padding: 4px 15px;
                margin: 0px;
            }
        """)

        # وضع النص في المنتصف تماماً
        title_layout.addStretch()
        title_layout.addWidget(title_text)
        title_layout.addStretch()
        layout.addWidget(title_frame)

        # محرر النصوص المتطور مطابق للنموذج المرجعي
        self.text_editor = QTextEdit()
        self.text_editor.setStyleSheet("""
            QTextEdit {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.15),
                    stop:1 rgba(248, 250, 252, 0.1));
                color: #FFFFFF;
                font-size: 14px;
                font-family: 'Segoe UI', Arial, sans-serif;
                border: 2px solid rgba(255, 255, 255, 0.3);
                border-radius: 8px;
                padding: 10px;
                selection-background-color: rgba(59, 130, 246, 0.5);
                line-height: 1.4;
            }
            QTextEdit:focus {
                border: 2px solid rgba(255, 215, 0, 0.6);
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.2),
                    stop:1 rgba(248, 250, 252, 0.15));
            }
        """)
        self.text_editor.setPlaceholderText("اكتب ملاحظاتك هنا...\n\nيمكنك كتابة معلومات مفصلة عن المصروف، تذكيرات، أو أي ملاحظات مهمة.")
        self.text_editor.setMinimumHeight(180)
        layout.addWidget(self.text_editor)

        # أزرار بسيطة
        self.create_buttons(layout)

        # تحميل النص
        self.load_note()

    def create_buttons(self, layout):
        """أزرار متطورة مطابقة للنموذج المرجعي"""
        # إطار الأزرار مطابق للنموذج المرجعي
        buttons_frame = QFrame()
        buttons_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.08),
                    stop:1 rgba(241, 245, 249, 0.08));
                border: 2px solid rgba(255, 255, 255, 0.2);
                border-radius: 8px;
                padding: 15px;
                margin: 5px 0px;
                max-height: 80px;
            }
        """)

        buttons_layout = QHBoxLayout(buttons_frame)
        buttons_layout.setContentsMargins(10, 10, 10, 10)
        buttons_layout.setSpacing(20)

        # زر الإلغاء أولاً
        cancel_btn = QPushButton("❌ إلغاء")
        cancel_btn.setMinimumWidth(140)
        cancel_btn.setMinimumHeight(45)
        self.apply_reference_button_style(cancel_btn, 'danger')
        cancel_btn.clicked.connect(self.reject)

        # زر الحفظ ثانياً
        save_btn = QPushButton("💾 حفظ")
        save_btn.setMinimumWidth(140)
        save_btn.setMinimumHeight(45)
        self.apply_reference_button_style(save_btn, 'success')
        save_btn.clicked.connect(self.save_note)

        # وضع الأزرار في المنتصف
        buttons_layout.addStretch()
        buttons_layout.addWidget(cancel_btn)
        buttons_layout.addWidget(save_btn)
        buttons_layout.addStretch()

        layout.addWidget(buttons_frame)

    def apply_reference_button_style(self, button, button_type):
        """تطبيق تصميم الأزرار المرجعي المتطور"""
        if self.parent_widget and hasattr(self.parent_widget, 'style_advanced_button'):
            self.parent_widget.style_advanced_button(button, button_type)
        else:
            colors = {
                'success': {
                    'bg_start': '#064e3b', 'bg_mid': '#047857', 'bg_end': '#065f46', 'bg_bottom': '#10b981',
                    'hover_start': '#10b981', 'hover_mid': '#34d399', 'hover_end': '#6ee7b7', 'hover_bottom': '#a7f3d0',
                    'border': '#10b981', 'shadow': 'rgba(16, 185, 129, 0.6)'
                },
                'danger': {
                    'bg_start': '#7f1d1d', 'bg_mid': '#991b1b', 'bg_end': '#b91c1c', 'bg_bottom': '#dc2626',
                    'hover_start': '#dc2626', 'hover_mid': '#ef4444', 'hover_end': '#f87171', 'hover_bottom': '#fca5a5',
                    'border': '#dc2626', 'shadow': 'rgba(220, 38, 38, 0.6)'
                }
            }

            color_set = colors.get(button_type, colors['success'])

            button.setStyleSheet(f"""
                QPushButton {{
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 {color_set['bg_start']},
                        stop:0.15 {color_set['bg_mid']},
                        stop:0.85 {color_set['bg_end']},
                        stop:1 {color_set['bg_bottom']});
                    color: #ffffff;
                    border: 4px solid {color_set['border']};
                    border-radius: 16px;
                    padding: 8px 16px;
                    font-weight: 900;
                    font-size: 13px;
                    font-family: 'Segoe UI', 'Roboto', 'Helvetica Neue', 'Arial', sans-serif;
                    text-align: center;
                    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8),
                               1px 1px 2px rgba(0, 0, 0, 0.6);
                    box-shadow: 0 6px 15px {color_set['shadow']},
                               inset 0 2px 0 rgba(255, 255, 255, 0.3),
                               inset 0 -2px 0 rgba(0, 0, 0, 0.2);
                }}
                QPushButton:hover {{
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 {color_set['hover_start']},
                        stop:0.15 {color_set['hover_mid']},
                        stop:0.85 {color_set['hover_end']},
                        stop:1 {color_set['hover_bottom']});
                    transform: translateY(-2px);
                    box-shadow: 0 8px 20px {color_set['shadow']},
                               inset 0 2px 0 rgba(255, 255, 255, 0.4),
                               inset 0 -2px 0 rgba(0, 0, 0, 0.3);
                }}
                QPushButton:pressed {{
                    transform: translateY(1px);
                    box-shadow: 0 3px 8px {color_set['shadow']},
                               inset 0 2px 0 rgba(0, 0, 0, 0.2),
                               inset 0 -1px 0 rgba(255, 255, 255, 0.2);
                }}
            """)

    def load_note(self):
        """تحميل الملاحظة الحالية"""
        if self.expense and self.expense.notes:
            self.text_editor.setPlainText(self.expense.notes)

    def save_note(self):
        """حفظ الملاحظة - مطابق للنموذج المرجعي"""
        try:
            note = self.text_editor.toPlainText().strip()
            self.expense.notes = note if note else None

            if self.parent_widget and self.parent_widget.session:
                self.parent_widget.session.commit()
                if hasattr(self.parent_widget, 'refresh_data'):
                    self.parent_widget.refresh_data()

            self.accept()

            show_info_message("تم", "حُفظت الملاحظة")

        except Exception as e:
            show_error_message("خطأ", f"فشل الحفظ: {str(e)}")

    def customize_title_bar(self):
        """تخصيص شريط العنوان الخارجي - موحد مع باقي النوافذ"""
        try:
            # إنشاء أيقونة مخصصة متطورة
            pixmap = QPixmap(48, 48)
            pixmap.fill(Qt.transparent)

            painter = QPainter(pixmap)
            painter.setRenderHint(QPainter.Antialiasing)

            # تدرج متطور للملاحظات
            gradient = QRadialGradient(24, 24, 20)
            gradient.setColorAt(0, QColor(168, 85, 247))
            gradient.setColorAt(0.7, QColor(124, 58, 237))
            gradient.setColorAt(1, QColor(109, 40, 217))

            painter.setBrush(QBrush(gradient))
            painter.setPen(QPen(QColor(255, 255, 255, 180), 2))
            painter.drawEllipse(4, 4, 40, 40)
            painter.setPen(QPen(QColor(255, 255, 255), 3))
            painter.setFont(QFont("Arial", 16, QFont.Bold))
            painter.drawText(12, 30, "📝")
            painter.end()

            self.setWindowIcon(QIcon(pixmap))

            # تطبيق تصميم متطور على شريط العنوان - موحد
            from ui.suppliers import TitleBarStyler
            TitleBarStyler.apply_advanced_title_bar_styling(self)
        except Exception as e:
            try:
                from ui.title_bar_styler import TitleBarStyler
                TitleBarStyler.apply_advanced_title_bar_styling(self)
            except Exception as e2:
                print(f"خطأ في تخصيص شريط العنوان: {e2}")


class ExpensesStatisticsDialog(QDialog):
    """نافذة إحصائيات المصروفات مطابقة للعملاء والموردين والعمال والمشاريع والعقارات والمخزون والمبيعات والمشتريات"""

    def __init__(self, session, parent=None):
        super().__init__(parent)
        self.session = session
        self.parent_widget = parent
        self.setup_ui()

    def setup_ui(self):
        """إعداد واجهة النافذة المتطورة"""
        # إعداد النافذة الأساسي مع شريط عنوان موحد
        self.setWindowTitle("📊 إحصائيات المصروفات - نظام إدارة العملاء المتطور والشامل")
        self.setWindowFlags(Qt.Dialog | Qt.WindowTitleHint | Qt.WindowCloseButtonHint)
        self.setModal(True)
        self.resize(600, 420)  # نفس ارتفاع نافذة العملاء

        # تخصيص شريط العنوان الموحد
        self.customize_title_bar()

        # تطبيق نمط النافذة
        self.setStyleSheet("""
            QDialog {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #334155,
                    stop:0.3 #475569, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                    stop:0.6 #60A5FA, stop:0.7 #8B5CF6, stop:0.8 #7C3AED,
                    stop:0.9 #6D28D9, stop:1 #5B21B6);
                border: none;
                border-radius: 15px;
            }
        """)

        # التخطيط الرئيسي المضغوط
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 15, 20, 15)  # تقليل الهوامش
        layout.setSpacing(12)  # تقليل المسافات من 20 إلى 12

        # العنوان الرئيسي المطور بدون إطار - مطابق للعملاء
        title_container = QWidget()
        title_container.setStyleSheet("""
            QWidget {
                background: transparent;
                padding: 10px;
            }
        """)

        title_inner_layout = QVBoxLayout(title_container)
        title_inner_layout.setContentsMargins(0, 0, 0, 0)
        title_inner_layout.setSpacing(8)

        # الأيقونة والعنوان الرئيسي
        main_title = QLabel("📊 إحصائيات المصروفات")
        main_title.setAlignment(Qt.AlignCenter)
        main_title.setStyleSheet("""
            QLabel {
                color: #ffffff;
                font-size: 30px;
                font-weight: bold;
                background: transparent;
                text-shadow: 4px 4px 8px rgba(0, 0, 0, 0.9);
                padding: 10px;
            }
        """)

        # العنوان الفرعي التوضيحي
        subtitle = QLabel("تقرير شامل عن حالة المصروفات والنفقات")
        subtitle.setAlignment(Qt.AlignCenter)
        subtitle.setStyleSheet("""
            QLabel {
                color: rgba(255, 255, 255, 0.8);
                font-size: 14px;
                font-weight: normal;
                background: transparent;
                text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
                padding: 5px;
            }
        """)

        title_inner_layout.addWidget(main_title)
        title_inner_layout.addWidget(subtitle)
        layout.addWidget(title_container)

        # حساب الإحصائيات
        self.calculate_statistics()

        # إنشاء قائمة الإحصائيات المضغوطة
        stats_layout = QVBoxLayout()
        stats_layout.setSpacing(8)  # تقليل المسافة أكثر من 15 إلى 8
        stats_layout.setContentsMargins(20, 10, 20, 10)  # تقليل الهوامش أكثر

        # إنشاء قائمة الإحصائيات
        stats_items = [
            ("💸", "إجمالي المصروفات المسجلة", str(self.total_expenses), "#3B82F6"),
            ("✅", "المصروفات المدفوعة والمكتملة", str(self.paid_expenses), "#10B981"),
            ("⏳", "المصروفات المعلقة والمؤجلة", str(self.pending_expenses), "#F59E0B"),
            ("❌", "المصروفات الملغية أو المرفوضة", str(self.cancelled_expenses), "#EF4444"),
            ("💰", "إجمالي قيمة المصروفات", format_currency(self.total_amount), "#10B981")
        ]

        for icon, title, value, color in stats_items:
            # إنشاء عنصر مضغوط بدون إطارات
            item_widget = QWidget()
            item_widget.setStyleSheet("""
                QWidget {
                    background: transparent;
                    padding: 5px;  /* تقليل من 8px إلى 5px */
                    margin: 1px;   /* تقليل من 2px إلى 1px */
                }
                QWidget:hover {
                    background: rgba(255, 255, 255, 0.05);
                    border-radius: 6px;
                }
            """)

            item_layout = QHBoxLayout(item_widget)
            item_layout.setSpacing(12)  # تقليل من 15 إلى 12
            item_layout.setContentsMargins(12, 5, 12, 5)  # تقليل الهوامش

            # الأيقونة بدون إطارات
            icon_label = QLabel(icon)
            icon_label.setFixedSize(28, 28)  # تصغير أكثر
            icon_label.setAlignment(Qt.AlignCenter)
            icon_label.setStyleSheet(f"""
                QLabel {{
                    color: {color};
                    font-size: 18px;  /* تصغير أكثر */
                    font-weight: bold;
                    text-shadow: 3px 3px 6px rgba(0, 0, 0, 0.9);
                    background: transparent;
                    border: none;
                    padding: 5px;
                }}
            """)
            item_layout.addWidget(icon_label)

            # العنوان المطور مع وصف مفصل
            title_label = QLabel(title)
            title_label.setStyleSheet("""
                QLabel {
                    color: #ffffff;
                    font-size: 16px;
                    font-weight: bold;
                    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.9);
                    padding: 4px 8px;
                }
            """)
            item_layout.addWidget(title_label)

            # مساحة فارغة للدفع
            item_layout.addStretch()

            # القيمة بدون إطارات
            value_label = QLabel(value)
            value_label.setStyleSheet(f"""
                QLabel {{
                    color: {color};
                    font-size: 18px;
                    font-weight: bold;
                    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.9);
                    background: transparent;
                    border: none;
                    padding: 6px 12px;
                    min-width: 70px;
                }}
            """)
            value_label.setAlignment(Qt.AlignCenter)
            item_layout.addWidget(value_label)

            stats_layout.addWidget(item_widget)

        layout.addLayout(stats_layout)

        # أزرار التحكم مطابقة للعملاء
        buttons_layout = QHBoxLayout()
        buttons_layout.setSpacing(15)

        # زر تصدير PDF
        export_pdf_button = QPushButton("📄 تصدير PDF")
        export_pdf_button.clicked.connect(self.export_statistics_to_pdf)
        export_pdf_button.setMinimumHeight(45)
        self.style_advanced_button(export_pdf_button, 'info')

        # زر الإغلاق
        close_button = QPushButton("❌ إغلاق")
        close_button.clicked.connect(self.accept)
        close_button.setMinimumHeight(45)
        self.style_advanced_button(close_button, 'danger')

        buttons_layout.addWidget(close_button)
        buttons_layout.addWidget(export_pdf_button)

        layout.addLayout(buttons_layout)

    def calculate_statistics(self):
        """حساب إحصائيات المصروفات"""
        try:
            # حساب إجمالي المصروفات
            self.total_expenses = self.session.query(Expense).count()

            # حساب المصروفات حسب الفئة (بدلاً من الحالة)
            self.paid_expenses = self.session.query(Expense).filter(Expense.category == 'مدفوعات').count()
            self.pending_expenses = self.session.query(Expense).filter(Expense.category == 'معلق').count()
            self.cancelled_expenses = self.session.query(Expense).filter(Expense.category.in_(['ملغي', 'مرفوض'])).count()

            # حساب إجمالي المبلغ
            total_amount_result = self.session.query(func.sum(Expense.amount)).scalar()
            self.total_amount = total_amount_result or 0

        except Exception as e:
            print(f"خطأ في حساب إحصائيات المصروفات: {e}")
            self.total_expenses = 0
            self.paid_expenses = 0
            self.pending_expenses = 0
            self.cancelled_expenses = 0
            self.total_amount = 0

    def export_statistics_to_pdf(self):
        """تصدير الإحصائيات إلى PDF - دالة مؤقتة"""
        QMessageBox.information(self, "تصدير PDF", "ميزة تصدير PDF قيد التطوير")

    def style_advanced_button(self, button, button_type, has_menu=False):
        """تطبيق تصميم متطور للأزرار مطابق للعملاء"""
        try:
            if self.parent_widget and hasattr(self.parent_widget, 'style_advanced_button'):
                self.parent_widget.style_advanced_button(button, button_type, has_menu)
            else:
                # تصميم متطور مطابق للشريط الرئيسي
                color_schemes = {
                    'info': {
                        'base': '#3B82F6',
                        'hover': '#2563EB',
                        'pressed': '#1D4ED8',
                        'shadow': 'rgba(59, 130, 246, 0.4)'
                    },
                    'danger': {
                        'base': '#EF4444',
                        'hover': '#DC2626',
                        'pressed': '#B91C1C',
                        'shadow': 'rgba(239, 68, 68, 0.4)'
                    }
                }

                colors = color_schemes.get(button_type, color_schemes['info'])

                button.setStyleSheet(f"""
                    QPushButton {{
                        background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                            stop:0 {colors['base']}, stop:1 {colors['hover']});
                        color: #ffffff;
                        border: 2px solid rgba(255, 255, 255, 0.2);
                        border-radius: 12px;
                        padding: 12px 24px;
                        font-size: 14px;
                        font-weight: bold;
                        text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
                        min-width: 120px;
                    }}
                    QPushButton:hover {{
                        background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                            stop:0 {colors['hover']}, stop:1 {colors['base']});
                        border: 3px solid rgba(255, 255, 255, 0.4);
                        transform: translateY(-2px);
                        box-shadow: 0 8px 25px {colors['shadow']};
                    }}
                    QPushButton:pressed {{
                        background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                            stop:0 {colors['pressed']}, stop:1 {colors['hover']});
                        transform: translateY(0px);
                        box-shadow: 0 4px 15px {colors['shadow']};
                    }}
                """)

        except Exception as e:
            print(f"خطأ في تصميم الزر: {e}")

    def customize_title_bar(self):
        """تخصيص شريط العنوان"""
        try:
            from ui.title_bar_utils import TitleBarStyler
            TitleBarStyler.apply_advanced_title_bar_styling(self)
        except Exception as e:
            print(f"تحذير: فشل في تطبيق تصميم شريط العنوان: {e}")

    # دوال التصدير المتقدمة الجديدة - مطابقة للعملاء
    def export_excel_advanced(self):
        """تصدير Excel متقدم للمصروفات"""
        try:
            from PyQt5.QtWidgets import QFileDialog
            from datetime import datetime
            import csv

            file_path, _ = QFileDialog.getSaveFileName(
                self, "تصدير المصروفات - Excel متقدم",
                f"مصروفات_متقدم_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv",
                "CSV Files (*.csv)"
            )

            if file_path:
                expenses = self.session.query(Expense).all()

                with open(file_path, 'w', newline='', encoding='utf-8-sig') as csvfile:
                    writer = csv.writer(csvfile)

                    # كتابة العنوان
                    writer.writerow(['تقرير المصروفات المتقدم'])
                    writer.writerow([f'تاريخ التصدير: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}'])
                    writer.writerow([f'إجمالي المصروفات: {len(expenses)}'])
                    writer.writerow([])  # سطر فارغ

                    # كتابة الرؤوس
                    writer.writerow([
                        'الرقم', 'العنوان', 'المبلغ', 'الفئة', 'التاريخ',
                        'المورد', 'الحالة', 'الملاحظات', 'تاريخ الإنشاء'
                    ])

                    # كتابة البيانات
                    for expense in expenses:
                        writer.writerow([
                            expense.id,
                            expense.title,
                            f"{expense.amount:.2f}",
                            expense.category,
                            expense.date.strftime('%Y-%m-%d') if expense.date else '',
                            expense.supplier.name if expense.supplier else 'غير محدد',
                            expense.category,  # استخدام الفئة كحالة
                            expense.notes or '',
                            expense.date.strftime('%Y-%m-%d %H:%M:%S') if expense.date else ''
                        ])

                show_info_message("تم", f"تم تصدير المصروفات بنجاح (Excel متقدم):\n{file_path}")

        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ في التصدير المتقدم: {str(e)}")

    def export_csv_advanced(self):
        """تصدير CSV شامل للمصروفات"""
        try:
            from PyQt5.QtWidgets import QFileDialog
            from datetime import datetime
            import csv

            file_path, _ = QFileDialog.getSaveFileName(
                self, "تصدير المصروفات - CSV شامل",
                f"مصروفات_شامل_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv",
                "CSV Files (*.csv)"
            )

            if file_path:
                expenses = self.session.query(Expense).all()

                with open(file_path, 'w', newline='', encoding='utf-8-sig') as csvfile:
                    writer = csv.writer(csvfile)

                    # إحصائيات شاملة
                    total_amount = sum(expense.amount for expense in expenses)
                    categories = {}
                    for expense in expenses:
                        if expense.category in categories:
                            categories[expense.category] += expense.amount
                        else:
                            categories[expense.category] = expense.amount

                    # كتابة الإحصائيات
                    writer.writerow(['=== تقرير المصروفات الشامل ==='])
                    writer.writerow([f'تاريخ التصدير: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}'])
                    writer.writerow([f'إجمالي عدد المصروفات: {len(expenses)}'])
                    writer.writerow([f'إجمالي المبلغ: {total_amount:.2f} جنيه'])
                    writer.writerow([])

                    # إحصائيات الفئات
                    writer.writerow(['=== إحصائيات الفئات ==='])
                    for category, amount in categories.items():
                        writer.writerow([f'{category}: {amount:.2f} جنيه'])
                    writer.writerow([])

                    # البيانات التفصيلية
                    writer.writerow(['=== البيانات التفصيلية ==='])
                    writer.writerow([
                        'الرقم', 'العنوان', 'المبلغ', 'الفئة', 'التاريخ',
                        'المورد', 'الملاحظات'
                    ])

                    for expense in expenses:
                        writer.writerow([
                            expense.id,
                            expense.title,
                            f"{expense.amount:.2f}",
                            expense.category,
                            expense.date.strftime('%Y-%m-%d') if expense.date else '',
                            expense.supplier.name if expense.supplier else 'غير محدد',
                            expense.notes or ''
                        ])

                show_info_message("تم", f"تم تصدير المصروفات بنجاح (CSV شامل):\n{file_path}")

        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ في التصدير الشامل: {str(e)}")

    def export_pdf_advanced(self):
        """تصدير PDF تفصيلي للمصروفات"""
        try:
            from PyQt5.QtWidgets import QFileDialog
            from PyQt5.QtPrintSupport import QPrinter
            from PyQt5.QtGui import QTextDocument
            from datetime import datetime

            file_path, _ = QFileDialog.getSaveFileName(
                self, "تصدير المصروفات - PDF تفصيلي",
                f"مصروفات_تفصيلي_{datetime.now().strftime('%Y%m%d_%H%M%S')}.pdf",
                "PDF Files (*.pdf)"
            )

            if file_path:
                expenses = self.session.query(Expense).all()
                total_amount = sum(expense.amount for expense in expenses)

                # إنشاء محتوى HTML متقدم
                html_content = f"""
                <html dir="rtl">
                <head>
                    <meta charset="utf-8">
                    <style>
                        body {{ font-family: Arial, sans-serif; margin: 20px; }}
                        .header {{ text-align: center; color: #2563EB; margin-bottom: 30px; }}
                        .summary {{ background: #f8f9fa; padding: 15px; border-radius: 8px; margin-bottom: 20px; }}
                        table {{ width: 100%; border-collapse: collapse; margin-top: 20px; }}
                        th, td {{ border: 1px solid #ddd; padding: 8px; text-align: center; }}
                        th {{ background-color: #2563EB; color: white; }}
                        .total {{ font-weight: bold; background-color: #e3f2fd; }}
                    </style>
                </head>
                <body>
                    <div class="header">
                        <h1>📊 تقرير المصروفات التفصيلي</h1>
                        <p>تاريخ التصدير: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
                    </div>

                    <div class="summary">
                        <h3>📈 ملخص الإحصائيات</h3>
                        <p><strong>إجمالي عدد المصروفات:</strong> {len(expenses)}</p>
                        <p><strong>إجمالي المبلغ:</strong> {total_amount:.2f} جنيه</p>
                        <p><strong>متوسط المصروف:</strong> {(total_amount/len(expenses)):.2f} جنيه</p>
                    </div>

                    <table>
                        <thead>
                            <tr>
                                <th>الرقم</th>
                                <th>العنوان</th>
                                <th>المبلغ</th>
                                <th>الفئة</th>
                                <th>التاريخ</th>
                                <th>المورد</th>
                                <th>الملاحظات</th>
                            </tr>
                        </thead>
                        <tbody>
                """

                for expense in expenses:
                    html_content += f"""
                        <tr>
                            <td>{expense.id}</td>
                            <td>{expense.title}</td>
                            <td>{expense.amount:.2f} جنيه</td>
                            <td>{expense.category}</td>
                            <td>{expense.date.strftime('%Y-%m-%d') if expense.date else ''}</td>
                            <td>{expense.supplier.name if expense.supplier else 'غير محدد'}</td>
                            <td>{expense.notes or ''}</td>
                        </tr>
                    """

                html_content += f"""
                        </tbody>
                        <tfoot>
                            <tr class="total">
                                <td colspan="2"><strong>الإجمالي</strong></td>
                                <td><strong>{total_amount:.2f} جنيه</strong></td>
                                <td colspan="4"></td>
                            </tr>
                        </tfoot>
                    </table>
                </body>
                </html>
                """

                # إنشاء PDF
                printer = QPrinter(QPrinter.HighResolution)
                printer.setOutputFormat(QPrinter.PdfFormat)
                printer.setOutputFileName(file_path)
                printer.setPageSize(QPrinter.A4)

                document = QTextDocument()
                document.setHtml(html_content)
                document.print_(printer)

                show_info_message("تم", f"تم تصدير المصروفات بنجاح (PDF تفصيلي):\n{file_path}")

        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ في تصدير PDF التفصيلي: {str(e)}")

    def export_monthly_report(self):
        """تصدير التقرير الشهري للمصروفات"""
        show_info_message("قريباً", "ميزة التقرير الشهري قيد التطوير")

    def export_category_report(self):
        """تصدير تقرير الفئات"""
        show_info_message("قريباً", "ميزة تقرير الفئات قيد التطوير")

    def export_supplier_report(self):
        """تصدير تقرير الموردين"""
        show_info_message("قريباً", "ميزة تقرير الموردين قيد التطوير")

    def export_custom(self):
        """تصدير مخصص للمصروفات مع خيارات متقدمة - مطابق تماماً للعملاء"""
        try:
            from PyQt5.QtWidgets import QDialog, QVBoxLayout, QHBoxLayout, QCheckBox, QGroupBox, QPushButton, QLabel

            # إنشاء نافذة الخيارات المخصصة مع ألوان الإحصائيات - مطابق للعملاء
            dialog = QDialog(self)
            dialog.setWindowTitle("🔧 تصدير مخصص - خيارات متقدمة")
            dialog.setModal(True)
            dialog.resize(450, 500)

            # تطبيق نفس تصميم نوافذ الإحصائيات
            dialog.setStyleSheet("""
                QDialog {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                        stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #334155,
                        stop:0.3 #475569, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                        stop:0.6 #60A5FA, stop:0.7 #8B5CF6, stop:0.8 #7C3AED,
                        stop:0.9 #6D28D9, stop:1 #5B21B6);
                    border: none;
                    border-radius: 15px;
                }
            """)

            layout = QVBoxLayout()
            layout.setContentsMargins(25, 25, 25, 25)
            layout.setSpacing(20)

            # عنوان النافذة مطابق للإحصائيات
            title_label = QLabel("🔧 تصدير مخصص للمصروفات")
            title_label.setStyleSheet("""
                QLabel {
                    color: #ffffff;
                    font-size: 30px;
                    font-weight: bold;
                    text-align: center;
                    padding: 15px;
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 rgba(37, 99, 235, 0.8),
                        stop:0.5 rgba(59, 130, 246, 0.9),
                        stop:1 rgba(96, 165, 250, 0.8));
                    border: 3px solid rgba(37, 99, 235, 0.6);
                    border-radius: 12px;
                    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
                    box-shadow: 0 6px 20px rgba(37, 99, 235, 0.4);
                }
            """)
            title_label.setAlignment(Qt.AlignCenter)
            layout.addWidget(title_label)

            # عنوان فرعي
            subtitle_label = QLabel("اختر البيانات المراد تصديرها")
            subtitle_label.setStyleSheet("""
                QLabel {
                    color: #ffffff;
                    font-size: 16px;
                    font-weight: bold;
                    text-align: center;
                    padding: 8px;
                    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.7);
                }
            """)
            subtitle_label.setAlignment(Qt.AlignCenter)
            layout.addWidget(subtitle_label)

            # مجموعة خيارات البيانات
            data_group = QGroupBox("📊 البيانات المراد تصديرها")
            data_group.setStyleSheet("""
                QGroupBox {
                    color: #ffffff;
                    font-size: 14px;
                    font-weight: bold;
                    border: 2px solid rgba(255, 255, 255, 0.3);
                    border-radius: 10px;
                    margin-top: 10px;
                    padding-top: 10px;
                    background: rgba(255, 255, 255, 0.1);
                }
                QGroupBox::title {
                    subcontrol-origin: margin;
                    left: 10px;
                    padding: 0 10px 0 10px;
                }
            """)
            data_layout = QVBoxLayout()

            # خيارات التصدير مع علامات خارجية
            self.export_basic_info = QCheckBox("✓ المعلومات الأساسية")
            self.export_amounts = QCheckBox("✓ المبالغ والتواريخ")
            self.export_categories = QCheckBox("✓ الفئات والتصنيفات")
            self.export_suppliers = QCheckBox("✓ معلومات الموردين")
            self.export_notes = QCheckBox("✓ الملاحظات والتفاصيل")
            self.export_statistics = QCheckBox("✓ الإحصائيات والتحليلات")

            # تحديد جميع الخيارات افتراضياً
            for checkbox in [self.export_basic_info, self.export_amounts, self.export_categories,
                           self.export_suppliers, self.export_notes, self.export_statistics]:
                checkbox.setChecked(True)
                checkbox.setStyleSheet("""
                    QCheckBox {
                        color: #ffffff;
                        font-size: 13px;
                        font-weight: bold;
                        padding: 5px;
                        text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.7);
                    }
                    QCheckBox::indicator {
                        width: 18px;
                        height: 18px;
                        border: 2px solid #60A5FA;
                        border-radius: 4px;
                        background: rgba(255, 255, 255, 0.1);
                    }
                    QCheckBox::indicator:checked {
                        background: #60A5FA;
                        border: 2px solid #3B82F6;
                    }
                """)
                data_layout.addWidget(checkbox)

            data_group.setLayout(data_layout)
            layout.addWidget(data_group)

            # مجموعة تنسيق التصدير
            format_group = QGroupBox("📄 تنسيق التصدير")
            format_group.setStyleSheet("""
                QGroupBox {
                    color: #ffffff;
                    font-size: 14px;
                    font-weight: bold;
                    border: 2px solid rgba(255, 255, 255, 0.3);
                    border-radius: 10px;
                    margin-top: 10px;
                    padding-top: 10px;
                    background: rgba(255, 255, 255, 0.1);
                }
                QGroupBox::title {
                    subcontrol-origin: margin;
                    left: 10px;
                    padding: 0 10px 0 10px;
                }
            """)
            format_layout = QVBoxLayout()

            self.format_excel = QCheckBox("✓ Excel (.xlsx)")
            self.format_csv = QCheckBox("✓ CSV (.csv)")
            self.format_pdf = QCheckBox("✓ PDF (.pdf)")
            self.format_json = QCheckBox("✓ JSON (.json)")

            # تحديد Excel افتراضياً
            self.format_excel.setChecked(True)

            for checkbox in [self.format_excel, self.format_csv, self.format_pdf, self.format_json]:
                checkbox.setStyleSheet("""
                    QCheckBox {
                        color: #ffffff;
                        font-size: 13px;
                        font-weight: bold;
                        padding: 5px;
                        text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.7);
                    }
                    QCheckBox::indicator {
                        width: 18px;
                        height: 18px;
                        border: 2px solid #10B981;
                        border-radius: 4px;
                        background: rgba(255, 255, 255, 0.1);
                    }
                    QCheckBox::indicator:checked {
                        background: #10B981;
                        border: 2px solid #059669;
                    }
                """)
                format_layout.addWidget(checkbox)

            format_group.setLayout(format_layout)
            layout.addWidget(format_group)

            # أزرار التحكم مطابقة للإحصائيات
            buttons_layout = QHBoxLayout()
            buttons_layout.setSpacing(15)

            cancel_button = QPushButton("❌ إلغاء")
            cancel_button.clicked.connect(dialog.reject)
            cancel_button.setMinimumHeight(45)
            self.style_advanced_button(cancel_button, 'danger')

            export_button = QPushButton("📤 تصدير")
            export_button.clicked.connect(lambda: self.perform_custom_export(dialog))
            export_button.setMinimumHeight(45)
            self.style_advanced_button(export_button, 'emerald')

            buttons_layout.addWidget(cancel_button)
            buttons_layout.addWidget(export_button)
            layout.addLayout(buttons_layout)

            dialog.setLayout(layout)
            dialog.exec_()

        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ في نافذة التصدير المخصص: {str(e)}")

    def perform_custom_export(self, dialog):
        """تنفيذ التصدير المخصص"""
        try:
            # التحقق من اختيار تنسيق واحد على الأقل
            if not any([self.format_excel.isChecked(), self.format_csv.isChecked(),
                       self.format_pdf.isChecked(), self.format_json.isChecked()]):
                show_error_message("خطأ", "يجب اختيار تنسيق واحد على الأقل للتصدير")
                return

            dialog.accept()

            # تنفيذ التصدير حسب التنسيقات المختارة
            if self.format_excel.isChecked():
                self.export_excel_advanced()

            if self.format_csv.isChecked():
                self.export_csv_advanced()

            if self.format_pdf.isChecked():
                self.export_pdf_advanced()

            if self.format_json.isChecked():
                self.export_to_json()

        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ في تنفيذ التصدير المخصص: {str(e)}")

    def export_backup(self):
        """إنشاء نسخة احتياطية شاملة للمصروفات"""
        try:
            from PyQt5.QtWidgets import QFileDialog
            from datetime import datetime
            import json

            file_path, _ = QFileDialog.getSaveFileName(
                self, "حفظ النسخة الاحتياطية", f"نسخة_احتياطية_مصروفات_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json",
                "JSON Files (*.json)"
            )

            if file_path:
                expenses = self.session.query(Expense).all()
                backup_data = {
                    'backup_info': {
                        'created_at': datetime.now().isoformat(),
                        'total_records': len(expenses),
                        'backup_type': 'expenses_full_backup',
                        'version': '1.0'
                    },
                    'expenses': []
                }

                for expense in expenses:
                    expense_data = {
                        'id': expense.id,
                        'title': expense.title,
                        'amount': float(expense.amount) if expense.amount else 0.0,
                        'category': expense.category,
                        'date': expense.date.isoformat() if expense.date else None,
                        'supplier_id': expense.supplier_id if expense.supplier_id else None,
                        'supplier_name': expense.supplier.name if expense.supplier else None,
                        'notes': expense.notes,
                        'created_at': expense.date.isoformat() if expense.date else datetime.now().isoformat()
                    }
                    backup_data['expenses'].append(expense_data)

                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(backup_data, f, ensure_ascii=False, indent=2)

                show_info_message("تم", f"تم إنشاء النسخة الاحتياطية بنجاح:\n{file_path}\n\nتم حفظ {len(expenses)} مصروف")

        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ في إنشاء النسخة الاحتياطية: {str(e)}")

    def restore_backup(self):
        """استعادة نسخة احتياطية للمصروفات"""
        try:
            from PyQt5.QtWidgets import QFileDialog, QMessageBox
            import json
            from datetime import datetime

            file_path, _ = QFileDialog.getOpenFileName(
                self, "اختر النسخة الاحتياطية", "", "JSON Files (*.json)"
            )

            if file_path:
                # تأكيد الاستعادة
                reply = QMessageBox.question(
                    self, "تأكيد الاستعادة",
                    "هل أنت متأكد من استعادة النسخة الاحتياطية؟\n\nسيتم دمج البيانات مع الموجودة حالياً!",
                    QMessageBox.Yes | QMessageBox.No,
                    QMessageBox.No
                )

                if reply == QMessageBox.Yes:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        backup_data = json.load(f)

                    if 'expenses' in backup_data:
                        restored_count = 0
                        updated_count = 0

                        for expense_data in backup_data['expenses']:
                            # التحقق من وجود المصروف
                            existing = self.session.query(Expense).filter_by(title=expense_data.get('title')).first()

                            if existing:
                                # تحديث المصروف الموجود
                                existing.amount = expense_data.get('amount', 0)
                                existing.category = expense_data.get('category')
                                existing.notes = expense_data.get('notes')
                                if expense_data.get('date'):
                                    existing.date = datetime.fromisoformat(expense_data['date'])
                                updated_count += 1
                            else:
                                # إنشاء مصروف جديد
                                new_expense = Expense(
                                    title=expense_data.get('title'),
                                    amount=expense_data.get('amount', 0),
                                    category=expense_data.get('category'),
                                    date=datetime.fromisoformat(expense_data['date']) if expense_data.get('date') else datetime.now(),
                                    supplier_id=expense_data.get('supplier_id'),
                                    notes=expense_data.get('notes')
                                )
                                self.session.add(new_expense)
                                restored_count += 1

                        self.session.commit()
                        self.refresh_data()  # إعادة تحميل البيانات

                        show_info_message("تم", f"تم استعادة النسخة الاحتياطية بنجاح!\n\nتم إضافة {restored_count} مصروف جديد\nتم تحديث {updated_count} مصروف موجود")
                    else:
                        show_error_message("خطأ", "ملف النسخة الاحتياطية غير صالح!")

        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ في استعادة النسخة الاحتياطية: {str(e)}")

    def export_detailed_report(self):
        """تصدير تقرير تفصيلي للمصروفات"""
        show_info_message("قريباً", "ميزة التقرير التفصيلي قيد التطوير")

    def export_balance_report(self):
        """تصدير تقرير الأرصدة للمصروفات"""
        show_info_message("قريباً", "ميزة تقرير الأرصدة قيد التطوير")

    def safe_call_method(self, method_name):
        """استدعاء آمن للدوال مع معالجة الأخطاء"""
        try:
            if hasattr(self, method_name):
                method = getattr(self, method_name)
                method()
            else:
                show_info_message("قريباً", f"ميزة {method_name} قيد التطوير")
        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ في {method_name}: {str(e)}")
