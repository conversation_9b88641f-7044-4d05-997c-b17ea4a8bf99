# -*- coding: utf-8 -*-
"""
نظام مراقبة الأداء المتقدم
Advanced Performance Monitoring System
"""

import psutil
import time
import threading
import sqlite3
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Callable
import json
from pathlib import Path
import gc
import tracemalloc
from functools import wraps

from config import PerformanceConfig, AppConfig, DatabaseConfig
from logging_config import smart_logger

class PerformanceMonitor:
    """مراقب الأداء المتقدم"""
    
    def __init__(self):
        self.monitoring_active = False
        self.monitoring_thread = None
        self.performance_data = []
        self.slow_queries = []
        self.memory_snapshots = []
        self.alerts = []
        self.start_time = datetime.now()
        
        # بدء مراقبة الذاكرة إذا كانت مفعلة
        if PerformanceConfig.MEMORY_MONITORING_ENABLED:
            tracemalloc.start()
    
    def start_monitoring(self):
        """بدء مراقبة الأداء"""
        if not PerformanceConfig.PERFORMANCE_MONITORING_ENABLED:
            return
        
        try:
            self.monitoring_active = True
            self.monitoring_thread = threading.Thread(target=self._monitoring_loop, daemon=True)
            self.monitoring_thread.start()
            
            smart_logger.info("تم بدء مراقبة الأداء")
            
        except Exception as e:
            smart_logger.log_exception(e, "خطأ في بدء مراقبة الأداء")
    
    def stop_monitoring(self):
        """إيقاف مراقبة الأداء"""
        self.monitoring_active = False
        if self.monitoring_thread:
            self.monitoring_thread.join(timeout=5)
        
        smart_logger.info("تم إيقاف مراقبة الأداء")
    
    def _monitoring_loop(self):
        """حلقة مراقبة الأداء"""
        while self.monitoring_active:
            try:
                # جمع بيانات الأداء
                performance_data = self._collect_performance_data()
                self.performance_data.append(performance_data)
                
                # فحص التنبيهات
                self._check_performance_alerts(performance_data)
                
                # تنظيف البيانات القديمة
                self._cleanup_old_data()
                
                # انتظار قبل الفحص التالي
                time.sleep(60)  # فحص كل دقيقة
                
            except Exception as e:
                smart_logger.log_exception(e, "خطأ في حلقة مراقبة الأداء")
                time.sleep(60)
    
    def _collect_performance_data(self) -> Dict:
        """جمع بيانات الأداء"""
        try:
            # معلومات الذاكرة
            memory_info = psutil.virtual_memory()
            process = psutil.Process()
            process_memory = process.memory_info()
            
            # معلومات المعالج
            cpu_percent = psutil.cpu_percent(interval=1)
            process_cpu = process.cpu_percent()
            
            # معلومات القرص
            disk_usage = psutil.disk_usage('/')
            
            # معلومات قاعدة البيانات
            db_stats = self._get_database_stats()
            
            performance_data = {
                "timestamp": datetime.now().isoformat(),
                "system": {
                    "memory_total": memory_info.total,
                    "memory_available": memory_info.available,
                    "memory_percent": memory_info.percent,
                    "cpu_percent": cpu_percent,
                    "disk_total": disk_usage.total,
                    "disk_free": disk_usage.free,
                    "disk_percent": (disk_usage.used / disk_usage.total) * 100
                },
                "process": {
                    "memory_rss": process_memory.rss,
                    "memory_vms": process_memory.vms,
                    "memory_percent": process.memory_percent(),
                    "cpu_percent": process_cpu,
                    "num_threads": process.num_threads(),
                    "num_fds": process.num_fds() if hasattr(process, 'num_fds') else 0
                },
                "database": db_stats,
                "application": {
                    "uptime_seconds": (datetime.now() - self.start_time).total_seconds(),
                    "gc_collections": len(gc.get_stats()),
                    "active_threads": threading.active_count()
                }
            }
            
            return performance_data
            
        except Exception as e:
            smart_logger.log_exception(e, "خطأ في جمع بيانات الأداء")
            return {}
    
    def _get_database_stats(self) -> Dict:
        """الحصول على إحصائيات قاعدة البيانات"""
        try:
            if not DatabaseConfig.DB_PATH.exists():
                return {}
            
            conn = sqlite3.connect(DatabaseConfig.DB_PATH)
            cursor = conn.cursor()
            
            # حجم قاعدة البيانات
            db_size = DatabaseConfig.DB_PATH.stat().st_size
            
            # عدد الجداول
            cursor.execute("SELECT COUNT(*) FROM sqlite_master WHERE type='table'")
            table_count = cursor.fetchone()[0]
            
            # إحصائيات الصفحات
            cursor.execute("PRAGMA page_count")
            page_count = cursor.fetchone()[0]
            
            cursor.execute("PRAGMA page_size")
            page_size = cursor.fetchone()[0]
            
            # إحصائيات WAL
            wal_file = DatabaseConfig.DB_PATH.with_suffix('.db-wal')
            wal_size = wal_file.stat().st_size if wal_file.exists() else 0
            
            conn.close()
            
            return {
                "db_size": db_size,
                "table_count": table_count,
                "page_count": page_count,
                "page_size": page_size,
                "wal_size": wal_size,
                "total_size": db_size + wal_size
            }
            
        except Exception as e:
            smart_logger.log_exception(e, "خطأ في الحصول على إحصائيات قاعدة البيانات")
            return {}
    
    def _check_performance_alerts(self, performance_data: Dict):
        """فحص تنبيهات الأداء"""
        try:
            alerts = []
            
            # فحص استخدام الذاكرة
            process_memory_mb = performance_data.get("process", {}).get("memory_rss", 0) / (1024 * 1024)
            if process_memory_mb > PerformanceConfig.MAX_MEMORY_USAGE_MB:
                alerts.append({
                    "type": "high_memory_usage",
                    "severity": "warning",
                    "message": f"استخدام الذاكرة مرتفع: {process_memory_mb:.1f} MB",
                    "value": process_memory_mb,
                    "threshold": PerformanceConfig.MAX_MEMORY_USAGE_MB
                })
            
            # فحص استخدام المعالج
            cpu_percent = performance_data.get("process", {}).get("cpu_percent", 0)
            if cpu_percent > 80:  # 80% استخدام معالج
                alerts.append({
                    "type": "high_cpu_usage",
                    "severity": "warning",
                    "message": f"استخدام المعالج مرتفع: {cpu_percent:.1f}%",
                    "value": cpu_percent,
                    "threshold": 80
                })
            
            # فحص مساحة القرص
            disk_percent = performance_data.get("system", {}).get("disk_percent", 0)
            if disk_percent > 90:  # 90% امتلاء القرص
                alerts.append({
                    "type": "low_disk_space",
                    "severity": "critical",
                    "message": f"مساحة القرص منخفضة: {disk_percent:.1f}% مستخدمة",
                    "value": disk_percent,
                    "threshold": 90
                })
            
            # حفظ التنبيهات
            for alert in alerts:
                alert["timestamp"] = datetime.now().isoformat()
                self.alerts.append(alert)
                
                # تسجيل التنبيه
                smart_logger.log_performance_metric(
                    alert["type"], 
                    alert["value"], 
                    context=alert["message"]
                )
                
                # إرسال تنبيه حرج
                if alert["severity"] == "critical":
                    smart_logger.log_critical(f"تنبيه أداء حرج: {alert['message']}")
            
        except Exception as e:
            smart_logger.log_exception(e, "خطأ في فحص تنبيهات الأداء")
    
    def _cleanup_old_data(self):
        """تنظيف البيانات القديمة"""
        try:
            # الاحتفاظ ببيانات آخر 24 ساعة فقط
            cutoff_time = datetime.now() - timedelta(hours=24)
            
            # تنظيف بيانات الأداء
            self.performance_data = [
                data for data in self.performance_data
                if "timestamp" in data and datetime.fromisoformat(data["timestamp"]) > cutoff_time
            ]
            
            # تنظيف التنبيهات
            self.alerts = [
                alert for alert in self.alerts
                if datetime.fromisoformat(alert["timestamp"]) > cutoff_time
            ]
            
            # تنظيف الاستعلامات البطيئة
            self.slow_queries = [
                query for query in self.slow_queries
                if datetime.fromisoformat(query["timestamp"]) > cutoff_time
            ]
            
        except Exception as e:
            smart_logger.log_exception(e, "خطأ في تنظيف البيانات القديمة")
    
    def log_slow_query(self, query: str, execution_time: float, params: Optional[Dict] = None):
        """تسجيل استعلام بطيء"""
        if execution_time > PerformanceConfig.MAX_QUERY_TIME_SECONDS:
            slow_query = {
                "timestamp": datetime.now().isoformat(),
                "query": query[:500],  # أول 500 حرف فقط
                "execution_time": execution_time,
                "params": params or {},
                "stack_trace": traceback.format_stack()[-5:]  # آخر 5 مستويات
            }
            
            self.slow_queries.append(slow_query)
            
            smart_logger.log_performance_metric(
                "slow_query",
                execution_time,
                "seconds",
                f"Query: {query[:100]}..."
            )
    
    def take_memory_snapshot(self, description: str = ""):
        """أخذ لقطة من استخدام الذاكرة"""
        if not PerformanceConfig.MEMORY_MONITORING_ENABLED:
            return
        
        try:
            if tracemalloc.is_tracing():
                snapshot = tracemalloc.take_snapshot()
                top_stats = snapshot.statistics('lineno')
                
                memory_snapshot = {
                    "timestamp": datetime.now().isoformat(),
                    "description": description,
                    "total_memory": sum(stat.size for stat in top_stats),
                    "top_allocations": [
                        {
                            "file": stat.traceback.format()[0],
                            "size": stat.size,
                            "count": stat.count
                        }
                        for stat in top_stats[:10]  # أعلى 10 مخصصات
                    ]
                }
                
                self.memory_snapshots.append(memory_snapshot)
                
                smart_logger.log_performance_metric(
                    "memory_snapshot",
                    memory_snapshot["total_memory"],
                    "bytes",
                    description
                )
                
        except Exception as e:
            smart_logger.log_exception(e, "خطأ في أخذ لقطة الذاكرة")
    
    def get_performance_report(self, hours: int = 24) -> Dict:
        """الحصول على تقرير الأداء"""
        try:
            cutoff_time = datetime.now() - timedelta(hours=hours)
            
            # تصفية البيانات حسب الفترة الزمنية
            recent_data = [
                data for data in self.performance_data
                if datetime.fromisoformat(data["timestamp"]) > cutoff_time
            ]
            
            recent_alerts = [
                alert for alert in self.alerts
                if datetime.fromisoformat(alert["timestamp"]) > cutoff_time
            ]
            
            recent_slow_queries = [
                query for query in self.slow_queries
                if datetime.fromisoformat(query["timestamp"]) > cutoff_time
            ]
            
            # حساب الإحصائيات
            if recent_data:
                memory_usage = [data["process"]["memory_rss"] for data in recent_data]
                cpu_usage = [data["process"]["cpu_percent"] for data in recent_data]
                
                stats = {
                    "memory": {
                        "avg": sum(memory_usage) / len(memory_usage) / (1024 * 1024),  # MB
                        "max": max(memory_usage) / (1024 * 1024),  # MB
                        "min": min(memory_usage) / (1024 * 1024)   # MB
                    },
                    "cpu": {
                        "avg": sum(cpu_usage) / len(cpu_usage),
                        "max": max(cpu_usage),
                        "min": min(cpu_usage)
                    }
                }
            else:
                stats = {"memory": {}, "cpu": {}}
            
            return {
                "period_hours": hours,
                "data_points": len(recent_data),
                "statistics": stats,
                "alerts_count": len(recent_alerts),
                "slow_queries_count": len(recent_slow_queries),
                "alerts": recent_alerts,
                "slow_queries": recent_slow_queries[:10],  # أبطأ 10 استعلامات
                "uptime_seconds": (datetime.now() - self.start_time).total_seconds(),
                "generated_at": datetime.now().isoformat()
            }
            
        except Exception as e:
            smart_logger.log_exception(e, "خطأ في إنشاء تقرير الأداء")
            return {}
    
    def optimize_performance(self):
        """تحسين الأداء التلقائي"""
        try:
            # تشغيل garbage collection
            collected = gc.collect()
            smart_logger.info(f"تم تنظيف {collected} كائن من الذاكرة")
            
            # تحسين قاعدة البيانات
            self._optimize_database()
            
            smart_logger.info("تم تشغيل تحسين الأداء التلقائي")
            
        except Exception as e:
            smart_logger.log_exception(e, "خطأ في تحسين الأداء")
    
    def _optimize_database(self):
        """تحسين قاعدة البيانات"""
        try:
            if not DatabaseConfig.DB_PATH.exists():
                return
            
            conn = sqlite3.connect(DatabaseConfig.DB_PATH)
            cursor = conn.cursor()
            
            # تحليل الجداول
            cursor.execute("ANALYZE")
            
            # تحسين الفهارس
            cursor.execute("PRAGMA optimize")
            
            # تنظيف المساحة المهدرة
            cursor.execute("PRAGMA incremental_vacuum")
            
            conn.close()
            
            smart_logger.info("تم تحسين قاعدة البيانات")
            
        except Exception as e:
            smart_logger.log_exception(e, "خطأ في تحسين قاعدة البيانات")

# ديكوريتر لمراقبة أداء الدوال
def monitor_performance(func_name: str = None):
    """ديكوريتر لمراقبة أداء الدوال"""
    def decorator(func: Callable):
        @wraps(func)
        def wrapper(*args, **kwargs):
            start_time = time.time()
            
            try:
                result = func(*args, **kwargs)
                execution_time = time.time() - start_time
                
                # تسجيل الأداء
                performance_monitor.log_slow_query(
                    func_name or func.__name__,
                    execution_time,
                    {"args_count": len(args), "kwargs_count": len(kwargs)}
                )
                
                return result
                
            except Exception as e:
                execution_time = time.time() - start_time
                smart_logger.log_exception(e, f"خطأ في تنفيذ الدالة {func.__name__}")
                raise
                
        return wrapper
    return decorator

# إنشاء مثيل عام من مراقب الأداء
performance_monitor = PerformanceMonitor()

if __name__ == "__main__":
    # اختبار نظام مراقبة الأداء
    print("🔧 اختبار نظام مراقبة الأداء...")
    
    # بدء المراقبة
    performance_monitor.start_monitoring()
    
    # انتظار قليل لجمع البيانات
    time.sleep(5)
    
    # أخذ لقطة ذاكرة
    performance_monitor.take_memory_snapshot("اختبار النظام")
    
    # الحصول على تقرير
    report = performance_monitor.get_performance_report(1)  # آخر ساعة
    print(f"📊 تم جمع {report.get('data_points', 0)} نقطة بيانات")
    
    # إيقاف المراقبة
    performance_monitor.stop_monitoring()
    
    print("✅ تم اختبار نظام مراقبة الأداء بنجاح")
