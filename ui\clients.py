"""
═══════════════════════════════════════════════════════════════════════════════════
ملف العملاء - النموذج المرجعي للنظام
═══════════════════════════════════════════════════════════════════════════════════

هذا الملف يحتوي على:
- ClientInfoDialog: النموذج المرجعي لنوافذ المعلومات
- تصميم موحد ومتسق
- ألوان واضحة ومتباينة
- دوال مرجعية قابلة للاستخدام في نوافذ أخرى

استخدم ClientInfoDialog.get_reference_colors() للحصول على الألوان المرجعية
استخدم ClientInfoDialog.get_reference_styling() للحصول على التصميم المرجعي
"""

import re
import csv
from datetime import datetime as dt
from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QPushButton,
                            Q<PERSON><PERSON>l, Q<PERSON>ineEdit, QSizePolicy, Q<PERSON><PERSON><PERSON>, Q<PERSON><PERSON><PERSON>, QA<PERSON>,
                            QTableWidget, QTableWidgetItem, QHeaderView, QDialog,
                            QFormLayout, QTextEdit, QDoubleSpinBox, QMessageBox, QShortcut,
                            QFileDialog, QAbstractSlider, QScrollArea, QGridLayout,
                            QListWidget, QListWidgetItem)
from PyQt5.QtCore import Qt, QTimer, QSize
from PyQt5.QtGui import (QFont, QColor, QPainter, QIcon, QPixmap, QBrush, QKeySequence,
                        QRadialGradient, QPen, QPalette)
from PyQt5.QtWidgets import QGraphicsDropShadowEffect

from database import Client, Document
from utils import format_currency, show_info_message, show_error_message, show_confirmation_message
from ui.unified_styles import UnifiedStyles
from ui.common_dialogs import WarningDialog
from ui.title_bar_utils import TitleBarStyler


class ClientsWidget(QWidget):
    """واجهة إدارة العملاء"""

    def __init__(self, session):
        super().__init__()
        try:
            self.session = session
            self.init_ui()
        except Exception as e:
            print(f"خطأ في تهيئة قسم العملاء: {str(e)}")
            # إنشاء واجهة بسيطة في حالة الخطأ
            layout = QVBoxLayout()
            error_label = QLabel(f"خطأ في تحميل قسم العملاء: {str(e)}")
            layout.addWidget(error_label)
            self.setLayout(layout)

    def manage_documents(self):
        """إدارة وثائق العميل - بطاقة هوية، عقد، وثائق أخرى"""
        try:
            print("📁 بدء إدارة وثائق العميل")

            selected_row = self.clients_table.currentRow()
            if selected_row < 0:
                show_error_message("خطأ", "الرجاء اختيار عميل من القائمة")
                return

            # الحصول على معرف العميل بطريقة آمنة
            try:
                client_id_text = self.clients_table.item(selected_row, 0).text()
                # إزالة الأيقونات والنصوص الإضافية والحصول على الرقم فقط
                client_id_clean = ''.join(filter(str.isdigit, client_id_text))
                if not client_id_clean:
                    show_error_message("خطأ", "لا يمكن الحصول على معرف العميل")
                    return
                client_id = int(client_id_clean)
            except (ValueError, AttributeError) as e:
                print(f"خطأ في تحويل معرف العميل: {e}")
                show_error_message("خطأ", "خطأ في معرف العميل المحدد")
                return

            client = self.session.query(Client).get(client_id)
            if not client:
                show_error_message("خطأ", "لم يتم العثور على العميل")
                return

            print(f"✅ تم العثور على العميل: {client.name}")

            # فتح نافذة إدارة الوثائق المحسنة
            dialog = ClientDocumentsDialog(self, client=client, session=self.session)
            dialog.exec_()

        except Exception as e:
            print(f"❌ خطأ في إدارة وثائق العميل: {str(e)}")
            show_error_message("خطأ", f"حدث خطأ في إدارة وثائق العميل: {str(e)}")

    def init_ui(self):
        # تهيئة آمنة لمتغيرات التحديد المتعدد
        self.selected_clients = []
        self.last_selected_row = -1

        # إنشاء التخطيط الرئيسي مطابق للفواتير
        main_layout = QVBoxLayout()
        main_layout.setContentsMargins(2, 2, 2, 2)  # تقليل الهوامش من 10 إلى 2
        main_layout.setSpacing(2)  # تقليل المسافات من 8 إلى 2

        # إضافة العنوان الرئيسي المطور والمحسن مطابق للفواتير
        title_label = QLabel("🤝 إدارة العملاء المتطورة - نظام شامل ومتقدم لإدارة العملاء مع أدوات احترافية للبحث والتحليل والتقارير")
        title_label.setFont(QFont("Segoe UI", 18, QFont.Bold))  # خط أكبر وأوضح
        title_label.setAlignment(Qt.AlignCenter)  # توسيط النص في المنتصف
        title_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #334155,
                    stop:0.3 #475569, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                    stop:0.6 #60A5FA, stop:0.7 #8B5CF6, stop:0.8 #7C3AED,
                    stop:0.9 #6D28D9, stop:1 #5B21B6);
                border: 3px solid #000000;
                border-radius: 10px;
                padding: 4px 10px;
                margin: 2px;
                font-weight: bold;
                max-height: 40px;
                min-height: 40px;
            }
        """)
        main_layout.addWidget(title_label)

        # إنشاء إطار علوي محسن بنفس الأسلوب القديم (صف واحد) مطابق للفواتير
        top_frame = QFrame()
        top_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #ffffff,
                    stop:0.3 #f8fafc,
                    stop:0.7 #e2e8f0,
                    stop:1 #cbd5e0);
                border: 3px solid #000000;
                border-radius: 10px;
                margin: 1px;
                padding: 0px;
                max-height: 65px;
                min-height: 60px;
            }
        """)

        # تخطيط أفقي واحد محسن (الطريقة القديمة)
        search_layout = QHBoxLayout()
        search_layout.setContentsMargins(0, 0, 0, 0)  # إزالة الهوامش للتوسيط الدقيق
        search_layout.setSpacing(3)  # مسافات أقل

        # إنشاء حاوي عمودي للتوسيط الحقيقي
        top_container = QVBoxLayout()
        top_container.setContentsMargins(4, 0, 4, 0)  # هوامش جانبية أقل
        top_container.setSpacing(0)

        # إضافة مساحة فارغة أعلى للتوسيط
        top_container.addStretch(1)

        # إضافة التخطيط الأفقي في المنتصف
        top_container.addLayout(search_layout)

        # إضافة مساحة فارغة أسفل للتوسيط
        top_container.addStretch(1)

        # تسمية البحث محسنة مع ألوان موحدة مطابقة للفواتير
        search_label = QLabel("🔍 بحث:")
        search_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                font-size: 16px;
                font-weight: 900;
                padding: 8px 12px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(37, 99, 235, 0.8),
                    stop:0.2 rgba(59, 130, 246, 0.7),
                    stop:0.4 rgba(96, 165, 250, 0.6),
                    stop:0.6 rgba(139, 92, 246, 0.7),
                    stop:0.8 rgba(124, 58, 237, 0.8),
                    stop:1 rgba(109, 40, 217, 0.7));
                border: 3px solid rgba(96, 165, 250, 0.8);
                border-radius: 15px;
                min-width: 70px;
                max-width: 70px;
                max-height: 38px;
                min-height: 34px;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
                transition: all 0.3s ease;
                text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
            }
            QLabel:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(59, 130, 246, 0.9),
                    stop:0.2 rgba(96, 165, 250, 0.8),
                    stop:0.4 rgba(139, 92, 246, 0.7),
                    stop:0.6 rgba(124, 58, 237, 0.8),
                    stop:0.8 rgba(109, 40, 217, 0.9),
                    stop:1 rgba(91, 33, 182, 0.8));
                border: 4px solid rgba(96, 165, 250, 0.95);
                transform: translateY(-1px);
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.25);
            }
        """)
        search_label.setAlignment(Qt.AlignCenter)  # توسيط النص داخل التسمية

        self.search_edit = QLineEdit()
        self.search_edit.setPlaceholderText("🔎 ابحث باسم العميل، الهاتف، البريد الإلكتروني أو العنوان...")
        self.search_edit.textChanged.connect(self.filter_clients)
        self.search_edit.setStyleSheet("""
            QLineEdit {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.9),
                    stop:0.2 rgba(248, 250, 252, 0.95),
                    stop:0.4 rgba(241, 245, 249, 0.9),
                    stop:0.6 rgba(248, 250, 252, 0.95),
                    stop:0.8 rgba(255, 255, 255, 0.9),
                    stop:1 rgba(226, 232, 240, 0.85));
                border: 3px solid rgba(96, 165, 250, 0.8);
                border-radius: 15px;
                padding: 8px 15px;
                font-size: 16px;
                font-weight: 900;
                color: #1f2937;
                max-height: 38px;
                min-height: 34px;
                selection-background-color: rgba(96, 165, 250, 0.3);
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
                transition: all 0.3s ease;
            }
            QLineEdit:focus {
                border: 4px solid rgba(96, 165, 250, 0.95);
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(240, 249, 255, 0.95),
                    stop:0.2 rgba(224, 242, 254, 0.9),
                    stop:0.4 rgba(186, 230, 253, 0.85),
                    stop:0.6 rgba(224, 242, 254, 0.9),
                    stop:0.8 rgba(240, 249, 255, 0.95),
                    stop:1 rgba(255, 255, 255, 0.9));
                box-shadow: 0 4px 12px rgba(96, 165, 250, 0.3);
            }
            QLineEdit:hover {
                border: 4px solid rgba(96, 165, 250, 0.9);
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(250, 251, 255, 0.95),
                    stop:0.2 rgba(241, 245, 249, 0.9),
                    stop:0.4 rgba(226, 232, 240, 0.85),
                    stop:0.6 rgba(241, 245, 249, 0.9),
                    stop:0.8 rgba(250, 251, 255, 0.95),
                    stop:1 rgba(255, 255, 255, 0.9));
                transform: translateY(-1px);
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
            }
        """)

        search_button = QPushButton("🔍")
        search_button.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(37, 99, 235, 0.8),
                    stop:0.2 rgba(59, 130, 246, 0.7),
                    stop:0.4 rgba(96, 165, 250, 0.6),
                    stop:0.6 rgba(139, 92, 246, 0.7),
                    stop:0.8 rgba(124, 58, 237, 0.8),
                    stop:1 rgba(109, 40, 217, 0.7));
                color: #ffffff;
                border: 2px solid rgba(96, 165, 250, 0.7);
                border-radius: 15px;
                padding: 8px;
                font-size: 22px;
                font-weight: 900;
                min-width: 50px;
                max-width: 50px;
                max-height: 38px;
                min-height: 34px;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
                transition: all 0.3s ease;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(59, 130, 246, 0.9),
                    stop:0.2 rgba(96, 165, 250, 0.8),
                    stop:0.4 rgba(139, 92, 246, 0.7),
                    stop:0.6 rgba(124, 58, 237, 0.8),
                    stop:0.8 rgba(109, 40, 217, 0.9),
                    stop:1 rgba(91, 33, 182, 0.8));
                border: 3px solid rgba(96, 165, 250, 0.9);
                transform: translateY(-1px);
                box-shadow: 0 4px 12px rgba(96, 165, 250, 0.3);
            }
            QPushButton:pressed {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(29, 78, 216, 0.9),
                    stop:0.2 rgba(37, 99, 235, 0.8),
                    stop:0.4 rgba(59, 130, 246, 0.7),
                    stop:0.6 rgba(96, 165, 250, 0.8),
                    stop:0.8 rgba(124, 58, 237, 0.9),
                    stop:1 rgba(109, 40, 217, 0.8));
                border: 3px solid rgba(96, 165, 250, 0.95);
                transform: translateY(1px);
                box-shadow:
                    0 0 15px rgba(96, 165, 250, 0.4),
                    0 2px 8px rgba(0, 0, 0, 0.4),
                    inset 0 1px 2px rgba(255, 255, 255, 0.3);
            }
        """)
        search_button.clicked.connect(self.filter_clients)
        search_button.setToolTip("بحث متقدم")
        search_button.setSizePolicy(QSizePolicy.Fixed, QSizePolicy.Fixed)
        search_button.setContentsMargins(0, 0, 0, 0)

        # تسمية التصفية مطورة بألوان احترافية
        filter_label = QLabel("🎯 حالة:")
        filter_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                font-size: 16px;
                font-weight: 900;
                padding: 8px 12px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(37, 99, 235, 0.8),
                    stop:0.2 rgba(59, 130, 246, 0.7),
                    stop:0.4 rgba(96, 165, 250, 0.6),
                    stop:0.6 rgba(139, 92, 246, 0.7),
                    stop:0.8 rgba(124, 58, 237, 0.8),
                    stop:1 rgba(109, 40, 217, 0.7));
                border: 3px solid rgba(96, 165, 250, 0.8);
                border-radius: 15px;
                min-width: 70px;
                max-width: 70px;
                max-height: 38px;
                min-height: 34px;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
                transition: all 0.3s ease;
                text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
            }
            QLabel:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(59, 130, 246, 0.9),
                    stop:0.2 rgba(96, 165, 250, 0.8),
                    stop:0.4 rgba(139, 92, 246, 0.7),
                    stop:0.6 rgba(124, 58, 237, 0.8),
                    stop:0.8 rgba(109, 40, 217, 0.9),
                    stop:1 rgba(91, 33, 182, 0.8));
                border: 4px solid rgba(96, 165, 250, 0.95);
                transform: translateY(-1px);
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.25);
            }
        """)
        filter_label.setAlignment(Qt.AlignCenter)  # توسيط النص داخل التسمية

        # إنشاء قائمة تصفية مخصصة ومطورة
        self.create_custom_status_filter()

        # إضافة جميع العناصر للصف الواحد مع استغلال العرض الكامل داخل الإطار
        search_layout.addWidget(search_label, 0, Qt.AlignVCenter)
        search_layout.addWidget(self.search_edit, 2, Qt.AlignVCenter)  # يأخذ مساحة أكبر
        search_layout.addWidget(search_button, 0, Qt.AlignVCenter)
        search_layout.addWidget(filter_label, 0, Qt.AlignVCenter)
        search_layout.addWidget(self.status_filter, 1, Qt.AlignVCenter)

        # تعيين التخطيط للإطار العلوي - استخدام الحاوي العمودي للتوسيط
        top_frame.setLayout(top_container)

        # إنشاء جدول العملاء المتطور والمحسن
        self.create_advanced_clients_table()

        main_layout.addWidget(top_frame)
        main_layout.addWidget(self.clients_table, 1)  # إعطاء الجدول أولوية في التمدد

        # إنشاء إطار سفلي للأزرار متساوي مع الجدول وارتفاع أقل مطابق للفواتير
        bottom_frame = QFrame()
        bottom_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #ffffff,
                    stop:0.3 #f8fafc,
                    stop:0.7 #e2e8f0,
                    stop:1 #cbd5e0);
                border: 3px solid #000000;
                border-radius: 10px;
                margin: 1px;
                padding: 0px;
                max-height: 75px;
                min-height: 70px;
            }
        """)
        actions_layout = QHBoxLayout()
        actions_layout.setContentsMargins(0, 0, 0, 0)
        actions_layout.setSpacing(4)

        # زر الإضافة
        self.add_button = QPushButton("➕ إضافة عميل")
        self.style_advanced_button(self.add_button, 'emerald')
        self.add_button.clicked.connect(self.add_client)
        self.add_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        # زر التعديل
        self.edit_button = QPushButton("✏️ تعديل")
        self.style_advanced_button(self.edit_button, 'info')
        self.edit_button.clicked.connect(self.edit_client)
        self.edit_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        # زر الحذف
        self.delete_button = QPushButton("🗑️ حذف")
        self.style_advanced_button(self.delete_button, 'danger')
        self.delete_button.clicked.connect(self.delete_client)
        self.delete_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        # زر التحديث
        self.refresh_button = QPushButton("🔄 تحديث")
        self.style_advanced_button(self.refresh_button, 'modern_teal')
        self.refresh_button.clicked.connect(self.refresh_data)
        self.refresh_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        # زر عرض التفاصيل
        self.view_button = QPushButton("👁️ عرض التفاصيل")
        self.style_advanced_button(self.view_button, 'cyan')
        self.view_button.clicked.connect(self.view_client)
        self.view_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        # زر تعديل المبلغ (إضافة أو تقليل)
        self.edit_amount_button = QPushButton("💰 تعديل المبلغ")
        self.style_advanced_button(self.edit_amount_button, 'orange')
        self.edit_amount_button.clicked.connect(self.edit_amount)
        self.edit_amount_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        # زر إدارة الوثائق
        self.documents_button = QPushButton("📁 إدارة الوثائق")
        self.style_advanced_button(self.documents_button, 'gray')
        # سيتم ربط الدالة لاحقاً بعد تحميل البيانات
        self.documents_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        # زر التصدير
        self.export_button = QPushButton("📤 تصدير ▼")
        self.style_advanced_button(self.export_button, 'black', has_menu=True)
        self.export_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        # إنشاء قائمة التصدير مع تحديد العرض والموضع
        export_menu = QMenu(self)

        # تحديد عرض القائمة مع نفس ألوان وخلفية نافذة الإحصائيات
        export_menu.setStyleSheet("""
            QMenu {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #334155,
                    stop:0.3 #475569, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                    stop:0.6 #60A5FA, stop:0.7 #8B5CF6, stop:0.8 #7C3AED,
                    stop:0.9 #6D28D9, stop:1 #5B21B6);
                border: none;
                border-radius: 12px;
                padding: 8px;
                color: #ffffff;
                font-family: 'Segoe UI', 'Tahoma', 'Arial', sans-serif;
                font-weight: 900;
                font-size: 13px;
                box-shadow: 0 10px 25px rgba(0, 0, 0, 0.4),
                           0 5px 15px rgba(59, 130, 246, 0.2),
                           inset 0 1px 0 rgba(255, 255, 255, 0.1);
                min-width: 240px;
            }
            QMenu::item {
                background: transparent;
                padding: 10px 5px 10px 80px;
                margin: 2px;
                border: none;
                border-radius: 8px;
                color: #ffffff;
                font-weight: 700;
                font-size: 13px;
                text-align: left;
                min-height: 20px;
                text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.7);
                padding-left: 90px;
            }
            QMenu::item:selected {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(59, 130, 246, 0.8),
                    stop:0.3 rgba(96, 165, 250, 0.7),
                    stop:0.7 rgba(139, 92, 246, 0.6),
                    stop:1 rgba(124, 58, 237, 0.7));
                color: #ffffff;
                font-weight: 900;
                border: 1px solid rgba(255, 255, 255, 0.4);
                border-radius: 8px;
                box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4),
                           0 0 15px rgba(96, 165, 250, 0.3),
                           inset 0 1px 0 rgba(255, 255, 255, 0.2);
                text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
                transform: scale(1.02);
            }
            QMenu::item:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(96, 165, 250, 0.3),
                    stop:0.5 rgba(139, 92, 246, 0.25),
                    stop:1 rgba(124, 58, 237, 0.3));
                border: 1px solid rgba(255, 255, 255, 0.3);
                border-radius: 8px;
                box-shadow: 0 2px 8px rgba(96, 165, 250, 0.3);
                text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.6);
            }
            QMenu::separator {
                height: 2px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 rgba(255, 255, 255, 0.2),
                    stop:0.5 rgba(96, 165, 250, 0.4),
                    stop:1 rgba(255, 255, 255, 0.2));
                margin: 6px 12px;
                border: none;
                border-radius: 1px;
            }
        """)

        # قسم التصدير الأساسي
        excel_action = QAction("📊 تصدير Excel متقدم", self)
        excel_action.triggered.connect(self.export_to_excel_advanced)
        export_menu.addAction(excel_action)

        csv_action = QAction("📄 تصدير CSV شامل", self)
        csv_action.triggered.connect(self.export_to_csv_advanced)
        export_menu.addAction(csv_action)

        # فاصل
        export_menu.addSeparator()

        # قسم التقارير المتقدمة
        detailed_action = QAction("📊 تقرير تفصيلي", self)
        detailed_action.triggered.connect(self.export_detailed_report)
        export_menu.addAction(detailed_action)

        balance_action = QAction("💰 تقرير الأرصدة", self)
        balance_action.triggered.connect(self.export_balance_report)
        export_menu.addAction(balance_action)

        # فاصل
        export_menu.addSeparator()

        # قسم التصدير المخصص
        custom_action = QAction("⚙️ تصدير مخصص", self)
        custom_action.triggered.connect(self.export_custom)
        export_menu.addAction(custom_action)

        backup_action = QAction("💾 إنشاء نسخة احتياطية", self)
        backup_action.triggered.connect(self.export_backup)
        export_menu.addAction(backup_action)

        restore_action = QAction("📥 استعادة نسخة احتياطية", self)
        restore_action.triggered.connect(self.restore_backup)
        export_menu.addAction(restore_action)

        # تخصيص موضع وعرض القائمة
        def show_export_menu():
            """عرض قائمة التصدير فوق الزر مباشرة بنفس العرض"""
            # الحصول على موضع الزر (فوق الزر)
            button_pos = self.export_button.mapToGlobal(self.export_button.rect().topLeft())

            # تحديد عرض القائمة وتقليل العرض من اليمين واليسار
            button_width = self.export_button.width()
            export_menu.setFixedWidth(max(button_width, 180))

            # ترحيل القائمة لليمين درجة واحدة
            button_pos.setX(button_pos.x() + 20)

            # حساب ارتفاع القائمة لرفعها فوق الزر
            menu_height = export_menu.sizeHint().height()
            button_pos.setY(button_pos.y() - menu_height)

            # عرض القائمة في الموضع المحدد
            export_menu.exec_(button_pos)

        # ربط الزر بالدالة المخصصة بدلاً من setMenu
        self.export_button.clicked.connect(show_export_menu)

        # زر الاتصالات (واتساب)
        self.whatsapp_button = QPushButton("📞 إتصال واتساب")
        self.style_advanced_button(self.whatsapp_button, 'gold')
        # تكبير خط زر الاتصال درجة واحدة وتوحيد لون النص
        current_style = self.whatsapp_button.styleSheet()
        enhanced_style = current_style.replace("font-size: 14px", "font-size: 15px")
        enhanced_style = enhanced_style.replace("color: #1a1a1a", "color: #ffffff")
        enhanced_style = enhanced_style.replace("color: #2c1810", "color: #ffffff")
        self.whatsapp_button.setStyleSheet(enhanced_style)
        self.whatsapp_button.clicked.connect(self.show_whatsapp_options)
        self.whatsapp_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        # زر الإحصائيات
        self.statistics_button = QPushButton("📊 الإحصائيات")
        self.style_advanced_button(self.statistics_button, 'rose')
        self.statistics_button.clicked.connect(self.show_statistics)
        self.statistics_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        # زر إخفاء/إظهار الأعمدة
        self.columns_visibility_button = QPushButton("👁️ إدارة الأعمدة")
        self.style_advanced_button(self.columns_visibility_button, 'indigo')
        self.columns_visibility_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        # إنشاء قائمة إدارة الأعمدة
        self.create_columns_visibility_menu()

        # إضافة الأزرار للتخطيط
        actions_layout.addWidget(self.add_button)
        actions_layout.addWidget(self.edit_button)
        actions_layout.addWidget(self.delete_button)
        actions_layout.addWidget(self.refresh_button)
        actions_layout.addWidget(self.view_button)
        actions_layout.addWidget(self.edit_amount_button)
        actions_layout.addWidget(self.documents_button)
        actions_layout.addWidget(self.whatsapp_button)
        actions_layout.addWidget(self.statistics_button)
        actions_layout.addWidget(self.export_button)
        actions_layout.addWidget(self.columns_visibility_button)

        # إنشاء حاوي عمودي للتوسيط الحقيقي
        bottom_container = QVBoxLayout()
        bottom_container.setContentsMargins(6, 0, 6, 0)
        bottom_container.setSpacing(0)

        # إضافة مساحة فارغة أعلى للتوسيط
        bottom_container.addStretch(1)

        # إضافة التخطيط الأفقي في المنتصف
        bottom_container.addLayout(actions_layout)

        # إضافة مساحة فارغة أسفل للتوسيط
        bottom_container.addStretch(1)

        # تعيين التخطيط للإطار السفلي
        bottom_frame.setLayout(bottom_container)

        main_layout.addWidget(bottom_frame)

        self.setLayout(main_layout)

        # تهيئة حالة الأزرار (الأزرار الأساسية مفعلة، أزرار التحديد معطلة)
        self.initialize_button_states()

        # ربط دالة إدارة الوثائق بعد إنشاء جميع العناصر
        self.documents_button.clicked.connect(self.manage_documents)

        # تأجيل تحميل البيانات لتحسين الأداء
        QTimer.singleShot(50, self.refresh_data)

    def initialize_button_states(self):
        """تهيئة حالة الأزرار عند البداية - جميع الأزرار منيرة ومفعلة"""
        try:
            print("🔧 بدء تهيئة حالة الأزرار...")

            # تفعيل جميع الأزرار وجعلها منيرة
            buttons = [
                (self.add_button, "➕ إضافة عميل"),
                (self.edit_button, "✏️ تعديل"),
                (self.delete_button, "🗑️ حذف"),
                (self.refresh_button, "🔄 تحديث"),
                (self.view_button, "👁️ عرض التفاصيل"),
                (self.edit_amount_button, "💰 تعديل المبلغ"),
                (self.documents_button, "📁 إدارة الوثائق"),
                (self.export_button, "📤 تصدير"),
                (self.whatsapp_button, "📞 واتساب"),
                (self.statistics_button, "📊 الإحصائيات"),
                (self.columns_visibility_button, "👁️ إدارة الأعمدة")
            ]

            for button, name in buttons:
                # تفعيل الزر
                button.setEnabled(True)

                # إزالة أي تأثيرات شفافية وجعل الزر منير
                current_style = button.styleSheet()
                # إزالة أي opacity موجودة
                clean_style = re.sub(r'opacity:\s*[\d.]+;?', '', current_style)
                # إضافة opacity كاملة لجعل الزر منير
                bright_style = clean_style + "\nQPushButton { opacity: 1.0 !important; }"
                button.setStyleSheet(bright_style)
                button.show()

                pass  # تم تنوير الزر بنجاح

            pass  # تم تهيئة جميع الأزرار بنجاح

        except Exception as e:
            # في حالة الخطأ، تفعيل جميع الأزرار بالطريقة التقليدية
            try:
                self.add_button.setEnabled(True)
                self.edit_button.setEnabled(True)
                self.delete_button.setEnabled(True)
                self.refresh_button.setEnabled(True)
                self.view_button.setEnabled(True)
                self.edit_amount_button.setEnabled(True)
                self.documents_button.setEnabled(True)
                self.export_button.setEnabled(True)
                self.whatsapp_button.setEnabled(True)
                self.statistics_button.setEnabled(True)
            except Exception as e2:
                pass  # خطأ في التفعيل التقليدي

    def create_custom_status_filter(self):
        """إنشاء قائمة تصفية مخصصة ومطورة بدون مشاكل"""
        # إنشاء إطار للقائمة المخصصة
        self.status_filter_frame = QFrame()
        self.status_filter_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.9),
                    stop:0.2 rgba(248, 250, 252, 0.95),
                    stop:0.4 rgba(241, 245, 249, 0.9),
                    stop:0.6 rgba(248, 250, 252, 0.95),
                    stop:0.8 rgba(255, 255, 255, 0.9),
                    stop:1 rgba(226, 232, 240, 0.85));
                border: 3px solid rgba(96, 165, 250, 0.8);
                border-radius: 15px;
                padding: 6px 15px;
                min-width: 500px;
                max-width: 500px;
                min-height: 33px;
                max-height: 37px;
                font-size: 16px;
                font-weight: 900;
                color: #1f2937;
                selection-background-color: rgba(96, 165, 250, 0.3);
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
                transition: all 0.3s ease;
            }
            QFrame:focus {
                border: 4px solid rgba(96, 165, 250, 0.95);
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(240, 249, 255, 0.95),
                    stop:0.2 rgba(224, 242, 254, 0.9),
                    stop:0.4 rgba(186, 230, 253, 0.85),
                    stop:0.6 rgba(224, 242, 254, 0.9),
                    stop:0.8 rgba(240, 249, 255, 0.95),
                    stop:1 rgba(255, 255, 255, 0.9));
                box-shadow: 0 4px 12px rgba(96, 165, 250, 0.3);
            }
            QFrame:hover {
                border: 4px solid rgba(96, 165, 250, 0.9);
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(250, 251, 255, 0.95),
                    stop:0.2 rgba(241, 245, 249, 0.9),
                    stop:0.4 rgba(226, 232, 240, 0.85),
                    stop:0.6 rgba(241, 245, 249, 0.9),
                    stop:0.8 rgba(250, 251, 255, 0.95),
                    stop:1 rgba(255, 255, 255, 0.9));
                transform: translateY(-1px);
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
            }
        """)

        # تخطيط أفقي للإطار
        filter_layout = QHBoxLayout()
        filter_layout.setContentsMargins(0, 0, 0, 0)
        filter_layout.setSpacing(0)

        # سهم يسار (مشابه للسهم الأيمن)
        self.left_arrow = QPushButton("▼")
        self.left_arrow.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(37, 99, 235, 0.8),
                    stop:0.2 rgba(59, 130, 246, 0.7),
                    stop:0.4 rgba(96, 165, 250, 0.6),
                    stop:0.6 rgba(139, 92, 246, 0.7),
                    stop:0.8 rgba(124, 58, 237, 0.8),
                    stop:1 rgba(109, 40, 217, 0.7));
                border: 2px solid rgba(96, 165, 250, 0.6);
                border-radius: 12px;
                color: #ffffff;
                font-size: 16px;
                font-weight: bold;
                min-width: 35px;
                max-width: 35px;
                min-height: 25px;
                max-height: 25px;
                padding: 0px;
                margin: 0px;
                text-align: center;
                box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
                transition: all 0.2s ease;
                text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(59, 130, 246, 0.9),
                    stop:0.2 rgba(96, 165, 250, 0.8),
                    stop:0.4 rgba(139, 92, 246, 0.7),
                    stop:0.6 rgba(124, 58, 237, 0.8),
                    stop:0.8 rgba(109, 40, 217, 0.9),
                    stop:1 rgba(91, 33, 182, 0.8));
                border: 3px solid rgba(96, 165, 250, 0.8);
                transform: translateY(-1px);
                box-shadow: 0 4px 10px rgba(96, 165, 250, 0.3);
            }
            QPushButton:pressed {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(29, 78, 216, 0.9),
                    stop:0.2 rgba(37, 99, 235, 0.8),
                    stop:0.4 rgba(59, 130, 246, 0.7),
                    stop:0.6 rgba(96, 165, 250, 0.8),
                    stop:0.8 rgba(124, 58, 237, 0.9),
                    stop:1 rgba(109, 40, 217, 0.8));
                border: 3px solid rgba(96, 165, 250, 0.95);
                transform: translateY(1px);
                box-shadow:
                    0 0 15px rgba(96, 165, 250, 0.4),
                    0 2px 8px rgba(0, 0, 0, 0.4),
                    inset 0 1px 2px rgba(255, 255, 255, 0.3);
            }
        """)

        # النص الحالي
        self.current_filter_label = QLabel("جميع الحالات")
        self.current_filter_label.setAlignment(Qt.AlignCenter)  # توسيط النص أفقياً وعمودياً
        self.current_filter_label.setStyleSheet("""
            QLabel {
                color: #1f2937;
                font-size: 16px;
                font-weight: 900;
                background: transparent;
                border: none;
                padding: 0px 12px;
                text-align: center;
                max-width: 435px;
                min-width: 435px;
                text-shadow: 0 1px 1px rgba(255, 255, 255, 0.5);
                cursor: pointer;
            }
        """)

        # زر القائمة (سهم يمين)
        self.filter_menu_button = QPushButton("▼")
        self.filter_menu_button.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(37, 99, 235, 0.8),
                    stop:0.2 rgba(59, 130, 246, 0.7),
                    stop:0.4 rgba(96, 165, 250, 0.6),
                    stop:0.6 rgba(139, 92, 246, 0.7),
                    stop:0.8 rgba(124, 58, 237, 0.8),
                    stop:1 rgba(109, 40, 217, 0.7));
                border: 2px solid rgba(96, 165, 250, 0.6);
                border-radius: 12px;
                color: #ffffff;
                font-size: 16px;
                font-weight: bold;
                min-width: 35px;
                max-width: 35px;
                min-height: 25px;
                max-height: 25px;
                padding: 0px;
                margin: 0px;
                text-align: center;
                box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
                transition: all 0.2s ease;
                text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(59, 130, 246, 0.9),
                    stop:0.2 rgba(96, 165, 250, 0.8),
                    stop:0.4 rgba(139, 92, 246, 0.7),
                    stop:0.6 rgba(124, 58, 237, 0.8),
                    stop:0.8 rgba(109, 40, 217, 0.9),
                    stop:1 rgba(91, 33, 182, 0.8));
                border: 3px solid rgba(96, 165, 250, 0.8);
                transform: translateY(-1px);
                box-shadow: 0 4px 10px rgba(96, 165, 250, 0.3);
            }
            QPushButton:pressed {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(29, 78, 216, 0.9),
                    stop:0.2 rgba(37, 99, 235, 0.8),
                    stop:0.4 rgba(59, 130, 246, 0.7),
                    stop:0.6 rgba(96, 165, 250, 0.8),
                    stop:0.8 rgba(124, 58, 237, 0.9),
                    stop:1 rgba(109, 40, 217, 0.8));
                border: 3px solid rgba(96, 165, 250, 0.95);
                transform: translateY(1px);
                box-shadow:
                    0 0 15px rgba(96, 165, 250, 0.4),
                    0 2px 8px rgba(0, 0, 0, 0.4),
                    inset 0 1px 2px rgba(255, 255, 255, 0.3);
            }
        """)

        # إضافة العناصر للتخطيط - سهم يسار، النص في المنتصف، سهم يمين
        filter_layout.addWidget(self.left_arrow, 0)
        filter_layout.addWidget(self.current_filter_label, 1)
        filter_layout.addWidget(self.filter_menu_button, 0)

        self.status_filter_frame.setLayout(filter_layout)

        # إنشاء القائمة المنسدلة المخصصة
        self.create_filter_menu()

        # ربط الأزرار بالقائمة
        self.filter_menu_button.clicked.connect(self.show_filter_menu)
        self.left_arrow.clicked.connect(self.show_filter_menu)

        # إضافة ميزة الضغط على أي مكان في الإطار
        self.status_filter_frame.mousePressEvent = self.frame_mouse_press_event
        self.current_filter_label.mousePressEvent = self.frame_mouse_press_event

        # جعل الإطار قابل للتركيز للحصول على تأثيرات أفضل
        self.status_filter_frame.setFocusPolicy(Qt.ClickFocus)

        # تعيين القيمة الافتراضية
        self.current_filter_value = None

        # استخدام الإطار كـ status_filter
        self.status_filter = self.status_filter_frame

    def create_filter_menu(self):
        """إنشاء قائمة التصفية المخصصة"""
        self.filter_menu = QMenu(self)
        self.filter_menu.setStyleSheet("""
            QMenu {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.9),
                    stop:0.2 rgba(248, 250, 252, 0.95),
                    stop:0.4 rgba(241, 245, 249, 0.9),
                    stop:0.6 rgba(248, 250, 252, 0.95),
                    stop:0.8 rgba(255, 255, 255, 0.9),
                    stop:1 rgba(226, 232, 240, 0.85));
                border: 3px solid rgba(96, 165, 250, 0.8);
                border-radius: 4px;
                padding: 8px;
                color: #1f2937;
                font-weight: 900;
                font-size: 16px;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
                min-width: 515px;
                max-width: 515px;
            }
            QMenu::item {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.9),
                    stop:0.2 rgba(248, 250, 252, 0.95),
                    stop:0.4 rgba(241, 245, 249, 0.9),
                    stop:0.6 rgba(248, 250, 252, 0.95),
                    stop:0.8 rgba(255, 255, 255, 0.9),
                    stop:1 rgba(226, 232, 240, 0.85));
                border: 2px solid rgba(96, 165, 250, 0.3);
                padding: 12px 0px;
                border-radius: 15px;
                margin: 3px;
                min-height: 32px;
                max-height: 32px;
                max-width: 495px;
                min-width: 495px;
                color: #1f2937;
                font-weight: 900;
                font-size: 17px;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
                text-align: center;
            }
            QMenu::item:selected {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(96, 165, 250, 0.4),
                    stop:0.2 rgba(139, 92, 246, 0.3),
                    stop:0.4 rgba(124, 58, 237, 0.25),
                    stop:0.6 rgba(139, 92, 246, 0.3),
                    stop:0.8 rgba(96, 165, 250, 0.4),
                    stop:1 rgba(59, 130, 246, 0.35));
                border: 3px solid rgba(96, 165, 250, 0.7);
                color: #1f2937;
                font-weight: 900;
                box-shadow:
                    0 4px 12px rgba(96, 165, 250, 0.3),
                    0 0 15px rgba(96, 165, 250, 0.2),
                    inset 0 1px 2px rgba(255, 255, 255, 0.5);
                transform: scale(1.02);
            }
            QMenu::item:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(250, 251, 255, 0.95),
                    stop:0.2 rgba(241, 245, 249, 0.9),
                    stop:0.4 rgba(226, 232, 240, 0.85),
                    stop:0.6 rgba(241, 245, 249, 0.9),
                    stop:0.8 rgba(250, 251, 255, 0.95),
                    stop:1 rgba(255, 255, 255, 0.9));
                border: 4px solid rgba(96, 165, 250, 0.9);
                color: #1f2937;
                font-weight: 900;
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
                transform: translateY(-1px);
            }
            QMenu::separator {
                height: 1px;
                background: rgba(96, 165, 250, 0.2);
                margin: 3px 15px;
                border: none;
            }
        """)

        # إضافة العناصر
        filter_options = [
            ("جميع الحالات", None),
            ("🟢 العملاء النشطين", "active"),
            ("🟡 العملاء العاديين", "normal"),
            ("🔴 العملاء المدينين", "debtor")
        ]

        for text, value in filter_options:
            # إنشاء عنصر مع توسيط النص المثالي
            centered_text = f"{text:^35}"  # توسيط النص في مساحة 35 حرف للعرض الجديد
            action = QAction(centered_text, self)
            action.triggered.connect(lambda checked, v=value, t=text: self.set_filter(v, t))
            self.filter_menu.addAction(action)

    def frame_mouse_press_event(self, event):
        """التعامل مع الضغط على أي مكان في الإطار"""
        if event.button() == Qt.LeftButton:
            # إضافة تأثير بصري للضغط
            self.status_filter_frame.setFocus()
            self.show_filter_menu()

    def show_filter_menu(self):
        """عرض قائمة التصفية"""
        # تحديد موقع القائمة تحت الإطار مباشرة
        frame_pos = self.status_filter_frame.mapToGlobal(self.status_filter_frame.rect().bottomLeft())
        self.filter_menu.exec_(frame_pos)

    def set_filter(self, value, text):
        """تعيين قيمة التصفية"""
        self.current_filter_value = value
        self.current_filter_label.setText(text)
        self.filter_clients()

    def create_columns_visibility_menu(self):
        """إنشاء قائمة إدارة إخفاء/إظهار الأعمدة"""
        # إنشاء قائمة إدارة الأعمدة
        columns_menu = QMenu(self)

        # تحديد عرض القائمة مع نفس ألوان وخلفية نافذة الإحصائيات
        columns_menu.setStyleSheet("""
            QMenu {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #334155,
                    stop:0.3 #475569, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                    stop:0.6 #60A5FA, stop:0.7 #8B5CF6, stop:0.8 #7C3AED,
                    stop:0.9 #6D28D9, stop:1 #5B21B6);
                border: none;
                border-radius: 8px;
                padding: 4px;
                color: #ffffff;
                font-family: 'Segoe UI', 'Tahoma', 'Arial', sans-serif;
                font-weight: 900;
                font-size: 14px;
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3),
                           0 2px 8px rgba(59, 130, 246, 0.2),
                           inset 0 1px 0 rgba(255, 255, 255, 0.1);
                min-width: 160px;
            }
            QMenu::item {
                background: transparent;
                padding: 6px 25px 6px 15px;
                margin: 1px;
                border: none;
                border-radius: 6px;
                color: #ffffff;
                font-weight: 700;
                font-size: 14px;
                text-align: left;
                min-height: 20px;
                min-width: 140px;
                text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.7);
                white-space: nowrap;
                overflow: visible;
            }
            QMenu::item:selected {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(59, 130, 246, 0.8),
                    stop:0.3 rgba(96, 165, 250, 0.7),
                    stop:0.7 rgba(139, 92, 246, 0.6),
                    stop:1 rgba(124, 58, 237, 0.7));
                color: #ffffff;
                font-weight: 900;
                border: 1px solid rgba(255, 255, 255, 0.4);
                border-radius: 8px;
                box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4),
                           0 0 15px rgba(96, 165, 250, 0.3),
                           inset 0 1px 0 rgba(255, 255, 255, 0.2);
                text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
            }
            QMenu::item:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(96, 165, 250, 0.3),
                    stop:0.5 rgba(139, 92, 246, 0.25),
                    stop:1 rgba(124, 58, 237, 0.3));
                border: 1px solid rgba(255, 255, 255, 0.3);
                border-radius: 8px;
                box-shadow: 0 2px 8px rgba(96, 165, 250, 0.3);
                text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.6);
            }
            QMenu::separator {
                height: 1px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 rgba(255, 255, 255, 0.2),
                    stop:0.5 rgba(96, 165, 250, 0.4),
                    stop:1 rgba(255, 255, 255, 0.2));
                margin: 3px 8px;
                border: none;
                border-radius: 1px;
            }
            QMenu::indicator {
                width: 16px;
                height: 16px;
                margin-left: 0px;
                margin-right: 8px;
                border: 2px solid rgba(255, 255, 255, 0.3);
                border-radius: 3px;
                background: transparent;
                subcontrol-position: right center;
                subcontrol-origin: padding;
            }
            QMenu::indicator:checked {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(34, 197, 94, 0.9),
                    stop:1 rgba(22, 163, 74, 0.9));
                border: 2px solid rgba(34, 197, 94, 0.8);
                image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTIiIGhlaWdodD0iMTIiIHZpZXdCb3g9IjAgMCAxMiAxMiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTEwIDNMNC41IDguNUwyIDYiIHN0cm9rZT0id2hpdGUiIHN0cm9rZS13aWR0aD0iMiIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIi8+Cjwvc3ZnPgo=);
            }
        """)

        # قائمة الأعمدة مع أيقوناتها
        self.column_headers = [
            ("🔢 ID", 0),
            ("🧑‍💼 اسم العميل", 1),
            ("🏠 العنوان", 2),
            ("📧 البريد الإلكتروني", 3),
            ("📱 رقم الهاتف", 4),
            ("💵 الرصيد", 5),
            ("⭐ حالة العميل", 6),
            ("📋 الملاحظات", 7),
            ("🗓️ تاريخ الإضافة", 8)
        ]

        # إضافة عناصر القائمة لكل عمود
        for header_text, column_index in self.column_headers:
            action = QAction(header_text, self)
            action.setCheckable(True)
            action.setChecked(True)  # جميع الأعمدة مرئية افتراضياً
            action.triggered.connect(lambda checked, col=column_index: self.toggle_column_visibility(col, checked))
            columns_menu.addAction(action)

        # إضافة فاصل
        columns_menu.addSeparator()

        # إضافة خيارات إضافية
        show_all_action = QAction("👁️ إظهار جميع الأعمدة", self)
        show_all_action.triggered.connect(self.show_all_columns)
        columns_menu.addAction(show_all_action)

        hide_all_action = QAction("🙈 إخفاء جميع الأعمدة", self)
        hide_all_action.triggered.connect(self.hide_all_columns)
        columns_menu.addAction(hide_all_action)

        # حفظ مرجع للقائمة
        self.columns_menu = columns_menu

        # تطبيق تصميم المحاذاة اليمنى للعناصر المحددة
        for action in columns_menu.actions():
            if action.property("align_right"):
                current_text = action.text()
                # إضافة مسافات لدفع النص لليمين نسبة بسيطة
                right_aligned_text = f"{current_text:>30}"
                action.setText(right_aligned_text)

        # تخصيص موضع وعرض القائمة
        def show_columns_menu():
            """عرض قائمة إدارة الأعمدة فوق الزر مباشرة بنفس العرض"""
            # الحصول على موضع الزر (فوق الزر)
            button_pos = self.columns_visibility_button.mapToGlobal(self.columns_visibility_button.rect().topLeft())

            # تحديد عرض القائمة لتكون مناسبة للنصوص
            button_width = self.columns_visibility_button.width()
            menu_width = max(button_width, 160)  # عرض أدنى 160 بكسل
            columns_menu.setFixedWidth(menu_width)

            # حساب ارتفاع القائمة لرفعها فوق الزر
            menu_height = columns_menu.sizeHint().height()
            button_pos.setY(button_pos.y() - menu_height)

            # عرض القائمة في الموضع المحدد
            columns_menu.exec_(button_pos)

        # ربط الزر بالدالة المخصصة
        self.columns_visibility_button.clicked.connect(show_columns_menu)

    def toggle_column_visibility(self, column_index, visible):
        """تبديل إظهار/إخفاء عمود محدد"""
        try:
            if hasattr(self, 'clients_table') and self.clients_table:
                if visible:
                    self.clients_table.showColumn(column_index)
                else:
                    self.clients_table.hideColumn(column_index)

                # تحديث حالة العنصر في القائمة
                for action in self.columns_menu.actions():
                    if action.data() == column_index:
                        action.setChecked(visible)
                        break

        except Exception as e:
            print(f"خطأ في تبديل إظهار العمود: {e}")

    def show_all_columns(self):
        """إظهار جميع الأعمدة"""
        try:
            if hasattr(self, 'clients_table') and self.clients_table:
                for i in range(self.clients_table.columnCount()):
                    self.clients_table.showColumn(i)

                # تحديث حالة جميع العناصر في القائمة
                for action in self.columns_menu.actions():
                    if action.isCheckable():
                        action.setChecked(True)

        except Exception as e:
            print(f"خطأ في إظهار جميع الأعمدة: {e}")

    def hide_all_columns(self):
        """إخفاء جميع الأعمدة"""
        try:
            if hasattr(self, 'clients_table') and self.clients_table:
                for i in range(self.clients_table.columnCount()):  # إخفاء جميع الأعمدة بما في ذلك ID
                    self.clients_table.hideColumn(i)

                # تحديث حالة جميع العناصر في القائمة
                for action in self.columns_menu.actions():
                    if action.isCheckable():
                        action.setChecked(False)

        except Exception as e:
            print(f"خطأ في إخفاء الأعمدة: {e}")

    def create_advanced_clients_table(self):
        """إنشاء جدول العملاء المتطور والنظيف"""
        # إنشاء الجدول
        self.clients_table = QTableWidget()
        self.clients_table.setColumnCount(9)

        # عناوين الأعمدة مع الأيقونات الجديدة (الترتيب الجديد مع الملاحظات والتاريخ في النهاية)
        headers = [
            "🔢 ID",
            "🧑‍💼 اسم العميل",
            "🏠 العنوان",
            "📧 البريد الإلكتروني",
            "📱 رقم الهاتف",
            "💵 الرصيد",
            "⭐ حالة العميل",
            "📋 الملاحظات",
            "🗓️ تاريخ الإضافة"
        ]

        self.clients_table.setHorizontalHeaderLabels(headers)

        # إعدادات الجدول مع التحديد المتعدد المتقدم
        self.clients_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.clients_table.setSelectionMode(QTableWidget.ExtendedSelection)  # تمكين التحديد المتعدد
        self.clients_table.setEditTriggers(QTableWidget.NoEditTriggers)
        self.clients_table.setAlternatingRowColors(False)
        self.clients_table.setSortingEnabled(True)

        # إعدادات الصفوف والأعمدة
        self.clients_table.verticalHeader().setDefaultSectionSize(50)
        self.clients_table.verticalHeader().setVisible(False)

        header = self.clients_table.horizontalHeader()
        header.setFixedHeight(60)
        header.setDefaultAlignment(Qt.AlignCenter)

        # إضافة خاصية التكيف التلقائي مطابقة للفواتير مع مقاسات مخصصة
        header.setSectionResizeMode(QHeaderView.Stretch)

        # تعيين مقاسات ثابتة لأعمدة محددة مع الحفاظ على التكيف التلقائي للباقي
        header.setSectionResizeMode(0, QHeaderView.Fixed)  # عمود ID
        header.setSectionResizeMode(1, QHeaderView.Fixed)  # عمود اسم العميل
        header.setSectionResizeMode(2, QHeaderView.Fixed)  # عمود العنوان
        header.setSectionResizeMode(3, QHeaderView.Fixed)  # عمود البريد الإلكتروني
        self.clients_table.setColumnWidth(0, 120)  # ID - 120 بكسل
        self.clients_table.setColumnWidth(1, 300)  # اسم العميل - 300 بكسل
        self.clients_table.setColumnWidth(2, 300)  # العنوان - 300 بكسل
        self.clients_table.setColumnWidth(3, 250)  # البريد الإلكتروني - 250 بكسل

        # إخفاء شريط التمرير المرئي مع الحفاظ على التمرير بالماوس
        self.clients_table.setVerticalScrollBarPolicy(Qt.ScrollBarAlwaysOff)

        # ضبط إعدادات التمرير للتحكم الدقيق
        try:
            scrollbar = self.clients_table.verticalScrollBar()
            if scrollbar:
                scrollbar.setSingleStep(50)  # ارتفاع الصف الواحد
                scrollbar.setPageStep(200)   # 4 صفوف للصفحة
        except Exception:
            pass

        # تطبيق التصميم
        self.apply_table_style()
        self.add_watermark_to_table()
        self.setup_table_interactions()

    def apply_table_style(self):
        """تطبيق التصميم المتطور للجدول"""
        self.clients_table.setStyleSheet("""
            QTableWidget {
                gridline-color: rgba(44, 62, 80, 0.2);
                background: #e2e8f0;
                border: 3px solid #000000;
                border-radius: 20px;
                font-family: 'Segoe UI', sans-serif;
                font-size: 14px;
                font-weight: 500;
                selection-background-color: rgba(102, 126, 234, 0.15);
                outline: none;
                padding: 5px;
            }
            QTableWidget::item {
                padding: 10px 12px;
                border: 2px solid rgba(102, 126, 234, 0.12);
                border-left: 5px solid rgba(102, 126, 234, 0.5);
                border-right: 5px solid rgba(102, 126, 234, 0.5);
                text-align: center;
                min-height: 30px;
                font-weight: bold;
                font-size: 14px;
                border-radius: 15px;
                margin: 3px;
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgba(248, 250, 252, 1.0),
                    stop:1 rgba(203, 213, 225, 1.0));
            }

            QTableWidget::item:selected {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(79, 70, 229, 0.9), stop:1 rgba(221, 214, 254, 0.9));
                color: white;
                border: 4px solid rgba(255, 255, 255, 0.9);
                border-left: 6px solid #fbbf24;
                border-right: 6px solid #fbbf24;
                border-radius: 18px;
                font-weight: bold;
                text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
            }
            QTableWidget::item:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgba(102, 126, 234, 0.15), stop:1 rgba(196, 181, 253, 0.3));
                border: 3px solid rgba(102, 126, 234, 0.7);
                border-left: 6px solid #06b6d4;
                border-right: 6px solid #06b6d4;
                border-radius: 16px;
                color: #0f172a;
                font-weight: bold;
                transform: translateY(-1px);
            }

            QHeaderView::section {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.2 #1E40AF, stop:0.4 #2563EB,
                    stop:0.6 #6366F1, stop:0.8 #6D28D9, stop:1 #4C1D95) !important;
                color: #FFFFFF !important;
                padding: 12px 16px !important;
                font-weight: 900 !important;
                font-size: 16px !important;
                font-family: 'Segoe UI', 'Roboto', 'Helvetica Neue', 'Arial', sans-serif !important;
                border: 3px solid rgba(255, 255, 255, 0.6) !important;
                border-radius: 12px 12px 0 0 !important;
                text-align: center !important;
                height: 55px !important;
                letter-spacing: 1.3px !important;
                text-transform: uppercase !important;
            }
            QHeaderView::section:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #1E293B, stop:0.2 #475569, stop:0.4 #2563EB,
                    stop:0.6 #6366F1, stop:0.8 #6D28D9, stop:1 #4C1D95) !important;
                transform: translateY(-2px) scale(1.02) !important;
                box-shadow: 0 6px 18px rgba(0, 0, 0, 0.5), 0 0 25px rgba(134, 158, 234, 0.4) !important;
                border: 4px solid rgba(255, 255, 255, 0.8) !important;
                text-shadow: 2px 2px 5px rgba(0, 0, 0, 0.9) !important;
                letter-spacing: 1.5px !important;
                font-weight: 900 !important;
            }
            QHeaderView::section:pressed {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.2 #334155, stop:0.4 #1E40AF,
                    stop:0.6 #2563EB, stop:0.8 #4C1D95, stop:1 #312E81) !important;
                transform: translateY(1px) scale(0.98) !important;
                box-shadow: inset 0 3px 6px rgba(0, 0, 0, 0.6) !important;
                border: 3px solid rgba(255, 255, 255, 0.9) !important;
                text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.9) !important;
                letter-spacing: 1.2px !important;
                font-weight: 900 !important;
            }
        """)

    def add_watermark_to_table(self):
        """إضافة علامة مائية للجدول"""
        def paint_watermark(painter, rect):
            painter.save()
            painter.setOpacity(0.08)
            painter.setPen(QColor(30, 41, 59))
            font = QFont("Segoe UI", 180, QFont.Bold)
            painter.setFont(font)
            center_rect = rect.adjusted(0, rect.height() // 4, 0, -rect.height() // 4)
            painter.drawText(center_rect, Qt.AlignCenter, "Smart Finish")
            painter.restore()

        original_paint = self.clients_table.paintEvent
        def new_paint_event(event):
            try:
                original_paint(event)
                painter = QPainter(self.clients_table.viewport())
                paint_watermark(painter, self.clients_table.viewport().rect())
                painter.end()
            except Exception:
                pass

        self.clients_table.paintEvent = new_paint_event

    def setup_table_interactions(self):
        """إعداد التفاعلات مع الجدول مع التحديد المتعدد المتقدم"""
        self.clients_table.cellDoubleClicked.connect(self.on_cell_double_clicked)

        # إضافة معالجة التحديد المتعدد المتقدم
        self.setup_advanced_multi_selection()

        def mousePressEvent(event):
            item = self.clients_table.itemAt(event.pos())
            if item is None:
                self.clients_table.clearSelection()
            QTableWidget.mousePressEvent(self.clients_table, event)

        self.clients_table.mousePressEvent = mousePressEvent

        # إضافة معالج التمرير المخصص (يحاكي سلوك الأسهم)
        def wheelEvent(event):
            try:
                # التمرير العمودي بالماوس
                delta = event.angleDelta().y()

                # تجاهل الحركات الصغيرة جداً
                if abs(delta) < 120:
                    event.accept()
                    return

                # الحصول على شريط التمرير
                scrollbar = self.clients_table.verticalScrollBar()
                if not scrollbar:
                    event.accept()
                    return

                # محاكاة سلوك الأسهم - خطوة واحدة في كل مرة
                if delta > 0:
                    # التمرير لأعلى - مثل الضغط على السهم العلوي
                    scrollbar.triggerAction(QAbstractSlider.SliderSingleStepSub)
                else:
                    # التمرير لأسفل - مثل الضغط على السهم السفلي
                    scrollbar.triggerAction(QAbstractSlider.SliderSingleStepAdd)

                event.accept()

            except Exception:
                # في حالة الخطأ، استخدم التمرير الافتراضي
                QTableWidget.wheelEvent(self.clients_table, event)

        self.clients_table.wheelEvent = wheelEvent

    def on_cell_double_clicked(self, row, column):
        """معالج النقر المزدوج على خلية"""
        try:
            self.edit_client()
        except Exception as e:
            print(f"خطأ في النقر المزدوج: {str(e)}")

    def setup_advanced_multi_selection(self):
        """إعداد التحديد المتعدد المتقدم مع اختصارات لوحة المفاتيح"""
        try:
            from PyQt5.QtCore import Qt
            from PyQt5.QtWidgets import QShortcut
            from PyQt5.QtGui import QKeySequence

            # التأكد من وجود قائمة العملاء المحددين
            if not hasattr(self, 'selected_clients'):
                self.selected_clients = []

            # اختصارات لوحة المفاتيح للتحديد المتعدد
            # Ctrl+A - تحديد الكل
            select_all_shortcut = QShortcut(QKeySequence.SelectAll, self.clients_table)
            select_all_shortcut.activated.connect(self.select_all_clients)

            # Ctrl+D - إلغاء تحديد الكل
            deselect_all_shortcut = QShortcut(QKeySequence("Ctrl+D"), self.clients_table)
            deselect_all_shortcut.activated.connect(self.deselect_all_clients)

            # Ctrl+I - عكس التحديد
            invert_selection_shortcut = QShortcut(QKeySequence("Ctrl+I"), self.clients_table)
            invert_selection_shortcut.activated.connect(self.invert_selection)

            # Delete - حذف العملاء المحددين
            delete_shortcut = QShortcut(QKeySequence.Delete, self.clients_table)
            delete_shortcut.activated.connect(self.delete_selected_clients)

            # إضافة معالج النقر بالزر الأيمن للقائمة السياقية
            self.clients_table.setContextMenuPolicy(Qt.CustomContextMenu)
            self.clients_table.customContextMenuRequested.connect(self.show_context_menu)

            # تحديث معالج تغيير التحديد
            try:
                self.clients_table.itemSelectionChanged.disconnect()  # إزالة الاتصال السابق
            except:
                pass  # لا يوجد اتصال سابق
            self.clients_table.itemSelectionChanged.connect(self.on_advanced_selection_changed)

        except Exception as e:
            print(f"خطأ في إعداد التحديد المتعدد: {e}")
            # في حالة الفشل، استخدام التحديد البسيط
            if not hasattr(self, 'selected_clients'):
                self.selected_clients = []

    def select_all_clients(self):
        """تحديد جميع العملاء في الجدول"""
        try:
            self.clients_table.selectAll()
            self.update_selected_clients_list()
            self.update_selection_status()
        except Exception as e:
            print(f"خطأ في تحديد جميع العملاء: {e}")

    def deselect_all_clients(self):
        """إلغاء تحديد جميع العملاء"""
        try:
            # التأكد من وجود قائمة العملاء المحددين
            if not hasattr(self, 'selected_clients'):
                self.selected_clients = []

            self.clients_table.clearSelection()
            self.selected_clients.clear()
            self.update_selection_status()
        except Exception as e:
            print(f"خطأ في إلغاء التحديد: {e}")

    def invert_selection(self):
        """عكس التحديد - تحديد غير المحدد وإلغاء تحديد المحدد"""
        try:
            total_rows = self.clients_table.rowCount()
            currently_selected = set()

            # الحصول على الصفوف المحددة حالياً
            for item in self.clients_table.selectedItems():
                currently_selected.add(item.row())

            # إلغاء التحديد الحالي
            self.clients_table.clearSelection()

            # تحديد الصفوف غير المحددة
            for row in range(total_rows):
                if row not in currently_selected:
                    self.clients_table.selectRow(row)

            self.update_selected_clients_list()
            self.update_selection_status()
        except Exception as e:
            print(f"خطأ في عكس التحديد: {e}")

    def on_advanced_selection_changed(self):
        """معالج متقدم لتغيير التحديد مع تحديث القوائم"""
        try:
            # التأكد من وجود قائمة العملاء المحددين
            if not hasattr(self, 'selected_clients'):
                self.selected_clients = []

            self.update_selected_clients_list()
            self.update_selection_status()
            self.update_button_states()
        except Exception as e:
            print(f"خطأ في معالجة تغيير التحديد: {e}")

    def update_selected_clients_list(self):
        """تحديث قائمة العملاء المحددين"""
        try:
            # التأكد من وجود قائمة العملاء المحددين
            if not hasattr(self, 'selected_clients'):
                self.selected_clients = []

            self.selected_clients.clear()
            selected_rows = set()

            # الحصول على الصفوف المحددة
            for item in self.clients_table.selectedItems():
                selected_rows.add(item.row())

            # إضافة معرفات العملاء المحددين
            for row in selected_rows:
                client_id_item = self.clients_table.item(row, 0)  # عمود ID
                if client_id_item:
                    try:
                        # استخراج الأرقام فقط من النص (إزالة الأيقونات)
                        id_text = client_id_item.text()
                        id_clean = ''.join(filter(str.isdigit, id_text))
                        if id_clean:
                            client_id = int(id_clean)
                            self.selected_clients.append(client_id)
                    except (ValueError, AttributeError):
                        continue

        except Exception as e:
            print(f"خطأ في تحديث قائمة العملاء المحددين: {e}")

    def update_selection_status(self):
        """تحديث حالة التحديد في شريط الحالة أو العنوان"""
        try:
            # التأكد من وجود قائمة العملاء المحددين
            if not hasattr(self, 'selected_clients'):
                self.selected_clients = []

            selected_count = len(self.selected_clients)
            total_count = self.clients_table.rowCount()

            if selected_count == 0:
                status_text = f"إجمالي العملاء: {total_count}"
            elif selected_count == 1:
                status_text = f"محدد: عميل واحد من {total_count}"
            else:
                status_text = f"محدد: {selected_count} عميل من {total_count}"

            # تحديث النص في تسمية الإجمالي
            if hasattr(self, 'total_clients_label'):
                current_text = self.total_clients_label.text()
                if " | " in current_text:
                    # الاحتفاظ بالجزء الثاني (القيمة الإجمالية) وتحديث الجزء الأول
                    parts = current_text.split(" | ")
                    if len(parts) >= 2:
                        self.total_clients_label.setText(f"{status_text} | {parts[1]}")
                    else:
                        self.total_clients_label.setText(status_text)
                else:
                    self.total_clients_label.setText(status_text)

        except Exception as e:
            print(f"خطأ في تحديث حالة التحديد: {e}")

    def show_context_menu(self, position):
        """عرض القائمة السياقية للتحديد المتعدد"""
        try:
            from PyQt5.QtWidgets import QMenu, QAction
            from PyQt5.QtCore import Qt

            # التأكد من وجود قائمة العملاء المحددين
            if not hasattr(self, 'selected_clients'):
                self.selected_clients = []

            # التحقق من وجود عنصر في الموضع المحدد
            item = self.clients_table.itemAt(position)
            if item is None:
                return

            # إنشاء القائمة السياقية
            context_menu = QMenu(self)
            context_menu.setStyleSheet("""
                QMenu {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                        stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #334155,
                        stop:0.3 #475569, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                        stop:0.6 #60A5FA, stop:0.7 #8B5CF6, stop:0.8 #7C3AED,
                        stop:0.9 #6D28D9, stop:1 #5B21B6);
                    border: none;
                    border-radius: 8px;
                    padding: 4px;
                    color: #ffffff;
                    font-family: 'Segoe UI', 'Tahoma', 'Arial', sans-serif;
                    font-weight: 700;
                    font-size: 13px;
                    min-width: 180px;
                }
                QMenu::item {
                    background: transparent;
                    padding: 8px 15px;
                    margin: 1px;
                    border: none;
                    border-radius: 6px;
                    color: #ffffff;
                    min-height: 20px;
                    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.7);
                }
                QMenu::item:selected {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 rgba(59, 130, 246, 0.8),
                        stop:0.5 rgba(139, 92, 246, 0.6),
                        stop:1 rgba(124, 58, 237, 0.7));
                    border: 1px solid rgba(255, 255, 255, 0.4);
                    border-radius: 6px;
                    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
                }
                QMenu::separator {
                    height: 1px;
                    background: rgba(255, 255, 255, 0.3);
                    margin: 3px 8px;
                }
            """)

            selected_count = len(self.selected_clients)

            if selected_count <= 1:
                # قائمة العميل الواحد
                edit_action = QAction("✏️ تعديل العميل", self)
                edit_action.triggered.connect(self.edit_client)
                context_menu.addAction(edit_action)

                view_action = QAction("👁️ عرض التفاصيل", self)
                view_action.triggered.connect(self.view_client_details)
                context_menu.addAction(view_action)

                context_menu.addSeparator()

                delete_action = QAction("🗑️ حذف العميل", self)
                delete_action.triggered.connect(self.delete_client)
                context_menu.addAction(delete_action)
            else:
                # قائمة التحديد المتعدد
                context_menu.addAction(f"📊 محدد: {selected_count} عميل")
                context_menu.addSeparator()

                export_selected_action = QAction("📤 تصدير المحددين", self)
                export_selected_action.triggered.connect(self.export_selected_clients)
                context_menu.addAction(export_selected_action)

                context_menu.addSeparator()

                delete_selected_action = QAction(f"🗑️ حذف {selected_count} عميل", self)
                delete_selected_action.triggered.connect(self.delete_selected_clients)
                context_menu.addAction(delete_selected_action)

            context_menu.addSeparator()

            # خيارات التحديد
            select_all_action = QAction("✅ تحديد الكل (Ctrl+A)", self)
            select_all_action.triggered.connect(self.select_all_clients)
            context_menu.addAction(select_all_action)

            deselect_all_action = QAction("❌ إلغاء التحديد (Ctrl+D)", self)
            deselect_all_action.triggered.connect(self.deselect_all_clients)
            context_menu.addAction(deselect_all_action)

            invert_action = QAction("🔄 عكس التحديد (Ctrl+I)", self)
            invert_action.triggered.connect(self.invert_selection)
            context_menu.addAction(invert_action)

            # عرض القائمة
            context_menu.exec_(self.clients_table.mapToGlobal(position))

        except Exception as e:
            print(f"خطأ في عرض القائمة السياقية: {e}")

    def delete_selected_clients(self):
        """حذف العملاء المحددين مع تأكيد"""
        try:
            # التأكد من وجود قائمة العملاء المحددين
            if not hasattr(self, 'selected_clients'):
                self.selected_clients = []

            if not self.selected_clients:
                show_info_message("تنبيه", "لم يتم تحديد أي عملاء للحذف")
                return

            selected_count = len(self.selected_clients)

            # رسالة تأكيد مخصصة للتحديد المتعدد
            if selected_count == 1:
                message = "هل أنت متأكد من حذف العميل المحدد؟"
            else:
                message = f"هل أنت متأكد من حذف {selected_count} عميل محدد؟\n\nهذا الإجراء لا يمكن التراجع عنه!"

            reply = show_confirmation_message("تأكيد الحذف", message)

            if reply:
                deleted_count = 0
                failed_deletions = []

                for client_id in self.selected_clients:
                    try:
                        client = self.session.query(Client).get(client_id)
                        if client:
                            self.session.delete(client)
                            deleted_count += 1
                        else:
                            failed_deletions.append(f"العميل {client_id} غير موجود")
                    except Exception as e:
                        failed_deletions.append(f"العميل {client_id}: {str(e)}")

                try:
                    self.session.commit()

                    # رسالة النتيجة
                    if deleted_count > 0:
                        if failed_deletions:
                            message = f"تم حذف {deleted_count} عميل بنجاح\n\nفشل في حذف:\n" + "\n".join(failed_deletions)
                            show_info_message("نتيجة الحذف", message)
                        else:
                            show_info_message("تم", f"تم حذف {deleted_count} عميل بنجاح")
                    else:
                        show_error_message("خطأ", "فشل في حذف أي عميل")

                    # تحديث الجدول وإعادة تعيين التحديد
                    self.load_clients()
                    self.selected_clients.clear()

                except Exception as e:
                    self.session.rollback()
                    show_error_message("خطأ", f"فشل في حفظ التغييرات: {str(e)}")

        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ أثناء الحذف: {str(e)}")

    def export_selected_clients(self):
        """تصدير العملاء المحددين فقط"""
        try:
            # التأكد من وجود قائمة العملاء المحددين
            if not hasattr(self, 'selected_clients'):
                self.selected_clients = []

            if not self.selected_clients:
                show_info_message("تنبيه", "لم يتم تحديد أي عملاء للتصدير")
                return

            from PyQt5.QtWidgets import QFileDialog
            from datetime import datetime
            import csv

            # اختيار مكان الحفظ
            file_path, _ = QFileDialog.getSaveFileName(
                self, "تصدير العملاء المحددين",
                f"عملاء_محددين_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv",
                "CSV Files (*.csv)"
            )

            if file_path:
                # الحصول على بيانات العملاء المحددين
                selected_clients_data = []
                for client_id in self.selected_clients:
                    client = self.session.query(Client).get(client_id)
                    if client:
                        selected_clients_data.append(client)

                # كتابة البيانات
                with open(file_path, 'w', newline='', encoding='utf-8-sig') as csvfile:
                    writer = csv.writer(csvfile)

                    # العنوان
                    writer.writerow([f'العملاء المحددين - {len(selected_clients_data)} عميل'])
                    writer.writerow([f'تاريخ التصدير: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}'])
                    writer.writerow([])

                    # رؤوس الأعمدة
                    writer.writerow(['ID', 'الاسم', 'العنوان', 'البريد الإلكتروني', 'الهاتف', 'الملاحظات', 'تاريخ الإضافة'])

                    # البيانات
                    for client in selected_clients_data:
                        writer.writerow([
                            client.id,
                            client.name or '',
                            client.address or '',
                            client.email or '',
                            client.phone or '',
                            client.notes or '',
                            client.created_at.strftime('%Y-%m-%d %H:%M:%S') if client.created_at else ''
                        ])

                show_info_message("تم", f"تم تصدير {len(selected_clients_data)} عميل بنجاح إلى:\n{file_path}")

        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ أثناء التصدير: {str(e)}")

    def edit_client(self):
        """تعديل عميل عند النقر المزدوج"""
        try:
            client_id = self.get_selected_client_id()
            if client_id:
                # الحصول على بيانات العميل من قاعدة البيانات
                client = self.session.query(Client).get(client_id)
                if client:
                    # فتح نافذة التعديل (نفس نافذة الإضافة مع تحميل البيانات)
                    dialog = AddClientDialog(self.session, self, client)
                    if dialog.exec_() == QDialog.Accepted:
                        self.refresh_data()  # تحديث الجدول بعد التعديل
                else:
                    QMessageBox.warning(self, "خطأ", "لم يتم العثور على العميل المحدد")
            else:
                QMessageBox.warning(self, "تحذير", "يرجى اختيار عميل للتعديل")
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ في تعديل العميل: {str(e)}")

    def get_selected_client_id(self):
        """الحصول على معرف العميل المحدد"""
        try:
            current_row = self.clients_table.currentRow()
            if current_row >= 0:
                client_id_item = self.clients_table.item(current_row, 0)
                if client_id_item:
                    # استخراج الرقم من النص الذي يحتوي على أيقونة (مثل "🆔 1")
                    text = client_id_item.text()
                    # البحث عن الرقم في النص
                    numbers = re.findall(r'\d+', text)
                    if numbers:
                        return int(numbers[0])
            return None
        except Exception as e:
            return None  # خطأ في الحصول على معرف العميل

    def refresh_data(self):
        """تحديث بيانات الجدول مع حماية من الضغط المتكرر"""
        try:
            # منع الضغط المتكرر على الزر
            if hasattr(self, '_is_refreshing') and self._is_refreshing:
                return

            # تعيين حالة التحديث
            self._is_refreshing = True
            print("🔄 تحديث بيانات العملاء...")

            # تعطيل زر التحديث مؤقتاً
            if hasattr(self, 'refresh_button'):
                self.refresh_button.setEnabled(False)
                self.refresh_button.setText("🔄 جاري التحديث...")

            # جلب جميع العملاء من قاعدة البيانات (من الأقدم للأحدث)
            clients = self.session.query(Client).order_by(Client.id.asc()).all()
            self.populate_table(clients)

            # إزالة تحديد الصف عند التحديث مثل باقي الأقسام
            self.clients_table.clearSelection()

        except Exception as e:
            print(f"❌ خطأ في تحميل البيانات: {str(e)}")
            show_error_message("خطأ", f"حدث خطأ في تحميل البيانات: {str(e)}")
        finally:
            # إعادة تفعيل زر التحديث وإعادة تعيين النص
            if hasattr(self, 'refresh_button'):
                self.refresh_button.setEnabled(True)
                self.refresh_button.setText("🔄 تحديث")

            # إعادة تعيين حالة التحديث
            self._is_refreshing = False

    def populate_table(self, clients):
        """ملء الجدول بالبيانات"""
        try:
            self.clients_table.setRowCount(len(clients))

            for row, client in enumerate(clients):
                # دالة مساعدة لإنشاء العناصر وتقليل التكرار
                def create_item(icon, text, default="No Data"):
                    display_text = text if text and text.strip() else default
                    item = QTableWidgetItem(f"{icon} {display_text}")
                    item.setTextAlignment(Qt.AlignCenter)
                    if display_text == default:
                        item.setForeground(QColor("#ef4444"))
                    return item

                # الرقم مع أيقونة حسب الرصيد - لون أسود للأرقام
                balance_value = client.balance or 0
                if balance_value > 0:
                    id_icon = "💰"
                elif balance_value < 0:
                    id_icon = "🔴"
                else:
                    id_icon = "🔢"

                # إنشاء عنصر ID مع لون أسود للرقم
                id_item = QTableWidgetItem(f"{id_icon} {client.id}")
                id_item.setTextAlignment(Qt.AlignCenter)
                id_item.setForeground(QColor("#000000"))  # لون أسود للرقم
                self.clients_table.setItem(row, 0, id_item)
                self.clients_table.setItem(row, 1, create_item("🧑‍💼", client.name))
                self.clients_table.setItem(row, 2, create_item("🏠", client.address))
                self.clients_table.setItem(row, 3, create_item("📧", client.email))
                self.clients_table.setItem(row, 4, create_item("📱", client.phone))

                # الرصيد مع ألوان حسب القيمة
                balance_text = format_currency(balance_value)
                if balance_value > 0:
                    balance_item = QTableWidgetItem(f"💰 {balance_text}")
                    balance_item.setForeground(QColor("#059669"))
                elif balance_value < 0:
                    balance_item = QTableWidgetItem(f"💸 {balance_text}")
                    balance_item.setForeground(QColor("#dc2626"))
                else:
                    balance_item = QTableWidgetItem(f"💵 {balance_text}")
                    balance_item.setForeground(QColor("#000000"))
                balance_item.setTextAlignment(Qt.AlignCenter)
                self.clients_table.setItem(row, 5, balance_item)

                # حالة العميل
                status_item = QTableWidgetItem(self.get_client_status(balance_value))
                status_item.setTextAlignment(Qt.AlignCenter)
                self.clients_table.setItem(row, 6, status_item)

                # الملاحظات والتاريخ
                notes_text = client.notes if hasattr(client, 'notes') and client.notes and client.notes.strip() else None
                date_text = client.created_at.strftime("%Y-%m-%d") if hasattr(client, 'created_at') and client.created_at else None

                self.clients_table.setItem(row, 7, create_item("📋", notes_text))
                self.clients_table.setItem(row, 8, create_item("🗓️", date_text))

        except Exception as e:
            print(f"خطأ في عرض البيانات: {str(e)}")

    def get_client_status(self, balance):
        """تحديد حالة العميل بناءً على الرصيد"""
        if balance > 0:
            return "🟢 نشط"
        elif balance == 0:
            return "🟡 عادي"
        else:
            return "🔴 مدين"

    def filter_clients(self):
        """تصفية العملاء بناءً على نص البحث والحالة"""
        try:
            search_text = self.search_edit.text().strip().lower()
            status = getattr(self, 'current_filter_value', None)

            # بناء الاستعلام
            query = self.session.query(Client)

            # تطبيق تصفية النص
            if search_text:
                query = query.filter(
                    Client.name.like(f"%{search_text}%") |
                    Client.phone.like(f"%{search_text}%") |
                    Client.email.like(f"%{search_text}%") |
                    Client.address.like(f"%{search_text}%")
                )

            # تطبيق تصفية الحالة
            if status == "active":
                query = query.filter(Client.balance > 0)
            elif status == "normal":
                query = query.filter(Client.balance == 0)
            elif status == "debtor":
                query = query.filter(Client.balance < 0)

            # تنفيذ الاستعلام (من الأقدم للأحدث)
            clients = query.order_by(Client.id.asc()).all()

            # تحديث الجدول
            self.populate_table(clients)

        except Exception as e:
            print(f"خطأ في تصفية البيانات: {str(e)}")

    def update_button_states(self):
        """تحديث حالة الأزرار حسب التحديد مع دعم التحديد المتعدد"""
        try:
            # التأكد من وجود قائمة العملاء المحددين
            if not hasattr(self, 'selected_clients'):
                self.selected_clients = []

            selected_count = len(self.selected_clients)
            has_selection = selected_count > 0
            has_single_selection = selected_count == 1
            has_multiple_selection = selected_count > 1

            # الأزرار التي تحتاج تحديد واحد فقط
            self.set_button_visibility(self.add_button, has_single_selection)
            self.set_button_visibility(self.edit_button, has_single_selection)
            self.set_button_visibility(self.view_button, has_single_selection)
            self.set_button_visibility(self.edit_amount_button, has_single_selection)
            self.set_button_visibility(self.documents_button, has_single_selection)
            self.set_button_visibility(self.whatsapp_button, has_single_selection)

            # الأزرار التي تعمل مع التحديد المتعدد
            self.set_button_visibility(self.delete_button, has_selection)

            # تحديث نص زر الحذف حسب عدد المحددين
            if has_multiple_selection:
                self.delete_button.setText(f"🗑️ حذف ({selected_count})")
            else:
                self.delete_button.setText("🗑️ حذف")

            # الأزرار المتاحة دائماً مع تأثير الظهور
            self.set_button_visibility(self.refresh_button, True)
            self.set_button_visibility(self.export_button, True)
            self.set_button_visibility(self.statistics_button, True)
        except Exception as e:
            # في حالة الخطأ، تفعيل جميع الأزرار
            try:
                self.add_button.setEnabled(True)
                self.edit_button.setEnabled(True)
                self.delete_button.setEnabled(True)
                self.refresh_button.setEnabled(True)
                self.view_button.setEnabled(True)
                self.edit_amount_button.setEnabled(True)
                self.documents_button.setEnabled(True)
                self.export_button.setEnabled(True)
                self.whatsapp_button.setEnabled(True)
                self.statistics_button.setEnabled(True)
            except:
                pass

    def set_button_visibility(self, button, enabled):
        """تعيين حالة الزر مع تأثير الظهور/الاختفاء السلس"""
        try:
            button.setEnabled(enabled)

            if enabled:
                # إظهار الزر بشفافية كاملة
                print(f"🟢 تفعيل الزر: {button.text()}")
                # إزالة أي تأثيرات شفافية سابقة وتطبيق الشفافية الكاملة
                current_style = button.styleSheet()
                # إزالة أي opacity موجودة
                clean_style = re.sub(r'opacity:\s*[\d.]+;?', '', current_style)
                # إضافة opacity كاملة
                new_style = clean_style + "\nQPushButton { opacity: 1.0; }"
                button.setStyleSheet(new_style)
                button.show()
            else:
                # تقليل شفافية الزر (لا نخفيه تماماً)
                print(f"🔴 تعطيل الزر: {button.text()}")
                current_style = button.styleSheet()
                # إزالة أي opacity موجودة
                clean_style = re.sub(r'opacity:\s*[\d.]+;?', '', current_style)
                # إضافة opacity منخفضة
                new_style = clean_style + "\nQPushButton { opacity: 0.3; }"
                button.setStyleSheet(new_style)

        except Exception as e:
            # في حالة الخطأ، استخدم الطريقة التقليدية
            button.setEnabled(enabled)

    def style_advanced_button(self, button, button_type, has_menu=False):
        """تطبيق تصميم متطور وجذاب على الأزرار مع ألوان متنوعة ومميزة"""
        try:
            # تحديد الألوان المتنوعة والمميزة حسب نوع الزر
            colors = {
                'primary': {
                    'bg_start': '#1a1a2e', 'bg_mid': '#16213e', 'bg_end': '#0f3460', 'bg_bottom': '#533483',
                    'hover_start': '#2a2a3e', 'hover_mid': '#26314e', 'hover_end': '#1f4470', 'hover_bottom': '#634493',
                    'hover_border': '#4f46e5', 'pressed_start': '#0a0a1e', 'pressed_mid': '#06112e',
                    'pressed_end': '#052450', 'pressed_bottom': '#332473', 'pressed_border': '#3730a3',
                    'border': '#4f46e5', 'text': '#ffffff', 'shadow': 'rgba(79, 70, 229, 0.5)'
                },
                'emerald': {
                    'bg_start': '#064e3b', 'bg_mid': '#047857', 'bg_end': '#065f46', 'bg_bottom': '#10b981',
                    'hover_start': '#10b981', 'hover_mid': '#34d399', 'hover_end': '#6ee7b7', 'hover_bottom': '#a7f3d0',
                    'hover_border': '#10b981', 'pressed_start': '#022c22', 'pressed_mid': '#064e3b',
                    'pressed_end': '#014737', 'pressed_bottom': '#047857', 'pressed_border': '#065f46',
                    'border': '#10b981', 'text': '#ffffff', 'shadow': 'rgba(16, 185, 129, 0.6)'
                },
                'danger': {
                    'bg_start': '#7f1d1d', 'bg_mid': '#991b1b', 'bg_end': '#b91c1c', 'bg_bottom': '#dc2626',
                    'hover_start': '#dc2626', 'hover_mid': '#ef4444', 'hover_end': '#f87171', 'hover_bottom': '#fca5a5',
                    'hover_border': '#dc2626', 'pressed_start': '#450a0a', 'pressed_mid': '#7f1d1d',
                    'pressed_end': '#991b1b', 'pressed_bottom': '#b91c1c', 'pressed_border': '#991b1b',
                    'border': '#dc2626', 'text': '#ffffff', 'shadow': 'rgba(220, 38, 38, 0.6)'
                },
                'info': {
                    'bg_start': '#0c4a6e', 'bg_mid': '#075985', 'bg_end': '#0369a1', 'bg_bottom': '#0284c7',
                    'hover_start': '#0284c7', 'hover_mid': '#0ea5e9', 'hover_end': '#38bdf8', 'hover_bottom': '#7dd3fc',
                    'hover_border': '#0ea5e9', 'pressed_start': '#082f49', 'pressed_mid': '#0c4a6e',
                    'pressed_end': '#075985', 'pressed_bottom': '#0369a1', 'pressed_border': '#075985',
                    'border': '#0ea5e9', 'text': '#ffffff', 'shadow': 'rgba(14, 165, 233, 0.6)'
                },
                'modern_teal': {
                    'bg_start': '#042f2e', 'bg_mid': '#134e4a', 'bg_end': '#0f766e', 'bg_bottom': '#0d9488',
                    'hover_start': '#0d9488', 'hover_mid': '#14b8a6', 'hover_end': '#2dd4bf', 'hover_bottom': '#5eead4',
                    'hover_border': '#14b8a6', 'pressed_start': '#042f2e', 'pressed_mid': '#134e4a',
                    'pressed_end': '#0f766e', 'pressed_bottom': '#0d9488', 'pressed_border': '#0f766e',
                    'border': '#14b8a6', 'text': '#ffffff', 'shadow': 'rgba(20, 184, 166, 0.6)'
                },
                'cyan': {
                    'bg_start': '#083344', 'bg_mid': '#164e63', 'bg_end': '#0e7490', 'bg_bottom': '#0891b2',
                    'hover_start': '#0891b2', 'hover_mid': '#06b6d4', 'hover_end': '#22d3ee', 'hover_bottom': '#67e8f9',
                    'hover_border': '#06b6d4', 'pressed_start': '#083344', 'pressed_mid': '#164e63',
                    'pressed_end': '#0e7490', 'pressed_bottom': '#0891b2', 'pressed_border': '#0e7490',
                    'border': '#06b6d4', 'text': '#ffffff', 'shadow': 'rgba(6, 182, 212, 0.6)'
                },
                'rose': {
                    'bg_start': '#500724', 'bg_mid': '#831843', 'bg_end': '#9d174d', 'bg_bottom': '#be185d',
                    'hover_start': '#be185d', 'hover_mid': '#ec4899', 'hover_end': '#f472b6', 'hover_bottom': '#f9a8d4',
                    'hover_border': '#ec4899', 'pressed_start': '#500724', 'pressed_mid': '#831843',
                    'pressed_end': '#9d174d', 'pressed_bottom': '#be185d', 'pressed_border': '#9d174d',
                    'border': '#ec4899', 'text': '#ffffff', 'shadow': 'rgba(236, 72, 153, 0.6)'
                },
                'indigo': {
                    'bg_start': '#1e1b4b', 'bg_mid': '#312e81', 'bg_end': '#3730a3', 'bg_bottom': '#4338ca',
                    'hover_start': '#4338ca', 'hover_mid': '#6366f1', 'hover_end': '#818cf8', 'hover_bottom': '#a5b4fc',
                    'hover_border': '#6366f1', 'pressed_start': '#1e1b4b', 'pressed_mid': '#312e81',
                    'pressed_end': '#3730a3', 'pressed_bottom': '#4338ca', 'pressed_border': '#3730a3',
                    'border': '#6366f1', 'text': '#ffffff', 'shadow': 'rgba(99, 102, 241, 0.6)'
                },
                'orange': {
                    'bg_start': '#431407', 'bg_mid': '#7c2d12', 'bg_end': '#9a3412', 'bg_bottom': '#c2410c',
                    'hover_start': '#c2410c', 'hover_mid': '#ea580c', 'hover_end': '#f97316', 'hover_bottom': '#fb923c',
                    'hover_border': '#ea580c', 'pressed_start': '#431407', 'pressed_mid': '#7c2d12',
                    'pressed_end': '#9a3412', 'pressed_bottom': '#c2410c', 'pressed_border': '#9a3412',
                    'border': '#ea580c', 'text': '#ffffff', 'shadow': 'rgba(234, 88, 12, 0.6)'
                },
                'purple': {
                    'bg_start': '#581c87', 'bg_mid': '#7c3aed', 'bg_end': '#8b5cf6', 'bg_bottom': '#a855f7',
                    'hover_start': '#a855f7', 'hover_mid': '#c084fc', 'hover_end': '#d8b4fe', 'hover_bottom': '#e9d5ff',
                    'hover_border': '#c084fc', 'pressed_start': '#3b0764', 'pressed_mid': '#581c87',
                    'pressed_end': '#6b21a8', 'pressed_bottom': '#7c3aed', 'pressed_border': '#6b21a8',
                    'border': '#a855f7', 'text': '#ffffff', 'shadow': 'rgba(168, 85, 247, 0.6)'
                },
                'gray': {
                    'bg_start': '#2d3748', 'bg_mid': '#4a5568', 'bg_end': '#718096', 'bg_bottom': '#a0aec0',
                    'hover_start': '#a0aec0', 'hover_mid': '#cbd5e0', 'hover_end': '#e2e8f0', 'hover_bottom': '#f7fafc',
                    'hover_border': '#cbd5e0', 'pressed_start': '#1a202c', 'pressed_mid': '#2d3748',
                    'pressed_end': '#4a5568', 'pressed_bottom': '#718096', 'pressed_border': '#4a5568',
                    'border': '#718096', 'text': '#ffffff', 'shadow': 'rgba(113, 128, 150, 0.8)'
                },
                'gold': {
                    'bg_start': '#8b6914', 'bg_mid': '#b8860b', 'bg_end': '#cd853f', 'bg_bottom': '#daa520',
                    'hover_start': '#daa520', 'hover_mid': '#f4a460', 'hover_end': '#ffd700', 'hover_bottom': '#ffdf00',
                    'hover_border': '#ffd700', 'pressed_start': '#6b5b0d', 'pressed_mid': '#8b6914',
                    'pressed_end': '#b8860b', 'pressed_bottom': '#cd853f', 'pressed_border': '#b8860b',
                    'border': '#ffd700', 'text': '#2c1810', 'shadow': 'rgba(255, 215, 0, 0.9)'
                },
                'beige': {
                    'bg_start': '#f5f5dc', 'bg_mid': '#f0e68c', 'bg_end': '#ddbf94', 'bg_bottom': '#d2b48c',
                    'hover_start': '#faf0e6', 'hover_mid': '#f5f5dc', 'hover_end': '#f0e68c', 'hover_bottom': '#ddbf94',
                    'hover_border': '#cd853f', 'pressed_start': '#ddbf94', 'pressed_mid': '#d2b48c',
                    'pressed_end': '#bc9a6a', 'pressed_bottom': '#a0826d', 'pressed_border': '#bc9a6a',
                    'border': '#d2b48c', 'text': '#2f1b14', 'shadow': 'rgba(210, 180, 140, 0.8)'
                },
                'ink': {
                    'bg_start': '#4a5568', 'bg_mid': '#5a6478', 'bg_end': '#6b7388', 'bg_bottom': '#7c8298',
                    'hover_start': '#7c8298', 'hover_mid': '#8d91a8', 'hover_end': '#9ea0b8', 'hover_bottom': '#afafc8',
                    'hover_border': '#8d91a8', 'pressed_start': '#394455', 'pressed_mid': '#4a5568',
                    'pressed_end': '#5a6478', 'pressed_bottom': '#6b7388', 'pressed_border': '#5a6478',
                    'border': '#6b7388', 'text': '#ffffff', 'shadow': 'rgba(107, 115, 136, 0.7)'
                },
                'galaxy': {
                    'bg_start': '#1a0f3d', 'bg_mid': '#2d1b69', 'bg_end': '#4c2a85', 'bg_bottom': '#6b39a1',
                    'hover_start': '#6b39a1', 'hover_mid': '#8a48bd', 'hover_end': '#a957d9', 'hover_bottom': '#c866f5',
                    'hover_border': '#c866f5', 'pressed_start': '#0d0620', 'pressed_mid': '#1a0f3d',
                    'pressed_end': '#2d1b69', 'pressed_bottom': '#4c2a85', 'pressed_border': '#2d1b69',
                    'border': '#c866f5', 'text': '#ffffff', 'shadow': 'rgba(200, 102, 245, 0.9)'
                },
                'black': {
                    'bg_start': '#000000', 'bg_mid': '#000000', 'bg_end': '#000000', 'bg_bottom': '#000000',
                    'hover_start': '#2d2d2d', 'hover_mid': '#404040', 'hover_end': '#525252', 'hover_bottom': '#666666',
                    'hover_border': '#808080', 'pressed_start': '#000000', 'pressed_mid': '#000000',
                    'pressed_end': '#000000', 'pressed_bottom': '#000000', 'pressed_border': '#000000',
                    'border': '#404040', 'text': '#ffffff', 'shadow': 'rgba(102, 102, 102, 0.8)'
                }
            }

            # الحصول على ألوان الزر المحدد
            color_scheme = colors.get(button_type, colors['primary'])

            # تحديد نمط القائمة المنسدلة إذا كان الزر يحتوي على قائمة
            menu_indicator = "::menu-indicator { width: 0px; }" if not has_menu else ""

            # تطبيق التصميم المتطور والأنيق مع ألوان جديدة وحفظ المقاسات
            style = f"""
                QPushButton {{
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 {color_scheme['bg_start']},
                        stop:0.15 {color_scheme['bg_mid']},
                        stop:0.85 {color_scheme['bg_end']},
                        stop:1 {color_scheme['bg_bottom']});
                    color: {color_scheme['text']};
                    border: 4px solid {color_scheme['border']};
                    border-radius: 16px;
                    padding: 8px 16px;
                    font-weight: 900;
                    font-size: 13px;
                    font-family: 'Segoe UI', 'Roboto', 'Helvetica Neue', 'Arial', sans-serif;
                    min-height: 38px;
                    max-height: 38px;
                    min-width: 100px;
                    text-align: center;
                    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8),
                               1px 1px 2px rgba(0, 0, 0, 0.6);
                    box-shadow: 0 6px 15px {color_scheme['shadow']},
                               inset 0 2px 0 rgba(255, 255, 255, 0.3),
                               inset 0 -2px 0 rgba(0, 0, 0, 0.3),
                               0 0 20px {color_scheme['shadow']};
                    letter-spacing: 1px;
                    text-transform: uppercase;
                }}
                QPushButton:hover {{
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 {color_scheme['hover_start']},
                        stop:0.15 {color_scheme['hover_mid']},
                        stop:0.85 {color_scheme['hover_end']},
                        stop:1 {color_scheme['hover_bottom']});
                    border: 5px solid {color_scheme['hover_border']};
                    transform: translateY(-3px) scale(1.02);
                    box-shadow: 0 10px 25px {color_scheme['shadow']},
                               inset 0 3px 0 rgba(255, 255, 255, 0.4),
                               inset 0 -3px 0 rgba(0, 0, 0, 0.4),
                               0 0 30px {color_scheme['shadow']};
                    text-shadow: 3px 3px 6px rgba(0, 0, 0, 0.9),
                               1px 1px 3px rgba(0, 0, 0, 0.7);
                    letter-spacing: 1.2px;
                }}
                QPushButton:pressed {{
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 {color_scheme['pressed_start']},
                        stop:0.15 {color_scheme['pressed_mid']},
                        stop:0.85 {color_scheme['pressed_end']},
                        stop:1 {color_scheme['pressed_bottom']});
                    border: 4px solid {color_scheme['pressed_border']};
                    transform: translateY(1px) scale(0.98);
                    box-shadow: inset 0 4px 8px rgba(0, 0, 0, 0.6),
                               0 3px 6px {color_scheme['shadow']},
                               inset 0 0 15px rgba(0, 0, 0, 0.4);
                    text-shadow: 2px 2px 4px rgba(0, 0, 0, 1.0),
                               1px 1px 2px rgba(0, 0, 0, 0.8);
                    letter-spacing: 0.8px;
                }}
                QPushButton:disabled {{
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 #9ca3af, stop:0.5 #6b7280, stop:1 #4b5563);
                    color: #d1d5db;
                    border: 3px solid #6b7280;
                    box-shadow: none;
                    text-shadow: none;
                    text-transform: none;
                }}
                {menu_indicator}
            """

            button.setStyleSheet(style)

        except Exception as e:
            print(f"خطأ في تطبيق تصميم الزر: {str(e)}")

    # دوال الأزرار
    def add_client(self):
        """إضافة عميل جديد"""
        try:
            dialog = AddClientDialog(self.session, self)
            if dialog.exec_() == QDialog.Accepted:
                self.refresh_data()  # تحديث الجدول بعد الإضافة
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ في إضافة العميل: {str(e)}")

    def delete_client(self):
        """حذف عميل أو عملاء متعددين مع نافذة تأكيد متطورة"""
        try:
            # التأكد من وجود قائمة العملاء المحددين
            if not hasattr(self, 'selected_clients'):
                self.selected_clients = []

            # التحقق من وجود تحديد
            if not self.selected_clients:
                show_info_message("تنبيه", "يرجى تحديد عميل أو أكثر للحذف")
                return

            # استخدام دالة الحذف المتعدد الجديدة
            self.delete_selected_clients()

        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ أثناء الحذف: {str(e)}")

    def delete_client_old(self):
        """الدالة القديمة للحذف الفردي - محفوظة للمرجع"""
        try:
            client_id = self.get_selected_client_id()
            if client_id:
                # الحصول على بيانات العميل
                client = self.session.query(Client).filter(Client.id == client_id).first()
                if client:
                    # إنشاء نافذة حذف مشابهة لنافذة الإضافة
                    dialog = DeleteClientDialog(self, client)
                    if dialog.exec_() == QDialog.Accepted:
                        try:
                            # حذف العميل من قاعدة البيانات
                            self.session.delete(client)
                            self.session.commit()

                            # إظهار رسالة نجاح متطورة
                            self.show_success_message(f"تم حذف العميل '{client.name}' بنجاح")

                            # تحديث الجدول
                            self.refresh_data()

                        except Exception as e:
                            self.session.rollback()
                            self.show_error_message(f"فشل في حذف العميل: {str(e)}")
                else:
                    self.show_error_message("لم يتم العثور على العميل المحدد")
            else:
                self.show_warning_message("يرجى اختيار عميل للحذف")
        except Exception as e:
            self.show_error_message(f"حدث خطأ في عملية الحذف: {str(e)}")

    def show_success_message(self, message):
        """إظهار رسالة نجاح متطورة"""
        msg = QMessageBox(self)
        msg.setIcon(QMessageBox.Information)
        msg.setWindowTitle("✅ نجح")
        msg.setText(message)
        msg.setStyleSheet("""
            QMessageBox {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #f0fdf4, stop:1 #dcfce7);
                border: 3px solid #22c55e;
                border-radius: 15px;
                font-family: 'Segoe UI', sans-serif;
                font-size: 14px;
            }
            QMessageBox QLabel {
                color: #15803d;
                font-weight: bold;
                padding: 10px;
            }
            QMessageBox QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #22c55e, stop:1 #16a34a);
                color: white;
                border: none;
                border-radius: 8px;
                padding: 8px 20px;
                font-weight: bold;
                min-width: 80px;
            }
            QMessageBox QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #16a34a, stop:1 #15803d);
            }
        """)
        msg.exec_()

    def show_error_message(self, message):
        """إظهار رسالة خطأ متطورة"""
        msg = QMessageBox(self)
        msg.setIcon(QMessageBox.Critical)
        msg.setWindowTitle("❌ خطأ")
        msg.setText(message)
        msg.setStyleSheet("""
            QMessageBox {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #fef2f2, stop:1 #fee2e2);
                border: 3px solid #ef4444;
                border-radius: 15px;
                font-family: 'Segoe UI', sans-serif;
                font-size: 14px;
            }
            QMessageBox QLabel {
                color: #dc2626;
                font-weight: bold;
                padding: 10px;
            }
            QMessageBox QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #ef4444, stop:1 #dc2626);
                color: white;
                border: none;
                border-radius: 8px;
                padding: 8px 20px;
                font-weight: bold;
                min-width: 80px;
            }
            QMessageBox QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #dc2626, stop:1 #b91c1c);
            }
        """)
        msg.exec_()

    def show_warning_message(self, message):
        """إظهار رسالة تحذير متطورة مشابهة لنوافذ البرنامج"""
        # إنشاء نافذة تحذير مخصصة مشابهة لنافذة الحذف
        dialog = WarningDialog(self, message)
        dialog.exec_()

    def view_client(self):
        """عرض تفاصيل العميل في نافذة متطورة"""
        try:
            client_id = self.get_selected_client_id()
            if client_id:
                print(f"عرض تفاصيل العميل رقم: {client_id}")
                client = self.session.query(Client).filter(Client.id == client_id).first()
                if client:
                    # إنشاء نافذة المعلومات المتطورة
                    info_dialog = ClientInfoDialog(self, client)
                    info_dialog.exec_()
                else:
                    QMessageBox.warning(self, "خطأ", "لم يتم العثور على العميل")
            else:
                QMessageBox.warning(self, "تحذير", "يرجى اختيار عميل لعرض تفاصيله")
        except Exception as e:
            print(f"خطأ في عرض تفاصيل العميل: {str(e)}")
            self.show_error_message(f"حدث خطأ في عرض تفاصيل العميل: {str(e)}")

    def edit_amount(self):
        """تعديل رصيد العميل - إضافة أو تقليل المبلغ"""
        try:
            client_id = self.get_selected_client_id()
            if not client_id:
                from utils import show_error_message
                show_error_message("تحذير", "يرجى اختيار عميل لتعديل رصيده")
                return

            client = self.session.query(Client).get(client_id)
            if not client:
                from utils import show_error_message
                show_error_message("خطأ", "لم يتم العثور على العميل")
                return

            # فتح نافذة تعديل المبلغ
            dialog = EditClientAmountDialog(self, client, self.session)
            if dialog.exec_() == QDialog.Accepted:
                self.refresh_data()

        except Exception as e:
            from utils import show_error_message
            show_error_message("خطأ", f"حدث خطأ: {str(e)}")

    def export_to_excel(self):
        """تصدير بيانات العملاء إلى Excel"""
        try:
            file_path, _ = QFileDialog.getSaveFileName(
                self, "حفظ ملف Excel", "العملاء.csv", "CSV Files (*.csv)"
            )

            if not file_path:
                return

            # جلب جميع العملاء
            clients = self.session.query(Client).order_by(Client.id.asc()).all()

            with open(file_path, 'w', newline='', encoding='utf-8') as csvfile:
                writer = csv.writer(csvfile)

                # كتابة العناوين
                headers = [
                    "الرقم التسلسلي",
                    "اسم العميل",
                    "العنوان",
                    "البريد الإلكتروني",
                    "رقم الهاتف",
                    "الرصيد",
                    "حالة العميل",
                    "الملاحظات",
                    "تاريخ الإضافة"
                ]
                writer.writerow(headers)

                # كتابة البيانات
                for client in clients:
                    # تحديد حالة العميل
                    balance = client.balance or 0
                    if balance > 0:
                        status_text = "نشط"
                    elif balance == 0:
                        status_text = "عادي"
                    else:
                        status_text = "مدين"

                    writer.writerow([
                        client.id,
                        client.name or "No Data",
                        client.address or "No Data",
                        client.email or "No Data",
                        client.phone or "No Data",
                        f"{balance:.2f} ج.م",
                        status_text,
                        client.notes or "No Data",
                        client.created_at.strftime("%Y-%m-%d %H:%M") if client.created_at else "No Data"
                    ])

                show_info_message("تم", f"تم تصدير العملاء بنجاح إلى:\n{file_path}")

        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ في التصدير: {str(e)}")

    def export_to_excel_advanced(self):
        """تصدير بيانات العملاء إلى Excel متقدم مع تنسيق وإحصائيات"""
        try:
            import csv
            from datetime import datetime

            file_path, _ = QFileDialog.getSaveFileName(
                self, "حفظ ملف Excel متقدم", f"العملاء_متقدم_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv",
                "CSV Files (*.csv)"
            )

            if file_path:
                clients = self.session.query(Client).all()

                with open(file_path, 'w', newline='', encoding='utf-8-sig') as csvfile:
                    writer = csv.writer(csvfile)

                    # كتابة عنوان التقرير
                    writer.writerow(['تقرير العملاء المتقدم'])
                    writer.writerow([f'تاريخ التصدير: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}'])
                    writer.writerow([f'إجمالي العملاء: {len(clients)}'])
                    writer.writerow([])  # سطر فارغ

                    # حساب الإحصائيات
                    total_balance = sum(client.balance for client in clients)
                    positive_balance = sum(client.balance for client in clients if client.balance > 0)
                    negative_balance = sum(client.balance for client in clients if client.balance < 0)

                    writer.writerow(['الإحصائيات العامة'])
                    writer.writerow(['إجمالي الأرصدة', f'{total_balance:,.2f} جنيه'])
                    writer.writerow(['الأرصدة الموجبة', f'{positive_balance:,.2f} جنيه'])
                    writer.writerow(['الأرصدة السالبة', f'{negative_balance:,.2f} جنيه'])
                    writer.writerow([])  # سطر فارغ

                    # كتابة رؤوس الأعمدة
                    headers = [
                        'الرقم التعريفي', 'اسم العميل', 'رقم الهاتف', 'العنوان',
                        'البريد الإلكتروني', 'الرصيد', 'حالة الرصيد', 'الملاحظات',
                        'تاريخ الإنشاء', 'آخر تحديث'
                    ]
                    writer.writerow(headers)

                    # كتابة بيانات العملاء
                    for client in clients:
                        balance_status = 'دائن' if client.balance > 0 else 'مدين' if client.balance < 0 else 'متوازن'

                        row = [
                            client.id,
                            client.name or '',
                            client.phone or '',
                            client.address or '',
                            client.email or '',
                            f'{client.balance:,.2f}',
                            balance_status,
                            client.notes or '',
                            client.created_at.strftime('%Y-%m-%d %H:%M:%S') if client.created_at else '',
                            client.updated_at.strftime('%Y-%m-%d %H:%M:%S') if hasattr(client, 'updated_at') and client.updated_at else ''
                        ]
                        writer.writerow(row)

                show_info_message("تم", f"تم تصدير العملاء بتنسيق متقدم بنجاح إلى:\n{file_path}")

        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ في التصدير المتقدم: {str(e)}")

    def export_to_csv_advanced(self):
        """تصدير بيانات العملاء إلى CSV شامل مع تفاصيل إضافية"""
        try:
            import csv
            from datetime import datetime

            file_path, _ = QFileDialog.getSaveFileName(
                self, "حفظ ملف CSV شامل", f"العملاء_شامل_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv",
                "CSV Files (*.csv)"
            )

            if file_path:
                clients = self.session.query(Client).all()

                with open(file_path, 'w', newline='', encoding='utf-8-sig') as csvfile:
                    writer = csv.writer(csvfile)

                    # معلومات التقرير
                    writer.writerow(['نظام إدارة العملاء - تقرير شامل'])
                    writer.writerow([f'تاريخ الإنشاء: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}'])
                    writer.writerow([f'عدد العملاء: {len(clients)}'])
                    writer.writerow([])

                    # تحليل البيانات
                    active_clients = len([c for c in clients if c.balance != 0])
                    inactive_clients = len(clients) - active_clients

                    writer.writerow(['تحليل العملاء'])
                    writer.writerow(['العملاء النشطين', active_clients])
                    writer.writerow(['العملاء غير النشطين', inactive_clients])
                    writer.writerow([])

                    # البيانات التفصيلية
                    headers = [
                        'ID', 'الاسم الكامل', 'الهاتف الأساسي', 'الهاتف الثانوي',
                        'العنوان التفصيلي', 'البريد الإلكتروني', 'الرصيد الحالي',
                        'نوع العميل', 'الملاحظات', 'تاريخ التسجيل', 'حالة النشاط'
                    ]
                    writer.writerow(headers)

                    for client in clients:
                        client_type = 'VIP' if client.balance > 10000 else 'عادي' if client.balance >= 0 else 'مدين'
                        activity_status = 'نشط' if client.balance != 0 else 'غير نشط'

                        row = [
                            client.id,
                            client.name or 'غير محدد',
                            client.phone or 'غير محدد',
                            getattr(client, 'phone2', '') or 'غير محدد',
                            client.address or 'غير محدد',
                            client.email or 'غير محدد',
                            f'{client.balance:,.2f} جنيه',
                            client_type,
                            client.notes or 'لا توجد ملاحظات',
                            client.created_at.strftime('%Y-%m-%d') if client.created_at else 'غير محدد',
                            activity_status
                        ]
                        writer.writerow(row)

                show_info_message("تم", f"تم تصدير التقرير الشامل بنجاح إلى:\n{file_path}")

        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ في التصدير الشامل: {str(e)}")









    def export_detailed_report(self):
        """تصدير تقرير تفصيلي شامل للعملاء"""
        try:
            import csv
            from datetime import datetime

            file_path, _ = QFileDialog.getSaveFileName(
                self, "حفظ التقرير التفصيلي", f"تقرير_تفصيلي_العملاء_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv",
                "CSV Files (*.csv)"
            )

            if file_path:
                clients = self.session.query(Client).all()

                with open(file_path, 'w', newline='', encoding='utf-8-sig') as csvfile:
                    writer = csv.writer(csvfile)

                    # معلومات التقرير
                    writer.writerow(['التقرير التفصيلي الشامل للعملاء'])
                    writer.writerow([f'تاريخ الإنشاء: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}'])
                    writer.writerow([f'إجمالي العملاء: {len(clients)}'])
                    writer.writerow([])

                    # تحليل مفصل
                    balances = [client.balance for client in clients]
                    avg_balance = sum(balances) / len(balances) if balances else 0
                    max_balance = max(balances) if balances else 0
                    min_balance = min(balances) if balances else 0

                    writer.writerow(['التحليل المالي المفصل'])
                    writer.writerow(['متوسط الرصيد', f'{avg_balance:,.2f} جنيه'])
                    writer.writerow(['أعلى رصيد', f'{max_balance:,.2f} جنيه'])
                    writer.writerow(['أقل رصيد', f'{min_balance:,.2f} جنيه'])
                    writer.writerow([])

                    # البيانات الكاملة
                    headers = [
                        'الرقم التعريفي', 'اسم العميل', 'رقم الهاتف', 'العنوان',
                        'البريد الإلكتروني', 'الرصيد الحالي', 'حالة الرصيد', 'تصنيف العميل',
                        'الملاحظات', 'تاريخ التسجيل', 'حالة النشاط', 'تقييم الائتمان'
                    ]
                    writer.writerow(headers)

                    for client in clients:
                        balance_status = 'دائن' if client.balance > 0 else 'مدين' if client.balance < 0 else 'متوازن'
                        client_type = 'VIP' if client.balance > 10000 else 'عادي' if client.balance >= 0 else 'مدين'
                        activity_status = 'نشط' if client.balance != 0 else 'غير نشط'
                        credit_rating = 'ممتاز' if client.balance > 5000 else 'جيد' if client.balance >= 0 else 'ضعيف'

                        row = [
                            client.id,
                            client.name or 'غير محدد',
                            client.phone or 'غير محدد',
                            client.address or 'غير محدد',
                            client.email or 'غير محدد',
                            f'{client.balance:,.2f} جنيه',
                            balance_status,
                            client_type,
                            client.notes or 'لا توجد ملاحظات',
                            client.created_at.strftime('%Y-%m-%d') if client.created_at else 'غير محدد',
                            activity_status,
                            credit_rating
                        ]
                        writer.writerow(row)

                show_info_message("تم", f"تم إنشاء التقرير التفصيلي بنجاح:\n{file_path}")

        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ في إنشاء التقرير التفصيلي: {str(e)}")

    def export_balance_report(self):
        """تصدير تقرير الأرصدة المتخصص"""
        try:
            import csv
            from datetime import datetime

            file_path, _ = QFileDialog.getSaveFileName(
                self, "حفظ تقرير الأرصدة", f"تقرير_الأرصدة_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv",
                "CSV Files (*.csv)"
            )

            if file_path:
                clients = self.session.query(Client).all()

                with open(file_path, 'w', newline='', encoding='utf-8-sig') as csvfile:
                    writer = csv.writer(csvfile)

                    # عنوان التقرير
                    writer.writerow(['تقرير الأرصدة المتخصص'])
                    writer.writerow([f'تاريخ الإنشاء: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}'])
                    writer.writerow([])

                    # تصنيف الأرصدة
                    positive_clients = [c for c in clients if c.balance > 0]
                    negative_clients = [c for c in clients if c.balance < 0]
                    zero_clients = [c for c in clients if c.balance == 0]

                    # ملخص الأرصدة
                    writer.writerow(['ملخص الأرصدة'])
                    writer.writerow(['إجمالي الأرصدة الموجبة', f'{sum(c.balance for c in positive_clients):,.2f} جنيه'])
                    writer.writerow(['إجمالي الأرصدة السالبة', f'{sum(c.balance for c in negative_clients):,.2f} جنيه'])
                    writer.writerow(['صافي الأرصدة', f'{sum(c.balance for c in clients):,.2f} جنيه'])
                    writer.writerow([])

                    # العملاء الدائنين
                    if positive_clients:
                        writer.writerow(['العملاء الدائنين (مرتبين تنازلياً)'])
                        writer.writerow(['الترتيب', 'اسم العميل', 'الرصيد', 'الهاتف'])

                        positive_clients.sort(key=lambda x: x.balance, reverse=True)
                        for i, client in enumerate(positive_clients, 1):
                            writer.writerow([i, client.name or 'غير محدد', f'{client.balance:,.2f} جنيه', client.phone or 'غير محدد'])
                        writer.writerow([])

                    # العملاء المدينين
                    if negative_clients:
                        writer.writerow(['العملاء المدينين (مرتبين تصاعدياً)'])
                        writer.writerow(['الترتيب', 'اسم العميل', 'الرصيد', 'الهاتف'])

                        negative_clients.sort(key=lambda x: x.balance)
                        for i, client in enumerate(negative_clients, 1):
                            writer.writerow([i, client.name or 'غير محدد', f'{client.balance:,.2f} جنيه', client.phone or 'غير محدد'])
                        writer.writerow([])

                    # العملاء المتوازنين
                    if zero_clients:
                        writer.writerow(['العملاء المتوازنين (رصيد صفر)'])
                        writer.writerow(['الرقم', 'اسم العميل', 'الهاتف'])

                        for i, client in enumerate(zero_clients, 1):
                            writer.writerow([i, client.name or 'غير محدد', client.phone or 'غير محدد'])

                show_info_message("تم", f"تم إنشاء تقرير الأرصدة بنجاح:\n{file_path}")

        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ في إنشاء تقرير الأرصدة: {str(e)}")

    def export_custom(self):
        """تصدير مخصص مع خيارات متقدمة"""
        try:
            from PyQt5.QtWidgets import QDialog, QVBoxLayout, QHBoxLayout, QCheckBox, QGroupBox, QPushButton, QLabel

            # إنشاء نافذة الخيارات المخصصة مع ألوان الإحصائيات
            dialog = QDialog(self)
            dialog.setWindowTitle("🔧 تصدير مخصص - خيارات متقدمة")
            dialog.setModal(True)
            dialog.resize(400, 380)  # تقليل الارتفاع من 500 إلى 380

            # إزالة علامة الاستفهام وتحسين شريط العنوان مطابق للبرنامج
            dialog.setWindowFlags(Qt.Dialog | Qt.WindowTitleHint | Qt.WindowCloseButtonHint)

            # تخصيص شريط العنوان مطابق للبرنامج
            try:
                from ui.title_bar_utils import TitleBarStyler
                TitleBarStyler.apply_advanced_title_bar_styling(dialog)
            except Exception as e:
                print(f"تحذير: فشل في تطبيق تصميم شريط العنوان للنافذة الحوارية: {e}")

            # تطبيق نمط النافذة مطابق تماماً لنافذة الإحصائيات
            dialog.setStyleSheet("""
                QDialog {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                        stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #334155,
                        stop:0.3 #475569, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                        stop:0.6 #60A5FA, stop:0.7 #8B5CF6, stop:0.8 #7C3AED,
                        stop:0.9 #6D28D9, stop:1 #5B21B6);
                    border: none;
                    border-radius: 15px;
                }
            """)

            # التخطيط الرئيسي مضغوط
            layout = QVBoxLayout(dialog)
            layout.setContentsMargins(15, 10, 15, 10)  # تقليل الهوامش
            layout.setSpacing(8)  # تقليل المسافات

            # العنوان الرئيسي مضغوط
            title_container = QWidget()
            title_container.setStyleSheet("""
                QWidget {
                    background: transparent;
                    padding: 5px;
                }
            """)

            title_inner_layout = QVBoxLayout(title_container)
            title_inner_layout.setContentsMargins(0, 0, 0, 0)
            title_inner_layout.setSpacing(3)  # تقليل المسافة

            # الأيقونة والعنوان الرئيسي مضغوط
            main_title = QLabel("🔧 تصدير مخصص للعملاء")
            main_title.setAlignment(Qt.AlignCenter)
            main_title.setStyleSheet("""
                QLabel {
                    color: #ffffff;
                    font-size: 22px;
                    font-weight: bold;
                    background: transparent;
                    text-shadow: 4px 4px 8px rgba(0, 0, 0, 0.9);
                    padding: 5px;
                }
            """)

            # العنوان الفرعي التوضيحي مضغوط
            subtitle = QLabel("اختر البيانات المراد تصديرها بدقة")
            subtitle.setAlignment(Qt.AlignCenter)
            subtitle.setStyleSheet("""
                QLabel {
                    color: rgba(255, 255, 255, 0.8);
                    font-size: 12px;
                    font-weight: normal;
                    background: transparent;
                    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
                    padding: 2px;
                }
            """)

            title_inner_layout.addWidget(main_title)
            title_inner_layout.addWidget(subtitle)
            layout.addWidget(title_container)

            # مجموعة البيانات الأساسية مطابقة لنافذة الإحصائيات
            basic_group = QWidget()
            basic_group.setStyleSheet("""
                QWidget {
                    background: transparent;
                    padding: 5px;
                    margin: 1px;
                }
                QWidget:hover {
                    background: rgba(255, 255, 255, 0.05);
                    border-radius: 6px;
                }
            """)

            basic_main_layout = QVBoxLayout(basic_group)
            basic_main_layout.setSpacing(5)  # تقليل المسافة
            basic_main_layout.setContentsMargins(15, 5, 15, 5)  # تقليل الهوامش

            # عنوان المجموعة مضغوط ومتوسط
            basic_title = QLabel("📋 البيانات الأساسية")
            basic_title.setAlignment(Qt.AlignCenter)  # توسيط العنوان
            basic_title.setStyleSheet("""
                QLabel {
                    color: #ffffff;
                    font-size: 14px;
                    font-weight: bold;
                    text-shadow: 3px 3px 6px rgba(0, 0, 0, 0.9);
                    background: transparent;
                    border: none;
                    padding: 2px;
                    margin-bottom: 5px;
                }
            """)
            basic_main_layout.addWidget(basic_title)

            basic_layout = QVBoxLayout()
            basic_layout.setSpacing(3)  # تقليل المسافة أكثر

            self.export_id = QCheckBox("🆔 الرقم التعريفي")
            self.export_name = QCheckBox("👤 اسم العميل")
            self.export_phone = QCheckBox("📞 رقم الهاتف")
            self.export_address = QCheckBox("🏠 العنوان")
            self.export_email = QCheckBox("📧 البريد الإلكتروني")

            # تحديد افتراضي
            self.export_name.setChecked(True)
            self.export_phone.setChecked(True)

            # تطبيق تصميم مطابق لعناصر الإحصائيات
            checkboxes_data = [
                (self.export_id, "#3B82F6"),
                (self.export_name, "#10B981"),
                (self.export_phone, "#F59E0B"),
                (self.export_address, "#EF4444"),
                (self.export_email, "#8B5CF6")
            ]

            for checkbox, color in checkboxes_data:
                # إنشاء عنصر مطابق لعناصر الإحصائيات
                item_widget = QWidget()
                item_widget.setStyleSheet("""
                    QWidget {
                        background: transparent;
                        padding: 2px;
                        margin: 0px;
                    }
                    QWidget:hover {
                        background: rgba(255, 255, 255, 0.05);
                        border-radius: 6px;
                    }
                """)

                item_layout = QHBoxLayout(item_widget)
                item_layout.setSpacing(8)  # تقليل المسافة
                item_layout.setContentsMargins(8, 3, 8, 3)  # تقليل الهوامش

                # تصميم الـ CheckBox مع علامة صح واضحة
                checkbox.setStyleSheet(f"""
                    QCheckBox {{
                        color: #ffffff;
                        font-size: 14px;
                        font-weight: normal;
                        text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
                        spacing: 12px;
                    }}
                    QCheckBox::indicator {{
                        width: 20px;
                        height: 20px;
                        border: 2px solid {color};
                        border-radius: 4px;
                        background: rgba(255, 255, 255, 0.1);
                    }}
                    QCheckBox::indicator:checked {{
                        background: {color};
                        border: 2px solid #ffffff;
                        image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAiIGhlaWdodD0iMjAiIHZpZXdCb3g9IjAgMCAyMCAyMCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTQgMTBMOCAxNEwxNiA2IiBzdHJva2U9IndoaXRlIiBzdHJva2Utd2lkdGg9IjMiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIgc3Ryb2tlLWxpbmVqb2luPSJyb3VuZCIvPgo8L3N2Zz4K);
                    }}
                    QCheckBox::indicator:hover {{
                        background: rgba(255, 255, 255, 0.2);
                        border: 2px solid rgba(255, 255, 255, 0.8);
                    }}
                """)



                # إضافة علامة صح خارجية محسنة
                check_label = QLabel("")
                check_label.setFixedSize(30, 25)
                check_label.setAlignment(Qt.AlignCenter)
                check_label.setStyleSheet("""
                    QLabel {
                        color: #10B981;
                        font-size: 20px;
                        font-weight: bold;
                        background: rgba(16, 185, 129, 0.1);
                        border: 1px solid rgba(16, 185, 129, 0.3);
                        border-radius: 8px;
                        text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
                        padding: 2px;
                    }
                """)

                # ربط تغيير حالة الـ CheckBox بإظهار/إخفاء علامة الصح
                def create_toggle_function(label):
                    def toggle_check(state):
                        if state == 2:  # محدد
                            label.setText("✓")
                        else:
                            label.setText("")
                    return toggle_check

                checkbox.stateChanged.connect(create_toggle_function(check_label))

                # إظهار علامة الصح للعناصر المحددة مسبقاً
                if checkbox.isChecked():
                    check_label.setText("✓")

                item_layout.addWidget(check_label)  # علامة الصح أولاً (الجانب الأيمن)
                item_layout.addWidget(checkbox)
                basic_layout.addWidget(item_widget)

            basic_main_layout.addLayout(basic_layout)
            layout.addWidget(basic_group)

            # مجموعة البيانات المالية مطابقة لنافذة الإحصائيات
            financial_group = QWidget()
            financial_group.setStyleSheet("""
                QWidget {
                    background: transparent;
                    padding: 5px;
                    margin: 1px;
                }
                QWidget:hover {
                    background: rgba(255, 255, 255, 0.05);
                    border-radius: 6px;
                }
            """)

            financial_main_layout = QVBoxLayout(financial_group)
            financial_main_layout.setSpacing(5)  # تقليل المسافة
            financial_main_layout.setContentsMargins(15, 5, 15, 5)  # تقليل الهوامش

            # عنوان المجموعة مضغوط ومتوسط
            financial_title = QLabel("💰 البيانات المالية")
            financial_title.setAlignment(Qt.AlignCenter)  # توسيط العنوان
            financial_title.setStyleSheet("""
                QLabel {
                    color: #ffffff;
                    font-size: 14px;
                    font-weight: bold;
                    text-shadow: 3px 3px 6px rgba(0, 0, 0, 0.9);
                    background: transparent;
                    border: none;
                    padding: 2px;
                    margin-bottom: 5px;
                }
            """)
            financial_main_layout.addWidget(financial_title)

            financial_layout = QVBoxLayout()
            financial_layout.setSpacing(3)  # تقليل المسافة أكثر

            self.export_balance = QCheckBox("💵 الرصيد الحالي")
            self.export_balance_status = QCheckBox("📊 حالة الرصيد")
            self.export_client_type = QCheckBox("🏆 تصنيف العميل")

            self.export_balance.setChecked(True)

            # تطبيق تصميم مطابق لعناصر الإحصائيات للبيانات المالية
            financial_checkboxes_data = [
                (self.export_balance, "#10B981"),
                (self.export_balance_status, "#F59E0B"),
                (self.export_client_type, "#EF4444")
            ]

            for checkbox, color in financial_checkboxes_data:
                # إنشاء عنصر مطابق لعناصر الإحصائيات
                item_widget = QWidget()
                item_widget.setStyleSheet("""
                    QWidget {
                        background: transparent;
                        padding: 5px;
                        margin: 1px;
                    }
                    QWidget:hover {
                        background: rgba(255, 255, 255, 0.05);
                        border-radius: 6px;
                    }
                """)

                item_layout = QHBoxLayout(item_widget)
                item_layout.setSpacing(12)
                item_layout.setContentsMargins(12, 5, 12, 5)

                # تصميم الـ CheckBox مع علامة صح واضحة
                checkbox.setStyleSheet(f"""
                    QCheckBox {{
                        color: #ffffff;
                        font-size: 14px;
                        font-weight: normal;
                        text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
                        spacing: 12px;
                    }}
                    QCheckBox::indicator {{
                        width: 20px;
                        height: 20px;
                        border: 2px solid {color};
                        border-radius: 4px;
                        background: rgba(255, 255, 255, 0.1);
                    }}
                    QCheckBox::indicator:checked {{
                        background: {color};
                        border: 2px solid #ffffff;
                        image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAiIGhlaWdodD0iMjAiIHZpZXdCb3g9IjAgMCAyMCAyMCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTQgMTBMOCAxNEwxNiA2IiBzdHJva2U9IndoaXRlIiBzdHJva2Utd2lkdGg9IjMiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIgc3Ryb2tlLWxpbmVqb2luPSJyb3VuZCIvPgo8L3N2Zz4K);
                    }}
                    QCheckBox::indicator:hover {{
                        background: rgba(255, 255, 255, 0.2);
                        border: 2px solid rgba(255, 255, 255, 0.8);
                    }}
                """)



                # إضافة علامة صح خارجية محسنة للبيانات المالية
                check_label = QLabel("")
                check_label.setFixedSize(30, 25)
                check_label.setAlignment(Qt.AlignCenter)
                check_label.setStyleSheet("""
                    QLabel {
                        color: #10B981;
                        font-size: 20px;
                        font-weight: bold;
                        background: rgba(16, 185, 129, 0.1);
                        border: 1px solid rgba(16, 185, 129, 0.3);
                        border-radius: 8px;
                        text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
                        padding: 2px;
                    }
                """)

                # ربط تغيير حالة الـ CheckBox بإظهار/إخفاء علامة الصح
                def create_toggle_function(label):
                    def toggle_check(state):
                        if state == 2:  # محدد
                            label.setText("✓")
                        else:
                            label.setText("")
                    return toggle_check

                checkbox.stateChanged.connect(create_toggle_function(check_label))

                # إظهار علامة الصح للعناصر المحددة مسبقاً
                if checkbox.isChecked():
                    check_label.setText("✓")

                item_layout.addWidget(check_label)  # علامة الصح أولاً (الجانب الأيمن)
                item_layout.addWidget(checkbox)
                financial_layout.addWidget(item_widget)

            financial_main_layout.addLayout(financial_layout)
            layout.addWidget(financial_group)

            # مجموعة البيانات الإضافية مطابقة لنافذة الإحصائيات
            additional_group = QWidget()
            additional_group.setStyleSheet("""
                QWidget {
                    background: transparent;
                    padding: 5px;
                    margin: 1px;
                }
                QWidget:hover {
                    background: rgba(255, 255, 255, 0.05);
                    border-radius: 6px;
                }
            """)

            additional_main_layout = QVBoxLayout(additional_group)
            additional_main_layout.setSpacing(5)  # تقليل المسافة
            additional_main_layout.setContentsMargins(15, 5, 15, 5)  # تقليل الهوامش

            # عنوان المجموعة مضغوط ومتوسط
            additional_title = QLabel("📝 البيانات الإضافية")
            additional_title.setAlignment(Qt.AlignCenter)  # توسيط العنوان
            additional_title.setStyleSheet("""
                QLabel {
                    color: #ffffff;
                    font-size: 14px;
                    font-weight: bold;
                    text-shadow: 3px 3px 6px rgba(0, 0, 0, 0.9);
                    background: transparent;
                    border: none;
                    padding: 2px;
                    margin-bottom: 5px;
                }
            """)
            additional_main_layout.addWidget(additional_title)

            additional_layout = QVBoxLayout()
            additional_layout.setSpacing(3)  # تقليل المسافة أكثر

            self.export_notes = QCheckBox("📋 الملاحظات")
            self.export_created_date = QCheckBox("📅 تاريخ الإنشاء")
            self.export_statistics = QCheckBox("📊 إضافة الإحصائيات")

            # تطبيق تصميم مطابق لعناصر الإحصائيات للبيانات الإضافية
            additional_checkboxes_data = [
                (self.export_notes, "#8B5CF6"),
                (self.export_created_date, "#F59E0B"),
                (self.export_statistics, "#3B82F6")
            ]

            for checkbox, color in additional_checkboxes_data:
                # إنشاء عنصر مطابق لعناصر الإحصائيات
                item_widget = QWidget()
                item_widget.setStyleSheet("""
                    QWidget {
                        background: transparent;
                        padding: 5px;
                        margin: 1px;
                    }
                    QWidget:hover {
                        background: rgba(255, 255, 255, 0.05);
                        border-radius: 6px;
                    }
                """)

                item_layout = QHBoxLayout(item_widget)
                item_layout.setSpacing(12)
                item_layout.setContentsMargins(12, 5, 12, 5)

                # تصميم الـ CheckBox مع علامة صح واضحة
                checkbox.setStyleSheet(f"""
                    QCheckBox {{
                        color: #ffffff;
                        font-size: 14px;
                        font-weight: normal;
                        text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
                        spacing: 12px;
                    }}
                    QCheckBox::indicator {{
                        width: 20px;
                        height: 20px;
                        border: 2px solid {color};
                        border-radius: 4px;
                        background: rgba(255, 255, 255, 0.1);
                    }}
                    QCheckBox::indicator:checked {{
                        background: {color};
                        border: 2px solid #ffffff;
                        image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAiIGhlaWdodD0iMjAiIHZpZXdCb3g9IjAgMCAyMCAyMCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTQgMTBMOCAxNEwxNiA2IiBzdHJva2U9IndoaXRlIiBzdHJva2Utd2lkdGg9IjMiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIgc3Ryb2tlLWxpbmVqb2luPSJyb3VuZCIvPgo8L3N2Zz4K);
                    }}
                    QCheckBox::indicator:hover {{
                        background: rgba(255, 255, 255, 0.2);
                        border: 2px solid rgba(255, 255, 255, 0.8);
                    }}
                """)



                # إضافة علامة صح خارجية محسنة للبيانات الإضافية
                check_label = QLabel("")
                check_label.setFixedSize(30, 25)
                check_label.setAlignment(Qt.AlignCenter)
                check_label.setStyleSheet("""
                    QLabel {
                        color: #10B981;
                        font-size: 20px;
                        font-weight: bold;
                        background: rgba(16, 185, 129, 0.1);
                        border: 1px solid rgba(16, 185, 129, 0.3);
                        border-radius: 8px;
                        text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
                        padding: 2px;
                    }
                """)

                # ربط تغيير حالة الـ CheckBox بإظهار/إخفاء علامة الصح
                def create_toggle_function(label):
                    def toggle_check(state):
                        if state == 2:  # محدد
                            label.setText("✓")
                        else:
                            label.setText("")
                    return toggle_check

                checkbox.stateChanged.connect(create_toggle_function(check_label))

                # إظهار علامة الصح للعناصر المحددة مسبقاً
                if checkbox.isChecked():
                    check_label.setText("✓")

                item_layout.addWidget(check_label)  # علامة الصح أولاً (الجانب الأيمن)
                item_layout.addWidget(checkbox)
                additional_layout.addWidget(item_widget)

            additional_main_layout.addLayout(additional_layout)
            layout.addWidget(additional_group)

            # أزرار التحكم مطابقة لنافذة الإحصائيات
            buttons_layout = QHBoxLayout()
            buttons_layout.setSpacing(15)

            # زر الإلغاء مطابق للبرنامج الرئيسي
            cancel_btn = QPushButton("❌ إلغاء")
            cancel_btn.clicked.connect(dialog.reject)
            cancel_btn.setMinimumHeight(45)

            # استخدام دالة التصميم من البرنامج الرئيسي
            self.style_advanced_button(cancel_btn, 'danger')

            # زر التصدير مطابق للبرنامج الرئيسي
            export_btn = QPushButton("📤 تصدير")
            export_btn.clicked.connect(lambda: self.perform_custom_export(dialog))
            export_btn.setMinimumHeight(45)

            # استخدام دالة التصميم من البرنامج الرئيسي
            self.style_advanced_button(export_btn, 'emerald')

            buttons_layout.addWidget(cancel_btn)
            buttons_layout.addWidget(export_btn)
            layout.addLayout(buttons_layout)

            dialog.exec_()

        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ في التصدير المخصص: {str(e)}")

    def perform_custom_export(self, dialog):
        """تنفيذ التصدير المخصص"""
        try:
            import csv
            from datetime import datetime

            file_path, _ = QFileDialog.getSaveFileName(
                self, "حفظ التصدير المخصص", f"تصدير_مخصص_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv",
                "CSV Files (*.csv)"
            )

            if file_path:
                clients = self.session.query(Client).all()

                with open(file_path, 'w', newline='', encoding='utf-8-sig') as csvfile:
                    writer = csv.writer(csvfile)

                    # إضافة الإحصائيات إذا تم اختيارها
                    if self.export_statistics.isChecked():
                        writer.writerow(['تصدير مخصص للعملاء'])
                        writer.writerow([f'تاريخ التصدير: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}'])
                        writer.writerow([f'إجمالي العملاء: {len(clients)}'])
                        writer.writerow([])

                    # إنشاء رؤوس الأعمدة حسب الاختيار
                    headers = []
                    if self.export_id.isChecked():
                        headers.append('الرقم التعريفي')
                    if self.export_name.isChecked():
                        headers.append('اسم العميل')
                    if self.export_phone.isChecked():
                        headers.append('رقم الهاتف')
                    if self.export_address.isChecked():
                        headers.append('العنوان')
                    if self.export_email.isChecked():
                        headers.append('البريد الإلكتروني')
                    if self.export_balance.isChecked():
                        headers.append('الرصيد')
                    if self.export_balance_status.isChecked():
                        headers.append('حالة الرصيد')
                    if self.export_client_type.isChecked():
                        headers.append('تصنيف العميل')
                    if self.export_notes.isChecked():
                        headers.append('الملاحظات')
                    if self.export_created_date.isChecked():
                        headers.append('تاريخ الإنشاء')

                    writer.writerow(headers)

                    # كتابة البيانات
                    for client in clients:
                        row = []
                        if self.export_id.isChecked():
                            row.append(client.id)
                        if self.export_name.isChecked():
                            row.append(client.name or 'غير محدد')
                        if self.export_phone.isChecked():
                            row.append(client.phone or 'غير محدد')
                        if self.export_address.isChecked():
                            row.append(client.address or 'غير محدد')
                        if self.export_email.isChecked():
                            row.append(client.email or 'غير محدد')
                        if self.export_balance.isChecked():
                            row.append(f'{client.balance:,.2f} جنيه')
                        if self.export_balance_status.isChecked():
                            balance_status = 'دائن' if client.balance > 0 else 'مدين' if client.balance < 0 else 'متوازن'
                            row.append(balance_status)
                        if self.export_client_type.isChecked():
                            client_type = 'VIP' if client.balance > 10000 else 'عادي' if client.balance >= 0 else 'مدين'
                            row.append(client_type)
                        if self.export_notes.isChecked():
                            row.append(client.notes or 'لا توجد ملاحظات')
                        if self.export_created_date.isChecked():
                            row.append(client.created_at.strftime('%Y-%m-%d') if client.created_at else 'غير محدد')

                        writer.writerow(row)

                dialog.accept()
                show_info_message("تم", f"تم التصدير المخصص بنجاح إلى:\n{file_path}")

        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ في التصدير المخصص: {str(e)}")

    def export_backup(self):
        """إنشاء نسخة احتياطية شاملة"""
        try:
            import json
            from datetime import datetime

            file_path, _ = QFileDialog.getSaveFileName(
                self, "حفظ النسخة الاحتياطية", f"نسخة_احتياطية_العملاء_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json",
                "JSON Files (*.json)"
            )

            if file_path:
                clients = self.session.query(Client).all()

                # إنشاء نسخة احتياطية شاملة
                backup_data = {
                    "backup_info": {
                        "created_at": datetime.now().isoformat(),
                        "system_version": "1.0",
                        "total_records": len(clients),
                        "backup_type": "full_clients_backup"
                    },
                    "clients_data": []
                }

                for client in clients:
                    client_backup = {
                        "id": client.id,
                        "name": client.name,
                        "phone": client.phone,
                        "address": client.address,
                        "email": client.email,
                        "balance": float(client.balance),
                        "notes": client.notes,
                        "created_at": client.created_at.isoformat() if client.created_at else None,
                        # إضافة معلومات إضافية للنسخة الاحتياطية
                        "metadata": {
                            "balance_category": "positive" if client.balance > 0 else "negative" if client.balance < 0 else "zero",
                            "client_tier": "vip" if client.balance > 10000 else "regular" if client.balance >= 0 else "debtor",
                            "has_contact_info": bool(client.phone or client.email),
                            "has_address": bool(client.address),
                            "has_notes": bool(client.notes)
                        }
                    }
                    backup_data["clients_data"].append(client_backup)

                # كتابة النسخة الاحتياطية
                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(backup_data, f, ensure_ascii=False, indent=2)

                show_info_message("تم", f"تم إنشاء النسخة الاحتياطية بنجاح:\n{file_path}\n\nتحتوي على {len(clients)} عميل مع جميع البيانات والمعلومات الإضافية")

        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ في إنشاء النسخة الاحتياطية: {str(e)}")

    def restore_backup(self):
        """استعادة النسخة الاحتياطية للعملاء"""
        try:
            import json
            from datetime import datetime

            # اختيار ملف النسخة الاحتياطية
            file_path, _ = QFileDialog.getOpenFileName(
                self, "اختيار النسخة الاحتياطية للاستعادة", "",
                "JSON Files (*.json);;All Files (*)"
            )

            if not file_path:
                return

            # قراءة ملف النسخة الاحتياطية
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    backup_data = json.load(f)
            except Exception as e:
                show_error_message("خطأ", f"فشل في قراءة ملف النسخة الاحتياطية:\n{str(e)}")
                return

            # التحقق من صحة ملف النسخة الاحتياطية
            if not self.validate_backup_file(backup_data):
                return

            # عرض معلومات النسخة الاحتياطية
            backup_info = backup_data.get("backup_info", {})
            clients_data = backup_data.get("clients_data", [])

            backup_date = backup_info.get("created_at", "غير محدد")
            total_records = len(clients_data)

            # نافذة تأكيد الاستعادة
            confirmation_msg = f"""
هل تريد استعادة النسخة الاحتياطية؟

📋 معلومات النسخة الاحتياطية:
📅 تاريخ الإنشاء: {backup_date}
👥 عدد العملاء: {total_records}
🔄 نوع النسخة: {backup_info.get('backup_type', 'غير محدد')}

⚠️ تحذير: سيتم استبدال جميع بيانات العملاء الحالية!
            """

            reply = QMessageBox.question(
                self, "تأكيد الاستعادة", confirmation_msg,
                QMessageBox.Yes | QMessageBox.No, QMessageBox.No
            )

            if reply != QMessageBox.Yes:
                return

            # بدء عملية الاستعادة
            self.perform_restore(clients_data)

        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ في استعادة النسخة الاحتياطية: {str(e)}")

    def validate_backup_file(self, backup_data):
        """التحقق من صحة ملف النسخة الاحتياطية"""
        try:
            # التحقق من وجود المفاتيح الأساسية
            if not isinstance(backup_data, dict):
                show_error_message("خطأ", "ملف النسخة الاحتياطية غير صحيح - تنسيق خاطئ")
                return False

            if "backup_info" not in backup_data:
                show_error_message("خطأ", "ملف النسخة الاحتياطية غير صحيح - معلومات النسخة مفقودة")
                return False

            if "clients_data" not in backup_data:
                show_error_message("خطأ", "ملف النسخة الاحتياطية غير صحيح - بيانات العملاء مفقودة")
                return False

            # التحقق من نوع النسخة الاحتياطية
            backup_info = backup_data["backup_info"]
            if backup_info.get("backup_type") != "full_clients_backup":
                show_error_message("خطأ", "هذا الملف ليس نسخة احتياطية صحيحة للعملاء")
                return False

            # التحقق من وجود بيانات العملاء
            clients_data = backup_data["clients_data"]
            if not isinstance(clients_data, list):
                show_error_message("خطأ", "بيانات العملاء في النسخة الاحتياطية غير صحيحة")
                return False

            if len(clients_data) == 0:
                show_error_message("تحذير", "النسخة الاحتياطية فارغة - لا تحتوي على أي عملاء")
                return False

            # التحقق من صحة بيانات العميل الأول كعينة
            if clients_data:
                sample_client = clients_data[0]
                required_fields = ["name", "phone", "address", "email", "balance"]
                for field in required_fields:
                    if field not in sample_client:
                        show_error_message("خطأ", f"بيانات العميل غير مكتملة - الحقل '{field}' مفقود")
                        return False

            return True

        except Exception as e:
            show_error_message("خطأ", f"خطأ في التحقق من النسخة الاحتياطية: {str(e)}")
            return False

    def perform_restore(self, clients_data):
        """تنفيذ عملية الاستعادة"""
        try:
            from datetime import datetime

            # إنشاء نافذة تقدم العملية
            progress_dialog = QMessageBox(self)
            progress_dialog.setWindowTitle("جاري الاستعادة...")
            progress_dialog.setText("جاري استعادة النسخة الاحتياطية، يرجى الانتظار...")
            progress_dialog.setStandardButtons(QMessageBox.NoButton)
            progress_dialog.show()

            # حذف جميع العملاء الحاليين
            try:
                self.session.query(Client).delete()
                self.session.commit()
            except Exception as e:
                self.session.rollback()
                progress_dialog.close()
                show_error_message("خطأ", f"فشل في حذف البيانات الحالية: {str(e)}")
                return

            # استعادة العملاء من النسخة الاحتياطية
            restored_count = 0
            failed_count = 0

            for client_data in clients_data:
                try:
                    # إنشاء عميل جديد
                    new_client = Client(
                        name=client_data.get("name"),
                        phone=client_data.get("phone"),
                        address=client_data.get("address"),
                        email=client_data.get("email"),
                        balance=float(client_data.get("balance", 0)),
                        notes=client_data.get("notes"),
                        created_at=datetime.fromisoformat(client_data["created_at"]) if client_data.get("created_at") else datetime.now()
                    )

                    self.session.add(new_client)
                    restored_count += 1

                except Exception as e:
                    print(f"فشل في استعادة العميل: {client_data.get('name', 'غير محدد')} - {str(e)}")
                    failed_count += 1
                    continue

            # حفظ التغييرات
            try:
                self.session.commit()
                progress_dialog.close()

                # عرض نتائج الاستعادة
                result_msg = f"""
✅ تمت الاستعادة بنجاح!

📊 نتائج العملية:
✅ تم استعادة: {restored_count} عميل
❌ فشل في الاستعادة: {failed_count} عميل
📅 تاريخ الاستعادة: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
                """

                show_info_message("تمت الاستعادة", result_msg)

                # تحديث الجدول
                self.refresh_data()

            except Exception as e:
                self.session.rollback()
                progress_dialog.close()
                show_error_message("خطأ", f"فشل في حفظ البيانات المستعادة: {str(e)}")

        except Exception as e:
            progress_dialog.close()
            show_error_message("خطأ", f"حدث خطأ أثناء الاستعادة: {str(e)}")

    def export_to_csv(self):
        """تصدير بيانات العملاء إلى CSV"""
        try:
            # عرض مربع حوار حفظ الملف
            file_path, _ = QFileDialog.getSaveFileName(self, "حفظ كملف CSV", "قائمة_العملاء.csv", "ملفات CSV (*.csv)")

            if not file_path:
                return

            # جلب جميع العملاء
            clients = self.session.query(Client).order_by(Client.id.asc()).all()

            # إعداد البيانات
            headers = ["الرقم التسلسلي", "اسم العميل", "العنوان", "البريد الإلكتروني", "رقم الهاتف", "الرصيد", "حالة العميل", "الملاحظات", "تاريخ الإضافة"]
            data = []

            for client in clients:
                # تحديد حالة العميل
                balance = client.balance or 0
                if balance > 0:
                    status_text = "نشط"
                elif balance == 0:
                    status_text = "عادي"
                else:
                    status_text = "مدين"

                data.append([
                    client.id,
                    client.name or "No Data",
                    client.address or "No Data",
                    client.email or "No Data",
                    client.phone or "No Data",
                    f"{balance:.2f} ج.م",
                    status_text,
                    client.notes or "No Data",
                    client.created_at.strftime("%Y-%m-%d %H:%M") if client.created_at else "No Data"
                ])

            # كتابة البيانات إلى الملف
            with open(file_path, 'w', newline='', encoding='utf-8') as csvfile:
                writer = csv.writer(csvfile)
                writer.writerow(headers)
                writer.writerows(data)

            show_info_message("تم", f"تم تصدير البيانات بنجاح إلى:\n{file_path}")
        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ أثناء تصدير البيانات: {str(e)}")

    def show_whatsapp_options(self):
        """عرض خيارات الواتساب للعميل المحدد"""
        try:
            # التحقق من وجود عميل محدد
            selected_items = self.clients_table.selectedItems()
            if not selected_items:
                from utils import show_info_message
                show_info_message("تنبيه", "يرجى تحديد عميل أولاً للتواصل معه عبر الواتساب")
                return

            # الحصول على بيانات العميل المحدد
            row = selected_items[0].row()
            # تنظيف النص وإزالة الأيقونات للحصول على الرقم فقط
            client_id_text = self.clients_table.item(row, 0).text()
            client_id_clean = ''.join(filter(str.isdigit, client_id_text))
            if not client_id_clean:
                from utils import show_error_message
                show_error_message("خطأ", "لا يمكن الحصول على معرف العميل")
                return
            client_id = int(client_id_clean)
            client = self.session.query(Client).filter(Client.id == client_id).first()

            if not client:
                from utils import show_error_message
                show_error_message("خطأ", "لم يتم العثور على بيانات العميل")
                return

            # عرض نافذة خيارات الواتساب
            dialog = WhatsAppDialog(client, self)
            dialog.exec_()

        except Exception as e:
            print(f"خطأ في عرض خيارات الواتساب: {str(e)}")
            from utils import show_error_message
            show_error_message("خطأ", f"حدث خطأ في عرض خيارات الواتساب: {str(e)}")

    def show_statistics(self):
        """عرض نافذة الإحصائيات المتطورة"""
        try:
            dialog = ClientStatisticsDialog(self, self.session)
            dialog.exec_()
        except Exception as e:
            print(f"خطأ في عرض الإحصائيات: {str(e)}")
            from utils import show_error_message
            show_error_message("خطأ", f"حدث خطأ في عرض الإحصائيات: {str(e)}")




class AddClientDialog(QDialog):
    """نافذة إضافة أو تعديل عميل مع نظام تعدد الأرقام"""

    def __init__(self, session, parent=None, client=None):
        super().__init__(parent)
        self.session = session
        self.parent_widget = parent  # حفظ مرجع للوالد
        self.client = client  # العميل للتعديل (None للإضافة)
        self.is_edit_mode = client is not None  # تحديد وضع التعديل

        # سيتم تحديد العنوان في setup_ui

        self.setModal(True)
        self.resize(650, 850)  # زيادة الارتفاع أكثر
        self.setup_ui()

        # تحميل البيانات في وضع التعديل
        if self.is_edit_mode:
            self.load_client_data()

    def style_advanced_button(self, button, button_type, has_menu=False):
        """تطبيق تصميم متطور على الأزرار - نسخة مبسطة لنافذة الحوار"""
        try:
            # استخدام دالة التصميم من الوالد إذا كانت متاحة
            if self.parent_widget and hasattr(self.parent_widget, 'style_advanced_button'):
                self.parent_widget.style_advanced_button(button, button_type, has_menu)
            else:
                # تصميم بسيط كبديل
                colors = {
                    'emerald': '#10b981',
                    'danger': '#ef4444',
                    'info': '#3b82f6'
                }
                color = colors.get(button_type, '#6b7280')
                button.setStyleSheet(f"""
                    QPushButton {{
                        background-color: {color};
                        color: white;
                        border: none;
                        border-radius: 8px;
                        padding: 10px 20px;
                        font-weight: bold;
                    }}
                    QPushButton:hover {{
                        background-color: {color}dd;
                    }}
                """)
        except Exception as e:
            print(f"خطأ في تصميم الزر: {e}")

    def setup_ui(self):
        """إعداد واجهة النافذة - شريط العنوان في مكانه الطبيعي"""
        # استخدام شريط العنوان الطبيعي للنظام مع النص في المنتصف
        if self.is_edit_mode:
            self.setWindowTitle("✏️ تعديل - نظام إدارة العملاء المتطور والشامل")
        else:
            self.setWindowTitle("🤝 إضافة - نظام إدارة العملاء المتطور والشامل")

        # إزالة علامة الاستفهام وتحسين شريط العنوان
        self.setWindowFlags(Qt.Dialog | Qt.WindowTitleHint | Qt.WindowCloseButtonHint)

        # تخصيص شريط العنوان
        self.customize_title_bar()

        # خلفية النافذة مطابقة للنافذة الرئيسية
        self.setStyleSheet("""
            QDialog {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #334155,
                    stop:0.3 #475569, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                    stop:0.6 #60A5FA, stop:0.7 #8B5CF6, stop:0.8 #7C3AED,
                    stop:0.9 #6D28D9, stop:1 #5B21B6);
                border: none;
                border-radius: 15px;
            }
        """)

        # تخطيط المحتوى الرئيسي للنافذة
        layout = QVBoxLayout(self)
        layout.setContentsMargins(25, 25, 25, 25)
        layout.setSpacing(18)

        # إضافة عنوان النافذة الداخلي - يتغير حسب الوضع
        if self.is_edit_mode:
            title_text = "تعديل بيانات العميل"
            title_icon = "🧑‍💼"
        else:
            title_text = "إضافة عميل جديد"
            title_icon = "🧑‍💼"

        title_label = QLabel(f"{title_icon} {title_text}")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #334155,
                    stop:0.3 #475569, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                    stop:0.6 #60A5FA, stop:0.7 #8B5CF6, stop:0.8 #7C3AED,
                    stop:0.9 #6D28D9, stop:1 #5B21B6);
                border: 3px solid #000000;
                border-radius: 12px;
                padding: 18px 25px;
                margin: 8px 5px;
                font-weight: bold;
                font-size: 18px;
                text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
                box-shadow: 0 6px 20px rgba(0, 0, 0, 0.4),
                           0 3px 12px rgba(37, 99, 235, 0.3);
                min-height: 50px;
                max-height: 50px;
            }
        """)
        layout.addWidget(title_label)

        # نموذج البيانات مع تصميم محسن
        form_layout = QFormLayout()
        form_layout.setLabelAlignment(Qt.AlignRight | Qt.AlignVCenter)
        form_layout.setFormAlignment(Qt.AlignLeft | Qt.AlignTop)
        form_layout.setHorizontalSpacing(15)
        form_layout.setVerticalSpacing(12)

        # إنشاء دالة لتصميم النصوص مع عرض مقلل
        def create_styled_label(text, icon, required=False):
            label = QLabel(f"{icon} {text}")
            if required:
                label.setText(f"{icon} {text} *")
            label.setStyleSheet("""
                QLabel {
                    color: #ffffff;
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                        stop:0 rgba(59, 130, 246, 0.8),
                        stop:0.3 rgba(96, 165, 250, 0.7),
                        stop:0.7 rgba(139, 92, 246, 0.7),
                        stop:1 rgba(124, 58, 237, 0.8));
                    border: 2px solid rgba(96, 165, 250, 0.9);
                    border-radius: 5px;
                    padding: 8px 12px;
                    font-weight: bold;
                    font-size: 16px;
                    min-width: 120px;
                    max-width: 120px;
                    text-align: center;
                    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.7);
                    box-shadow: 0 2px 6px rgba(96, 165, 250, 0.4);
                }
            """)
            label.setAlignment(Qt.AlignCenter)
            return label

        # اسم العميل (مطلوب) - مع استغلال أفضل للمساحة
        self.name_edit = QLineEdit()
        self.name_edit.setPlaceholderText("أدخل اسم العميل الكامل هنا... (مطلوب)")
        self.name_edit.setStyleSheet("""
            QLineEdit {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.95),
                    stop:0.2 rgba(248, 250, 252, 0.98),
                    stop:0.4 rgba(241, 245, 249, 0.95),
                    stop:0.6 rgba(248, 250, 252, 0.98),
                    stop:0.8 rgba(255, 255, 255, 0.95),
                    stop:1 rgba(226, 232, 240, 0.9));
                border: 3px solid rgba(96, 165, 250, 0.8);
                border-radius: 12px;
                padding: 12px 20px;
                font-size: 16px;
                font-weight: bold;
                color: #1f2937;
                min-height: 30px;
                min-width: 350px;
            }
            QLineEdit:focus {
                border: 4px solid rgba(96, 165, 250, 0.95);
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(240, 249, 255, 0.98),
                    stop:0.2 rgba(224, 242, 254, 0.95),
                    stop:0.4 rgba(186, 230, 253, 0.9),
                    stop:0.6 rgba(224, 242, 254, 0.95),
                    stop:0.8 rgba(240, 249, 255, 0.98),
                    stop:1 rgba(255, 255, 255, 0.95));
                box-shadow: 0 6px 16px rgba(96, 165, 250, 0.4);
                transform: scale(1.02);
            }
        """)
        form_layout.addRow(create_styled_label("اسم العميل", "🧑‍💼", True), self.name_edit)

        # العنوان - مع استغلال أفضل للمساحة
        self.address_edit = QLineEdit()
        self.address_edit.setPlaceholderText("أدخل العنوان الكامل للعميل (الشارع، المدينة، المحافظة)...")
        self.address_edit.setStyleSheet("""
            QLineEdit {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.95),
                    stop:0.2 rgba(248, 250, 252, 0.98),
                    stop:0.4 rgba(241, 245, 249, 0.95),
                    stop:0.6 rgba(248, 250, 252, 0.98),
                    stop:0.8 rgba(255, 255, 255, 0.95),
                    stop:1 rgba(226, 232, 240, 0.9));
                border: 3px solid rgba(96, 165, 250, 0.7);
                border-radius: 12px;
                padding: 12px 20px;
                font-size: 16px;
                font-weight: bold;
                color: #1f2937;
                min-height: 30px;
                min-width: 350px;
            }
            QLineEdit:focus {
                border: 4px solid rgba(96, 165, 250, 0.95);
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(240, 249, 255, 0.98),
                    stop:0.2 rgba(224, 242, 254, 0.95),
                    stop:0.4 rgba(186, 230, 253, 0.9),
                    stop:0.6 rgba(224, 242, 254, 0.95),
                    stop:0.8 rgba(240, 249, 255, 0.98),
                    stop:1 rgba(255, 255, 255, 0.95));
                box-shadow: 0 6px 16px rgba(96, 165, 250, 0.4);
                transform: scale(1.02);
            }
        """)
        form_layout.addRow(create_styled_label("العنوان", "🏠"), self.address_edit)

        # البريد الإلكتروني - مع استغلال أفضل للمساحة
        self.email_edit = QLineEdit()
        self.email_edit.setPlaceholderText("أدخل البريد الإلكتروني الكامل (<EMAIL>)...")
        self.email_edit.setStyleSheet("""
            QLineEdit {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.95),
                    stop:0.2 rgba(248, 250, 252, 0.98),
                    stop:0.4 rgba(241, 245, 249, 0.95),
                    stop:0.6 rgba(248, 250, 252, 0.98),
                    stop:0.8 rgba(255, 255, 255, 0.95),
                    stop:1 rgba(226, 232, 240, 0.9));
                border: 3px solid rgba(96, 165, 250, 0.7);
                border-radius: 12px;
                padding: 12px 20px;
                font-size: 16px;
                font-weight: bold;
                color: #1f2937;
                min-height: 30px;
                min-width: 350px;
            }
            QLineEdit:focus {
                border: 4px solid rgba(96, 165, 250, 0.95);
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(240, 249, 255, 0.98),
                    stop:0.2 rgba(224, 242, 254, 0.95),
                    stop:0.4 rgba(186, 230, 253, 0.9),
                    stop:0.6 rgba(224, 242, 254, 0.95),
                    stop:0.8 rgba(240, 249, 255, 0.98),
                    stop:1 rgba(255, 255, 255, 0.95));
                box-shadow: 0 6px 16px rgba(96, 165, 250, 0.4);
                transform: scale(1.02);
            }
        """)
        form_layout.addRow(create_styled_label("البريد الإلكتروني", "📧"), self.email_edit)

        # أرقام الهاتف (نظام تعدد الأرقام)
        phone_container = QVBoxLayout()

        # الهاتف الأساسي - مع استغلال أفضل للمساحة
        self.phone_edit = QLineEdit()
        self.phone_edit.setPlaceholderText("أدخل رقم الهاتف الأساسي (مثال: 01234567890)...")
        self.phone_edit.setStyleSheet("""
            QLineEdit {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.95),
                    stop:0.2 rgba(248, 250, 252, 0.98),
                    stop:0.4 rgba(241, 245, 249, 0.95),
                    stop:0.6 rgba(248, 250, 252, 0.98),
                    stop:0.8 rgba(255, 255, 255, 0.95),
                    stop:1 rgba(226, 232, 240, 0.9));
                border: 3px solid rgba(96, 165, 250, 0.7);
                border-radius: 12px;
                padding: 12px 20px;
                font-size: 16px;
                font-weight: bold;
                color: #1f2937;
                min-height: 30px;
                min-width: 350px;
            }
            QLineEdit:focus {
                border: 4px solid rgba(96, 165, 250, 0.95);
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(240, 249, 255, 0.98),
                    stop:0.2 rgba(224, 242, 254, 0.95),
                    stop:0.4 rgba(186, 230, 253, 0.9),
                    stop:0.6 rgba(224, 242, 254, 0.95),
                    stop:0.8 rgba(240, 249, 255, 0.98),
                    stop:1 rgba(255, 255, 255, 0.95));
                box-shadow: 0 6px 16px rgba(96, 165, 250, 0.4);
                transform: scale(1.02);
            }
        """)
        phone_container.addWidget(self.phone_edit)

        # أرقام إضافية
        self.additional_phones = []
        self.phone_widgets = []

        # زر إضافة رقم هاتف مع ارتفاع مقلل ومنزل للأسفل
        add_phone_btn = QPushButton("➕ إضافة رقم")
        self.style_advanced_button(add_phone_btn, 'info')
        add_phone_btn.clicked.connect(self.add_phone_field)

        # تقليل ارتفاع الزر وإنزاله للأسفل
        add_phone_btn.setStyleSheet(add_phone_btn.styleSheet() + """
            QPushButton {
                margin-top: 15px;
                margin-bottom: 8px;
                margin-left: 5px;
                margin-right: 5px;
                max-height: 35px;
                min-height: 35px;
                padding: 6px 12px;
                font-size: 14px;
            }
        """)

        phone_container.addWidget(add_phone_btn)

        form_layout.addRow(create_styled_label("أرقام الهاتف", "📱"), phone_container)

        # الرصيد الابتدائي - مع استغلال أفضل للمساحة
        self.balance_spinbox = QDoubleSpinBox()
        self.balance_spinbox.setRange(-999999999, 999999999)
        self.balance_spinbox.setDecimals(0)  # إزالة الأرقام العشرية
        self.balance_spinbox.setValue(0)
        self.balance_spinbox.setSuffix(" جنيه")
        self.balance_spinbox.setButtonSymbols(QDoubleSpinBox.NoButtons)  # إزالة الأسهم
        self.balance_spinbox.setStyleSheet("""
            QDoubleSpinBox {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.95),
                    stop:0.2 rgba(248, 250, 252, 0.98),
                    stop:0.4 rgba(241, 245, 249, 0.95),
                    stop:0.6 rgba(248, 250, 252, 0.98),
                    stop:0.8 rgba(255, 255, 255, 0.95),
                    stop:1 rgba(226, 232, 240, 0.9));
                border: 3px solid rgba(96, 165, 250, 0.7);
                border-radius: 12px;
                padding: 12px 20px;
                font-size: 18px;
                font-weight: bold;
                color: #1f2937;
                min-height: 30px;
                min-width: 350px;
            }
            QDoubleSpinBox:focus {
                border: 4px solid rgba(96, 165, 250, 0.95);
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(240, 249, 255, 0.98),
                    stop:0.2 rgba(224, 242, 254, 0.95),
                    stop:0.4 rgba(186, 230, 253, 0.9),
                    stop:0.6 rgba(224, 242, 254, 0.95),
                    stop:0.8 rgba(240, 249, 255, 0.98),
                    stop:1 rgba(255, 255, 255, 0.95));
                box-shadow: 0 6px 16px rgba(96, 165, 250, 0.4);
                transform: scale(1.02);
            }
        """)
        form_layout.addRow(create_styled_label("الرصيد الابتدائي", "💰"), self.balance_spinbox)

        # الملاحظات - مع استغلال أفضل للمساحة
        self.notes_edit = QTextEdit()
        self.notes_edit.setPlaceholderText("أدخل أي ملاحظات أو تفاصيل إضافية عن العميل هنا...")
        self.notes_edit.setMaximumHeight(100)  # ارتفاع أكبر قليلاً
        self.notes_edit.setMinimumWidth(350)   # عرض أكبر
        self.notes_edit.setStyleSheet("""
            QTextEdit {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.95),
                    stop:0.2 rgba(248, 250, 252, 0.98),
                    stop:0.4 rgba(241, 245, 249, 0.95),
                    stop:0.6 rgba(248, 250, 252, 0.98),
                    stop:0.8 rgba(255, 255, 255, 0.95),
                    stop:1 rgba(226, 232, 240, 0.9));
                border: 3px solid rgba(96, 165, 250, 0.7);
                border-radius: 12px;
                padding: 15px 20px;
                font-size: 16px;
                font-weight: bold;
                color: #1f2937;
                line-height: 1.4;
            }
            QTextEdit:focus {
                border: 4px solid rgba(96, 165, 250, 0.95);
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(240, 249, 255, 0.98),
                    stop:0.2 rgba(224, 242, 254, 0.95),
                    stop:0.4 rgba(186, 230, 253, 0.9),
                    stop:0.6 rgba(224, 242, 254, 0.95),
                    stop:0.8 rgba(240, 249, 255, 0.98),
                    stop:1 rgba(255, 255, 255, 0.95));
                box-shadow: 0 6px 16px rgba(96, 165, 250, 0.4);
                transform: scale(1.01);
            }
        """)
        form_layout.addRow(create_styled_label("الملاحظات", "📝"), self.notes_edit)

        layout.addLayout(form_layout)

        # أزرار التحكم
        buttons_layout = QHBoxLayout()

        # زر الحفظ مطابق للفواتير - يتغير النص حسب الوضع
        if self.is_edit_mode:
            save_button = QPushButton("✏️ تحديث")
        else:
            save_button = QPushButton("💾 حفظ")
        self.style_advanced_button(save_button, 'emerald')
        save_button.clicked.connect(self.save_client)

        # زر الإلغاء - أحمر للخطر
        cancel_button = QPushButton("❌ إلغاء")
        self.style_advanced_button(cancel_button, 'danger')
        cancel_button.clicked.connect(self.reject)

        buttons_layout.addWidget(cancel_button)
        buttons_layout.addWidget(save_button)
        layout.addLayout(buttons_layout)

    def add_phone_field(self):
        """إضافة حقل رقم هاتف إضافي مطور - نسخة محسنة بدون أخطاء"""
        try:
            # إنشاء حقل الرقم الجديد مع عكس الكتابة للتوحيد
            phone_edit = QLineEdit()
            phone_edit.setPlaceholderText(f"أدخل رقم الهاتف الإضافي {len(self.additional_phones) + 1}...")
            phone_edit.setLayoutDirection(Qt.RightToLeft)  # عكس الكتابة للتوحيد
            phone_edit.setAlignment(Qt.AlignRight)  # محاذاة النص لليمين
            phone_edit.setStyleSheet("""
                QLineEdit {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 rgba(255, 255, 255, 0.95),
                        stop:0.2 rgba(248, 250, 252, 0.98),
                        stop:0.4 rgba(241, 245, 249, 0.95),
                        stop:0.6 rgba(248, 250, 252, 0.98),
                        stop:0.8 rgba(255, 255, 255, 0.95),
                        stop:1 rgba(226, 232, 240, 0.9));
                    border: 3px solid rgba(139, 92, 246, 0.7);
                    border-radius: 10px;
                    padding: 10px 15px;
                    font-size: 14px;
                    font-weight: bold;
                    color: #1f2937;
                    min-height: 25px;
                    min-width: 280px;
                    max-width: 320px;
                    margin-left: 10px;
                    margin-right: 25px;
                    margin-top: 3px;
                    margin-bottom: 3px;
                    text-align: right;
                }
                QLineEdit:focus {
                    border: 4px solid rgba(139, 92, 246, 0.95);
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 rgba(245, 243, 255, 0.98),
                        stop:0.2 rgba(237, 233, 254, 0.95),
                        stop:0.4 rgba(221, 214, 254, 0.9),
                        stop:0.6 rgba(237, 233, 254, 0.95),
                        stop:0.8 rgba(245, 243, 255, 0.98),
                        stop:1 rgba(255, 255, 255, 0.95));
                    box-shadow: 0 4px 12px rgba(139, 92, 246, 0.3);
                }
            """)

            # إنشاء حاوي أفقي للحقل وزر الحذف مع مسافات مقربة
            phone_container = QHBoxLayout()
            phone_container.setContentsMargins(15, 10, 15, 10)
            phone_container.setSpacing(20)  # مسافة مقربة بين العناصر

            # زر الحذف مطور مع تجنب التداخل
            remove_btn = QPushButton("🗑️ حذف")
            remove_btn.setFixedSize(75, 32)  # حجم أصغر قليلاً لتجنب التداخل
            self.style_advanced_button(remove_btn, 'danger')
            remove_btn.clicked.connect(lambda: self.remove_phone_field_improved(phone_edit, phone_container))

            # تحسين تنسيق زر الحذف مع تقريبه من الحقل
            remove_btn.setStyleSheet(remove_btn.styleSheet() + """
                QPushButton {
                    margin-left: 8px;
                    margin-right: 8px;
                    margin-top: 3px;
                    margin-bottom: 3px;
                    font-size: 12px;
                    padding: 5px 8px;
                    min-width: 60px;
                    max-width: 60px;
                }
            """)

            # إضافة العناصر للحاوي مع ترتيب معكوس - الحقل أولاً ثم الزر
            phone_container.addWidget(phone_edit)  # الحقل أولاً في اليمين
            phone_container.addWidget(remove_btn)  # الزر ثانياً في اليسار
            phone_container.addStretch()  # إضافة مساحة مرنة في النهاية

            # إضافة إلى القائمة
            self.additional_phones.append(phone_edit)
            self.phone_widgets.append(phone_container)

            # إضافة إلى الواجهة بطريقة محسنة
            self.add_phone_to_form(phone_container)

        except Exception as e:
            QMessageBox.warning(self, "خطأ", f"حدث خطأ في إضافة حقل الهاتف: {str(e)}")

    def add_phone_to_form(self, phone_container):
        """إضافة حقل الهاتف إلى النموذج بطريقة محسنة"""
        try:
            # البحث عن حاوي أرقام الهاتف الرئيسي
            main_layout = self.layout()
            form_layout = None

            # العثور على form_layout
            for i in range(main_layout.count()):
                item = main_layout.itemAt(i)
                if item and hasattr(item, 'layout') and item.layout():
                    if isinstance(item.layout(), QFormLayout):
                        form_layout = item.layout()
                        break

            if form_layout:
                # البحث عن صف أرقام الهاتف
                phone_row_index = -1
                for i in range(form_layout.rowCount()):
                    label_item = form_layout.itemAt(i, QFormLayout.LabelRole)
                    if label_item and label_item.widget():
                        label = label_item.widget()
                        if hasattr(label, 'text') and "أرقام الهاتف" in label.text():
                            phone_row_index = i
                            break

                if phone_row_index >= 0:
                    # إنشاء widget للحاوي الجديد مع تنسيق محسن
                    phone_widget = QWidget()
                    phone_widget.setLayout(phone_container)

                    # تحسين تنسيق الحاوي لتجنب التداخلات
                    phone_widget.setStyleSheet("""
                        QWidget {
                            background: transparent;
                            border: none;
                            margin-top: 5px;
                            margin-bottom: 5px;
                            margin-left: 10px;
                            margin-right: 10px;
                        }
                    """)

                    # إضافة الحقل الجديد بعد الصف الأساسي
                    insert_position = phone_row_index + len(self.phone_widgets)
                    form_layout.insertRow(insert_position, "", phone_widget)

        except Exception as e:
            print(f"خطأ في إضافة الحقل للنموذج: {e}")

    def remove_phone_field_improved(self, phone_edit, phone_container):
        """حذف حقل رقم هاتف محسن"""
        try:
            # إزالة من القوائم
            if phone_edit in self.additional_phones:
                self.additional_phones.remove(phone_edit)
            if phone_container in self.phone_widgets:
                self.phone_widgets.remove(phone_container)

            # إزالة من الواجهة
            self.remove_phone_from_form(phone_container)

        except Exception as e:
            QMessageBox.warning(self, "خطأ", f"حدث خطأ في حذف حقل الهاتف: {str(e)}")

    def remove_phone_from_form(self, phone_container):
        """إزالة حقل الهاتف من النموذج"""
        try:
            # البحث عن العنصر في النموذج وحذفه
            main_layout = self.layout()

            for i in range(main_layout.count()):
                item = main_layout.itemAt(i)
                if item and hasattr(item, 'layout') and item.layout():
                    if isinstance(item.layout(), QFormLayout):
                        form_layout = item.layout()

                        # البحث عن العنصر وحذفه
                        for j in range(form_layout.rowCount()):
                            field_item = form_layout.itemAt(j, QFormLayout.FieldRole)
                            if field_item and field_item.widget():
                                widget = field_item.widget()
                                if hasattr(widget, 'layout') and widget.layout() == phone_container:
                                    # حذف الصف
                                    form_layout.removeRow(j)
                                    widget.deleteLater()
                                    return
                        break

        except Exception as e:
            print(f"خطأ في إزالة الحقل من النموذج: {e}")

    def load_client_data(self):
        """تحميل بيانات العميل في وضع التعديل"""
        if not self.client:
            return

        try:
            # تحميل البيانات الأساسية
            self.name_edit.setText(self.client.name or "")
            self.address_edit.setText(self.client.address or "")
            self.email_edit.setText(self.client.email or "")
            self.balance_spinbox.setValue(self.client.balance or 0.0)
            self.notes_edit.setPlainText(self.client.notes or "")

            # تحميل أرقام الهاتف
            if self.client.phone:
                # تقسيم أرقام الهاتف المفصولة بفواصل
                phone_numbers = [phone.strip() for phone in self.client.phone.split(",") if phone.strip()]

                if phone_numbers:
                    # الرقم الأول في الحقل الأساسي
                    self.phone_edit.setText(phone_numbers[0])

                    # الأرقام الإضافية
                    for i, phone in enumerate(phone_numbers[1:], 1):
                        if i <= len(self.additional_phones):
                            # استخدام الحقول الموجودة
                            self.additional_phones[i-1].setText(phone)
                        else:
                            # إضافة حقول جديدة للأرقام الإضافية
                            self.add_phone_field()
                            if self.additional_phones:
                                self.additional_phones[-1].setText(phone)

        except Exception as e:
            print(f"خطأ في تحميل بيانات العميل: {e}")

    def save_client(self):
        """حفظ العميل الجديد أو تحديث العميل الموجود مع نظام تعدد الأرقام"""
        try:
            # التحقق من صحة البيانات
            name = self.name_edit.text().strip()
            if not name:
                QMessageBox.warning(self, "تحذير", "يرجى إدخال اسم العميل")
                self.name_edit.setFocus()
                return

            # جمع أرقام الهاتف
            phone_numbers = []

            # الرقم الأساسي
            main_phone = self.phone_edit.text().strip()
            if main_phone:
                phone_numbers.append(main_phone)

            # الأرقام الإضافية
            for phone_edit in self.additional_phones:
                additional_phone = phone_edit.text().strip()
                if additional_phone:
                    phone_numbers.append(additional_phone)

            # تحويل قائمة الأرقام إلى نص مفصول بفواصل
            phone_string = ", ".join(phone_numbers) if phone_numbers else None

            if self.is_edit_mode:
                # تحديث العميل الموجود
                self.client.name = name
                self.client.address = self.address_edit.text().strip() or None
                self.client.email = self.email_edit.text().strip() or None
                self.client.phone = phone_string
                self.client.balance = self.balance_spinbox.value()
                self.client.notes = self.notes_edit.toPlainText().strip() or None

                # حفظ التغييرات
                self.session.commit()

                # رسالة نجاح التحديث
                phone_info = f"\nأرقام الهاتف: {phone_string}" if phone_string else "\nلم يتم إدخال أرقام هاتف"
                QMessageBox.information(
                    self,
                    "تم التحديث",
                    f"تم تحديث بيانات العميل '{name}' بنجاح!{phone_info}"
                )
            else:
                # إنشاء عميل جديد
                new_client = Client(
                    name=name,
                    address=self.address_edit.text().strip() or None,
                    email=self.email_edit.text().strip() or None,
                    phone=phone_string,  # حفظ جميع الأرقام
                    balance=self.balance_spinbox.value(),
                    notes=self.notes_edit.toPlainText().strip() or None
                )

                # حفظ في قاعدة البيانات
                self.session.add(new_client)
                self.session.commit()

                # رسالة نجاح الإضافة
                phone_info = f"\nأرقام الهاتف: {phone_string}" if phone_string else "\nلم يتم إدخال أرقام هاتف"
                QMessageBox.information(
                    self,
                    "نجح",
                    f"تم إضافة العميل '{name}' بنجاح!{phone_info}"
                )

            self.accept()

        except Exception as e:
            self.session.rollback()
            QMessageBox.critical(self, "خطأ", f"حدث خطأ في حفظ العميل: {str(e)}")

    def mousePressEvent(self, event):
        """بداية سحب النافذة"""
        if event.button() == Qt.LeftButton:
            self.drag_position = event.globalPos() - self.frameGeometry().topLeft()
            event.accept()

    def mouseMoveEvent(self, event):
        """سحب النافذة"""
        if event.buttons() == Qt.LeftButton and self.drag_position:
            self.move(event.globalPos() - self.drag_position)
            event.accept()

    def customize_title_bar(self):
        """تخصيص شريط العنوان الطبيعي مع التدرجات والألوان الجديدة المتطورة"""
        try:
            # إنشاء أيقونة مخصصة متطورة مع تدرجات جديدة
            pixmap = QPixmap(48, 48)  # حجم أكبر للوضوح
            pixmap.fill(Qt.transparent)

            painter = QPainter(pixmap)
            painter.setRenderHint(QPainter.Antialiasing)
            painter.setRenderHint(QPainter.SmoothPixmapTransform)

            # إنشاء تدرج متطور للأيقونة
            gradient = QRadialGradient(24, 24, 20)
            # تدرج متطور مع ألوان جديدة
            gradient.setColorAt(0.0, QColor(15, 23, 42))      # أزرق داكن عميق
            gradient.setColorAt(0.2, QColor(30, 41, 59))      # أزرق رمادي
            gradient.setColorAt(0.4, QColor(37, 99, 235))     # أزرق حيوي
            gradient.setColorAt(0.6, QColor(59, 130, 246))    # أزرق فاتح
            gradient.setColorAt(0.8, QColor(96, 165, 250))    # أزرق ساطع
            gradient.setColorAt(1.0, QColor(147, 197, 253))   # أزرق فاتح جداً

            brush = QBrush(gradient)
            painter.setBrush(brush)

            # إضافة حدود ذهبية متطورة
            pen = QPen(QColor(251, 191, 36), 3)  # ذهبي
            pen.setStyle(Qt.SolidLine)
            painter.setPen(pen)

            # رسم دائرة متطورة مع ظل
            painter.drawEllipse(3, 3, 42, 42)

            # إضافة أيقونة العميل في المنتصف - يتغير حسب الوضع
            painter.setPen(QColor(255, 255, 255))
            font = painter.font()
            font.setPointSize(20)
            font.setBold(True)
            painter.setFont(font)

            # اختيار الرمز حسب الوضع
            icon_text = "✏️" if self.is_edit_mode else "🤝"
            painter.drawText(pixmap.rect(), Qt.AlignCenter, icon_text)

            painter.end()

            icon = QIcon(pixmap)
            self.setWindowIcon(icon)

            # تطبيق تدرجات متطورة على شريط العنوان مطابق للموردين والعمال
            self.apply_advanced_title_bar_styling()

        except Exception as e:
            print(f"خطأ في تخصيص شريط العنوان: {e}")

    def apply_advanced_title_bar_styling(self):
        """تطبيق تصميم متطور على شريط العنوان"""
        TitleBarStyler.apply_advanced_title_bar_styling(self)


class ClientInfoDialog(QDialog):
    """
    نافذة عرض معلومات العميل التفصيلية - النموذج المرجعي

    هذه النافذة تعتبر المرجع الأساسي لجميع نوافذ المعلومات في النظام
    المميزات:
    - تصميم موحد ومتسق مع حواف مربعة
    - ألوان واضحة ومتباينة للبيانات
    - تخطيط منظم ومرن
    - أزرار وظيفية متطورة
    - أيقونات محسنة ومتطورة
    """

    def __init__(self, parent=None, client=None):
        super().__init__(parent)
        self.client = client
        self.parent_widget = parent
        self.setup_ui()

    def setup_ui(self):
        """إعداد واجهة نافذة المعلومات المرجعية"""
        # ═══════════════════════════════════════════════════════════════
        # إعدادات النافذة الأساسية
        # ═══════════════════════════════════════════════════════════════
        self.setWindowTitle("👤📋 معلومات العميل - نظام إدارة العملاء المتطور والشامل")
        self.setWindowFlags(Qt.Dialog | Qt.WindowTitleHint | Qt.WindowCloseButtonHint)
        self.setWindowIcon(self.create_window_icon())
        self.customize_title_bar()
        self.setModal(True)
        self.resize(850, 780)  # حجم محسن للعرض الأمثل

        # ═══════════════════════════════════════════════════════════════
        # تصميم النافذة والخلفية المرجعية
        # ═══════════════════════════════════════════════════════════════
        self.setStyleSheet(self.get_reference_styling())

        # ═══════════════════════════════════════════════════════════════
        # التخطيط الرئيسي المرجعي
        # ═══════════════════════════════════════════════════════════════
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 15, 20, 15)
        layout.setSpacing(10)
        self.setGraphicsEffect(self.create_shadow_effect())

        # عنوان النافذة الداخلي المحسن
        title_container = QFrame()
        title_container.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(34, 197, 94, 0.25),
                    stop:0.2 rgba(59, 130, 246, 0.3),
                    stop:0.5 rgba(139, 92, 246, 0.28),
                    stop:0.8 rgba(168, 85, 247, 0.25),
                    stop:1 rgba(236, 72, 153, 0.2));
                border: 3px solid rgba(255, 255, 255, 0.3);
                border-radius: 6px;
                margin: 1px 0px 5px 0px;  /* تقليل margin أكثر لصالح البيانات */
                padding: 2px;  /* تقليل padding أكثر لصالح البيانات */
                max-height: 50px;  /* تحديد ارتفاع أقصى أقل */
                min-height: 45px;  /* تحديد ارتفاع أدنى أقل */
            }
        """)

        title_layout = QHBoxLayout(title_container)  # تغيير إلى HBoxLayout لتوفير مساحة
        title_layout.setContentsMargins(8, 1, 8, 5)  # رفع النص درجتين للأعلى (تقليل top margin)
        title_layout.setSpacing(5)  # تقليل spacing

        # العنوان المضغوط مع اسم العميل - في المنتصف مع خط أكبر
        title_text = f"🧑‍💼 معلومات العميل: {self.client.name if self.client and self.client.name else 'غير محدد'}"
        main_title = QLabel(title_text)
        main_title.setAlignment(Qt.AlignCenter | Qt.AlignVCenter)  # في المنتصف عمودياً وأفقياً
        main_title.setStyleSheet("""
            QLabel {
                color: #FFFFFF;
                font-size: 20px;  /* تكبير الخط لإبراز اسم العميل */
                font-weight: 900;  /* جعل الخط أكثر سمكاً */
                text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
                padding: 3px 15px 10px 15px;  /* رفع النص درجتين للأعلى (تقليل top padding) */
                background: transparent;
                border: none;
                line-height: 1.2;  /* تحسين المسافة بين الأسطر */
            }
        """)
        title_layout.addWidget(main_title, 1)  # إعطاء العنوان مساحة متوسعة

        # تم إزالة العنوان الفرعي لتجنب تكرار اسم العميل

        layout.addWidget(title_container)

        # إنشاء منطقة التمرير المحسنة للمعلومات - مساحة أكبر
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)  # سياسة حجم متوسعة
        scroll_area.setStyleSheet("""
            QScrollArea {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(15, 23, 42, 0.3),
                    stop:0.3 rgba(30, 41, 59, 0.25),
                    stop:0.7 rgba(51, 65, 85, 0.2),
                    stop:1 rgba(71, 85, 105, 0.15));
                border: 2px solid rgba(255, 255, 255, 0.1);
                border-radius: 15px;
                margin: 5px;
                padding: 5px;
            }
            QScrollBar:vertical {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 rgba(71, 85, 105, 0.4),
                    stop:1 rgba(100, 116, 139, 0.3));
                width: 16px;
                border-radius: 8px;
                border: 1px solid rgba(255, 255, 255, 0.1);
                margin: 2px;
            }
            QScrollBar::handle:vertical {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 rgba(59, 130, 246, 0.6),
                    stop:0.5 rgba(139, 92, 246, 0.5),
                    stop:1 rgba(34, 197, 94, 0.4));
                border: 1px solid rgba(255, 255, 255, 0.2);
                border-radius: 7px;
                min-height: 30px;
                margin: 1px;
            }
            QScrollBar::handle:vertical:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 rgba(59, 130, 246, 0.8),
                    stop:0.5 rgba(139, 92, 246, 0.7),
                    stop:1 rgba(34, 197, 94, 0.6));
                border: 2px solid rgba(255, 255, 255, 0.3);
            }
            QScrollBar::handle:vertical:pressed {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 rgba(37, 99, 235, 0.9),
                    stop:0.5 rgba(124, 58, 237, 0.8),
                    stop:1 rgba(21, 128, 61, 0.7));
            }
            QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {
                background: transparent;
                height: 0px;
            }
            QScrollBar::add-page:vertical, QScrollBar::sub-page:vertical {
                background: transparent;
            }
        """)

        # محتوى المعلومات المحسن
        info_widget = QWidget()
        info_widget.setStyleSheet("""
            QWidget {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(15, 23, 42, 0.1),
                    stop:0.2 rgba(30, 41, 59, 0.08),
                    stop:0.5 rgba(51, 65, 85, 0.06),
                    stop:0.8 rgba(71, 85, 105, 0.08),
                    stop:1 rgba(100, 116, 139, 0.1));
                border-radius: 12px;
                padding: 10px;
            }
        """)

        info_layout = QVBoxLayout(info_widget)
        info_layout.setContentsMargins(15, 15, 15, 15)
        info_layout.setSpacing(25)  # زيادة المسافة بين الأقسام لاستغلال المساحة الإضافية

        # معلومات العميل
        if self.client:
            # قسم المعلومات الأساسية المحسن مع تفاصيل إضافية
            basic_info = [
                ("🔢 المعرف الفريد", f"#{str(self.client.id).zfill(8)}"),  # تطوير الأيقونة وزيادة الأرقام
                ("📧 البريد الإلكتروني", self.client.email or "غير محدد"),
                ("📍 العنوان الكامل", self.client.address or "غير محدد"),
                ("🏷️ حالة العميل", "نشط ✅" if self.client.balance is not None else "غير نشط ❌"),
                ("📊 مستوى البيانات", self.get_data_completeness())
            ]
            self.add_info_section(info_layout, "📋 المعلومات الأساسية والشخصية", basic_info)

            # قسم المعلومات المالية المحسن
            balance_color = self.get_balance_color()
            balance_text = f"{self.client.balance:.0f} جنيه" if self.client.balance else "0 جنيه"
            self.add_info_section(info_layout, "💰 المعلومات المالية", [
                ("💵 الرصيد الحالي", f"{balance_color} {balance_text}"),
                ("📊 حالة الحساب", self.get_account_status()),
                ("💳 تصنيف العميل", self.get_client_type()),
                ("📈 مستوى النشاط", self.get_activity_level()),
                ("⚖️ تقييم الائتمان", self.get_credit_rating())
            ])

            # قسم معلومات الاتصال المحسن
            self.add_info_section(info_layout, "📞 معلومات الاتصال", [
                ("📱 الهاتف الرئيسي", self.client.phone or "غير محدد"),
                ("📞 أرقام إضافية", self.get_additional_phones()),
                ("📧 حالة البريد", self.get_email_status()),
                ("📲 طرق التواصل", self.get_contact_methods())
            ])

            # قسم معلومات التاريخ والإحصائيات
            self.add_info_section(info_layout, "📅 معلومات التاريخ والإحصائيات", [
                ("📅 تاريخ الإضافة", self.client.created_at.strftime("%Y-%m-%d %H:%M") if self.client.created_at else "غير محدد"),
                ("⏰ آخر تحديث", self.get_last_update()),
                ("📈 مدة العضوية", self.get_membership_duration()),
                ("📊 عدد المعاملات", self.get_transactions_count()),
                ("💼 إجمالي التعاملات", self.get_total_transactions())
            ])

            # قسم الملاحظات والتفاصيل الإضافية
            self.add_info_section(info_layout, "📝 ملاحظات وتفاصيل إضافية", [
                ("📝 الملاحظات", self.client.notes or "لا توجد ملاحظات"),
                ("🔍 معلومات إضافية", self.get_additional_info()),
                ("📋 ملخص الحساب", self.get_account_summary())
            ])

        scroll_area.setWidget(info_widget)
        layout.addWidget(scroll_area)

        # أزرار التحكم
        self.create_control_buttons(layout)

        # تطبيق تصميم شريط العنوان
        self.apply_advanced_title_bar_styling()

    def create_window_icon(self):
        """إنشاء أيقونة النافذة المطورة للتطابق مع رقم المعرف"""
        try:
            from PyQt5.QtGui import QPixmap, QPainter, QBrush, QRadialGradient, QIcon, QPen, QColor, QFont
            from PyQt5.QtCore import Qt

            # إنشاء أيقونة مخصصة 32x32
            pixmap = QPixmap(32, 32)
            pixmap.fill(Qt.transparent)

            painter = QPainter(pixmap)
            painter.setRenderHint(QPainter.Antialiasing)
            painter.setRenderHint(QPainter.TextAntialiasing)

            # تدرج دائري للخلفية مطور
            gradient = QRadialGradient(16, 16, 16)
            gradient.setColorAt(0, QColor(59, 130, 246))  # أزرق
            gradient.setColorAt(0.5, QColor(139, 92, 246))  # بنفسجي
            gradient.setColorAt(1, QColor(34, 197, 94))  # أخضر

            painter.setBrush(QBrush(gradient))
            painter.setPen(QPen(QColor(255, 255, 255, 150), 2))
            painter.drawRoundedRect(2, 2, 28, 28, 4, 4)  # مربع مدور بدلاً من دائرة

            # رسم رقم المعرف المطور
            painter.setPen(QPen(QColor(255, 255, 255), 2))
            painter.setFont(QFont("Arial", 10, QFont.Bold))

            # رسم رمز # للمعرف
            painter.drawText(6, 12, "#")

            # رسم أرقام المعرف
            painter.setFont(QFont("Arial", 8, QFont.Bold))
            painter.drawText(12, 12, "ID")

            # رسم أيقونة شخص صغيرة
            painter.setPen(QPen(QColor(255, 255, 255), 1))
            painter.setBrush(QBrush(QColor(255, 255, 255)))

            # رأس الشخص (أصغر)
            painter.drawEllipse(10, 18, 4, 4)

            # جسم الشخص (أصغر)
            painter.drawEllipse(8, 24, 8, 6)

            painter.end()

            return QIcon(pixmap)

        except Exception as e:
            print(f"خطأ في إنشاء أيقونة النافذة: {e}")
            return QIcon()  # أيقونة فارغة في حالة الخطأ

    def create_shadow_effect(self):
        """إنشاء تأثير الظل للنافذة"""
        try:
            from PyQt5.QtWidgets import QGraphicsDropShadowEffect
            from PyQt5.QtCore import Qt
            from PyQt5.QtGui import QColor

            shadow = QGraphicsDropShadowEffect()
            shadow.setBlurRadius(25)
            shadow.setXOffset(5)
            shadow.setYOffset(5)
            shadow.setColor(QColor(0, 0, 0, 120))
            return shadow
        except Exception as e:
            print(f"خطأ في إنشاء تأثير الظل: {e}")
            return None

    def add_info_section(self, layout, title, items):
        """إضافة قسم معلومات محسن مع تصميم متطور"""
        # إطار القسم الرئيسي
        section_frame = QFrame()
        section_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.08),
                    stop:0.3 rgba(248, 250, 252, 0.12),
                    stop:0.7 rgba(241, 245, 249, 0.10),
                    stop:1 rgba(226, 232, 240, 0.08));
                border: 2px solid rgba(255, 255, 255, 0.15);
                border-radius: 15px;
                margin: 8px 0px;
                padding: 0px;
            }
        """)

        section_layout = QVBoxLayout(section_frame)
        section_layout.setContentsMargins(15, 15, 15, 15)
        section_layout.setSpacing(12)

        # عنوان القسم المحسن
        section_title = QLabel(title)
        section_title.setAlignment(Qt.AlignCenter)
        section_title.setStyleSheet("""
            QLabel {
                color: #ffffff;
                font-size: 18px;
                font-weight: bold;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(59, 130, 246, 0.3),
                    stop:0.5 rgba(139, 92, 246, 0.25),
                    stop:1 rgba(34, 197, 94, 0.2));
                border: 2px solid rgba(255, 255, 255, 0.25);
                border-radius: 12px;
                padding: 12px 20px;
                margin-bottom: 10px;
                text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
            }
        """)
        section_layout.addWidget(section_title)

        # عناصر القسم المحسنة
        for i, (label, value) in enumerate(items):
            item_widget = QWidget()
            item_layout = QHBoxLayout(item_widget)
            item_layout.setContentsMargins(12, 8, 12, 8)
            item_layout.setSpacing(15)

            # خلفية متدرجة للعناصر
            item_widget.setStyleSheet(f"""
                QWidget {{
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                        stop:0 rgba(255, 255, 255, {0.03 + (i % 2) * 0.02}),
                        stop:0.5 rgba(248, 250, 252, {0.05 + (i % 2) * 0.02}),
                        stop:1 rgba(241, 245, 249, {0.03 + (i % 2) * 0.02}));
                    border: 1px solid rgba(255, 255, 255, 0.12);
                    border-radius: 10px;
                    margin: 2px 0px;
                }}
                QWidget:hover {{
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                        stop:0 rgba(59, 130, 246, 0.08),
                        stop:0.5 rgba(139, 92, 246, 0.06),
                        stop:1 rgba(34, 197, 94, 0.05));
                    border: 1px solid rgba(255, 255, 255, 0.2);
                }}
            """)

            # التسمية المحسنة - نصوص أوضح
            label_widget = QLabel(label)
            label_widget.setStyleSheet("""
                QLabel {
                    color: #FFFFFF;
                    font-size: 16px;
                    font-weight: 800;
                    min-width: 180px;
                    max-width: 180px;
                    padding: 12px 15px;
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 rgba(71, 85, 105, 0.6),
                        stop:1 rgba(100, 116, 139, 0.5));
                    border: 2px solid rgba(255, 255, 255, 0.25);
                    border-radius: 4px;
                    text-shadow: 2px 2px 3px rgba(0, 0, 0, 0.7);
                }
            """)

            # القيمة المحسنة مع ألوان مخصصة
            value_widget = QLabel(str(value))

            # تحديد لون القيمة حسب المحتوى
            value_color = self.get_value_color(label, str(value))

            value_widget.setStyleSheet(f"""
                QLabel {{
                    color: {value_color};
                    font-size: 15px;
                    font-weight: 600;
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 rgba(255, 255, 255, 0.08),
                        stop:0.5 rgba(248, 250, 252, 0.12),
                        stop:1 rgba(241, 245, 249, 0.08));
                    border: 1px solid rgba(255, 255, 255, 0.18);
                    border-radius: 8px;
                    padding: 10px 15px;
                    text-shadow: 1px 1px 1px rgba(0, 0, 0, 0.3);
                }}
                QLabel:hover {{
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 rgba(59, 130, 246, 0.15),
                        stop:0.5 rgba(139, 92, 246, 0.12),
                        stop:1 rgba(34, 197, 94, 0.10));
                    border: 1px solid rgba(255, 255, 255, 0.25);
                }}
            """)
            value_widget.setWordWrap(True)

            item_layout.addWidget(label_widget)
            item_layout.addWidget(value_widget, 1)

            section_layout.addWidget(item_widget)

        layout.addWidget(section_frame)

    def get_value_color(self, label, value):
        """تحديد لون القيمة حسب المحتوى - ألوان أوضح وأكثر تباينًا"""
        try:
            # ألوان للرصيد والمبالغ المالية - أوضح وأكثر تباينًا
            if "رصيد" in label or "جنيه" in value:
                if "0" in value or "0.0" in value:
                    return "#E2E8F0"  # رمادي فاتح جداً للصفر
                elif any(pos in value for pos in ["دائن", "💚", "🟢"]):
                    return "#00FF7F"  # أخضر نيون ساطع للموجب
                elif any(neg in value for neg in ["مدين", "🔴"]):
                    return "#FF6B6B"  # أحمر نيون ساطع للسالب
                else:
                    return "#FFFFFF"  # أبيض نقي افتراضي

            # ألوان للحالات - أوضح وأكثر تباينًا
            elif "حالة" in label:
                if any(good in value for good in ["نشط", "✅", "ممتاز", "🌟", "جيد"]):
                    return "#00FF7F"  # أخضر نيون للحالات الجيدة
                elif any(warn in value for warn in ["يحتاج", "⚠️", "متوسط"]):
                    return "#FFD700"  # ذهبي ساطع للتحذيرات
                elif any(bad in value for bad in ["❌", "غير صحيح", "منخفض"]):
                    return "#FF6B6B"  # أحمر نيون للحالات السيئة
                else:
                    return "#00BFFF"  # أزرق سماوي ساطع للحالات العادية

            # ألوان للتصنيفات - أوضح وأكثر تباينًا
            elif "تصنيف" in label or "نوع" in label:
                if any(vip in value for vip in ["VIP", "👑", "مميز", "⭐"]):
                    return "#FFD700"  # ذهبي نيون للعملاء المميزين
                elif "جديد" in value or "🆕" in value:
                    return "#00FFFF"  # سماوي نيون للجدد
                else:
                    return "#DA70D6"  # بنفسجي نيون للعاديين

            # ألوان للنشاط - أوضح وأكثر تباينًا
            elif "نشاط" in label:
                if "عالي" in value or "🔥" in value:
                    return "#FF4500"  # برتقالي أحمر نيون للنشاط العالي
                elif "متوسط" in value or "📈" in value:
                    return "#FFD700"  # ذهبي نيون للمتوسط
                else:
                    return "#C0C0C0"  # فضي فاتح للمنخفض

            # ألوان للتواصل - أوضح وأكثر تباينًا
            elif any(contact in label for contact in ["هاتف", "بريد", "تواصل"]):
                if "صحيح" in value or "✅" in value:
                    return "#00FF7F"  # أخضر نيون للصحيح
                elif "غير صحيح" in value or "❌" in value:
                    return "#FF6B6B"  # أحمر نيون للخطأ
                elif "غير محدد" in value or "❓" in value:
                    return "#D1D5DB"  # رمادي فاتح لغير المحدد
                else:
                    return "#00BFFF"  # أزرق سماوي نيون للمعلومات العادية

            # ألوان للمعرف والتواريخ - أوضح ومميز
            elif "معرف" in label:
                return "#FFD700"  # ذهبي مميز للمعرف الفريد
            elif "تاريخ" in label:
                return "#F0F8FF"  # أبيض مزرق فاتح للتواريخ

            # ألوان للملاحظات والنصوص الطويلة
            elif "ملاحظات" in label or "معلومات" in label:
                return "#F5F5DC"  # بيج فاتح للنصوص الطويلة

            # لون افتراضي أوضح وأكثر إشراقاً
            else:
                return "#FFFFFF"  # أبيض نقي افتراضي

        except Exception as e:
            print(f"خطأ في تحديد لون القيمة: {e}")
            return "#FFFFFF"  # أبيض نقي افتراضي في حالة الخطأ

    def get_data_completeness(self):
        """حساب مستوى اكتمال البيانات"""
        try:
            total_fields = 5  # إجمالي الحقول المهمة
            completed_fields = 0

            if self.client.name:
                completed_fields += 1
            if self.client.email:
                completed_fields += 1
            if self.client.phone:
                completed_fields += 1
            if self.client.address:
                completed_fields += 1
            if self.client.balance is not None:
                completed_fields += 1

            percentage = (completed_fields / total_fields) * 100

            if percentage == 100:
                return f"مكتمل 100% 🌟"
            elif percentage >= 80:
                return f"جيد {percentage:.0f}% ✅"
            elif percentage >= 60:
                return f"متوسط {percentage:.0f}% ⚠️"
            else:
                return f"ناقص {percentage:.0f}% ❌"

        except Exception as e:
            print(f"خطأ في حساب اكتمال البيانات: {e}")
            return "غير محدد ❓"

    def get_balance_color(self):
        """الحصول على لون الرصيد"""
        if not self.client.balance or self.client.balance == 0:
            return "⚫"  # أسود للصفر
        elif self.client.balance > 0:
            return "🟢"  # أخضر للموجب
        else:
            return "🔴"  # أحمر للسالب

    # دوال مساعدة مبسطة لعرض المعلومات
    def get_account_status(self):
        """حالة الحساب المبسطة"""
        if not self.client.balance or self.client.balance == 0:
            return "متوازن ⚖️"
        elif self.client.balance > 0:
            return "دائن 💚"
        else:
            return "مدين 🔴"

    def get_client_type(self):
        """نوع العميل المبسط"""
        balance = abs(self.client.balance) if self.client.balance else 0
        if balance == 0:
            return "عميل جديد 🆕"
        elif balance > 50000:
            return "عميل VIP 👑"
        elif balance > 10000:
            return "عميل مميز ⭐"
        else:
            return "عميل عادي 👤"

    def get_activity_level(self):
        """مستوى النشاط المبسط"""
        balance = abs(self.client.balance) if self.client.balance else 0
        if balance > 20000:
            return "نشاط عالي 🔥"
        elif balance > 5000:
            return "نشاط متوسط 📈"
        else:
            return "نشاط منخفض 📉"

    def get_credit_rating(self):
        """تقييم الائتمان المبسط"""
        if not self.client.balance:
            return "غير مقيم ❓"
        elif self.client.balance > 0:
            return "ممتاز 🌟"
        elif self.client.balance > -5000:
            return "جيد ✅"
        else:
            return "يحتاج متابعة ⚠️"

    def get_additional_phones(self):
        """الأرقام الإضافية"""
        return "لا توجد أرقام إضافية"

    def get_email_status(self):
        """حالة البريد الإلكتروني"""
        if self.client.email:
            if "@" in self.client.email and "." in self.client.email:
                return "صحيح ✅"
            else:
                return "غير صحيح ❌"
        return "غير محدد ❓"

    def get_contact_methods(self):
        """طرق التواصل"""
        methods = []
        if self.client.phone:
            methods.append("هاتف 📱")
        if self.client.email:
            methods.append("بريد إلكتروني 📧")
        if self.client.address:
            methods.append("عنوان 📍")
        return ", ".join(methods) if methods else "غير محدد"

    def get_last_update(self):
        """آخر تحديث"""
        from datetime import datetime
        return datetime.now().strftime("%Y-%m-%d %H:%M")

    def get_membership_duration(self):
        """مدة العضوية"""
        try:
            if self.client.created_at:
                from datetime import datetime
                duration = datetime.now() - self.client.created_at
                days = duration.days
                if days < 30:
                    return f"{days} يوم"
                elif days < 365:
                    months = days // 30
                    return f"{months} شهر"
                else:
                    years = days // 365
                    return f"{years} سنة"
            return "غير محدد"
        except:
            return "غير محدد"

    def get_transactions_count(self):
        """عدد المعاملات"""
        return "غير متاح حالياً"

    def get_total_transactions(self):
        """إجمالي المعاملات"""
        return "غير متاح حالياً"

    def get_additional_info(self):
        """معلومات إضافية"""
        info_parts = []
        if self.client.created_at:
            from datetime import datetime
            days_since_creation = (datetime.now() - self.client.created_at).days
            info_parts.append(f"عضو منذ {days_since_creation} يوم")

        if self.client.balance:
            if self.client.balance > 0:
                info_parts.append("حساب دائن")
            else:
                info_parts.append("حساب مدين")

        return " | ".join(info_parts) if info_parts else "لا توجد معلومات إضافية"

    def get_account_summary(self):
        """ملخص الحساب"""
        balance = self.client.balance or 0
        status = "نشط" if balance != 0 else "خامل"
        return f"الحساب {status} برصيد {balance:.0f} جنيه"

    def create_control_buttons(self, layout):
        """إنشاء أزرار التحكم المحسنة"""
        buttons_frame = QFrame()
        buttons_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.08),
                    stop:0.5 rgba(248, 250, 252, 0.12),
                    stop:1 rgba(241, 245, 249, 0.08));
                border: 2px solid rgba(255, 255, 255, 0.15);
                border-radius: 12px;
                padding: 10px;  /* تقليل padding لصالح البيانات */
                margin: 5px 0;  /* تقليل margin لصالح البيانات */
                min-height: 65px;  /* تحديد ارتفاع أقل */
                max-height: 70px;  /* تحديد ارتفاع أقصى أقل */
            }
        """)

        buttons_layout = QHBoxLayout(buttons_frame)
        buttons_layout.setSpacing(10)  # تقليل المسافة بين الأزرار لاستغلال المساحة

        # زر الإغلاق - في المقدمة مكان التعديل
        close_btn = QPushButton("❌ إغلاق النافذة")
        close_btn.setMinimumWidth(200)  # زيادة العرض لاستغلال المساحة الفارغة
        close_btn.setMaximumHeight(45)  # تقليل الارتفاع لصالح البيانات
        self.style_advanced_button(close_btn, 'danger')
        close_btn.clicked.connect(self.close)

        # زر الطباعة - عرض أكبر لاستغلال المساحة
        print_btn = QPushButton("🖨️ طباعة التفاصيل")
        print_btn.setMinimumWidth(200)  # زيادة العرض لاستغلال المساحة الفارغة
        print_btn.setMaximumHeight(45)  # تقليل الارتفاع لصالح البيانات
        self.style_advanced_button(print_btn, 'emerald')
        print_btn.clicked.connect(self.print_info)

        # زر تصدير PDF - عرض أكبر لاستغلال المساحة
        export_pdf_btn = QPushButton("📄 تصدير PDF")
        export_pdf_btn.setMinimumWidth(200)  # زيادة العرض لاستغلال المساحة الفارغة
        export_pdf_btn.setMaximumHeight(45)  # تقليل الارتفاع لصالح البيانات
        self.style_advanced_button(export_pdf_btn, 'info')
        export_pdf_btn.clicked.connect(self.export_to_pdf)

        # زر إضافة ملاحظة - عرض أكبر لاستغلال المساحة
        note_btn = QPushButton("📝 إضافة ملاحظة")
        note_btn.setMinimumWidth(200)  # زيادة العرض لاستغلال المساحة الفارغة
        note_btn.setMaximumHeight(45)  # تقليل الارتفاع لصالح البيانات
        self.style_advanced_button(note_btn, 'orange')
        note_btn.clicked.connect(self.add_note)

        # ترتيب الأزرار مع الإغلاق في المقدمة واستغلال كامل للمساحة
        buttons_layout.addWidget(close_btn)  # زر الإغلاق في المقدمة
        buttons_layout.addWidget(print_btn)
        buttons_layout.addWidget(export_pdf_btn)
        buttons_layout.addWidget(note_btn)

        layout.addWidget(buttons_frame)



    def add_note(self):
        """فتح نافذة إضافة ملاحظة متطورة"""
        try:
            dialog = AddNoteDialog(self, self.client, self.parent_widget)
            if dialog.exec_() == QDialog.Accepted:
                # تحديث العرض في النافذة الحالية
                self.refresh_client_info()

        except Exception as e:
            from utils import show_error_message
            show_error_message("خطأ", f"فشل في فتح نافذة الملاحظات: {str(e)}")

    def refresh_client_info(self):
        """تحديث معلومات العميل في النافذة الحالية"""
        try:
            # إعادة تحميل بيانات العميل من قاعدة البيانات
            if self.parent_widget and self.parent_widget.session:
                self.parent_widget.session.refresh(self.client)

            # إعادة إنشاء المحتوى
            self.setup_ui()

        except Exception as e:
            print(f"خطأ في تحديث معلومات العميل: {e}")

    def style_advanced_button(self, button, button_type):
        """تطبيق تصميم متطور على الأزرار"""
        try:
            # استخدام دالة التصميم من الوالد إذا كانت متاحة
            if self.parent_widget and hasattr(self.parent_widget, 'style_advanced_button'):
                self.parent_widget.style_advanced_button(button, button_type)
            else:
                # تصميم بسيط كبديل
                colors = {
                    'emerald': ('#059669', '#10B981'),
                    'info': ('#2563EB', '#3B82F6'),
                    'cyan': ('#0891B2', '#06B6D4'),
                    'orange': ('#EA580C', '#F97316'),
                    'danger': ('#DC2626', '#EF4444')
                }

                color_pair = colors.get(button_type, ('#6B7280', '#9CA3AF'))

                button.setStyleSheet(f"""
                    QPushButton {{
                        background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                            stop:0 {color_pair[0]}, stop:1 {color_pair[1]});
                        color: white;
                        border: none;
                        border-radius: 8px;
                        padding: 12px 24px;
                        font-size: 14px;
                        font-weight: bold;
                        min-height: 20px;
                    }}
                    QPushButton:hover {{
                        background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                            stop:0 {color_pair[0]}, stop:1 {color_pair[0]});
                        transform: translateY(-2px);
                    }}
                    QPushButton:pressed {{
                        transform: translateY(0px);
                    }}
                """)
        except Exception as e:
            print(f"خطأ في تطبيق تصميم الزر: {str(e)}")

    def print_info(self):
        """طباعة معلومات العميل المحسنة"""
        try:
            from PyQt5.QtPrintSupport import QPrinter, QPrintDialog
            from PyQt5.QtGui import QPainter, QFont
            from datetime import datetime

            printer = QPrinter()
            dialog = QPrintDialog(printer, self)

            if dialog.exec_() == QPrintDialog.Accepted:
                painter = QPainter(printer)

                # إعداد الخطوط
                title_font = QFont("Arial", 16, QFont.Bold)
                header_font = QFont("Arial", 14, QFont.Bold)
                normal_font = QFont("Arial", 12)

                y = 100

                # العنوان الرئيسي
                painter.setFont(title_font)
                painter.drawText(100, y, f"تقرير تفصيلي للعميل: {self.client.name}")
                y += 80

                # المعلومات الأساسية
                painter.setFont(header_font)
                painter.drawText(100, y, "المعلومات الأساسية:")
                y += 40

                painter.setFont(normal_font)
                painter.drawText(120, y, f"المعرف: #{str(self.client.id).zfill(6)}")
                y += 30
                painter.drawText(120, y, f"الاسم: {self.client.name}")
                y += 30
                painter.drawText(120, y, f"البريد الإلكتروني: {self.client.email or 'غير محدد'}")
                y += 30
                painter.drawText(120, y, f"الهاتف: {self.client.phone or 'غير محدد'}")
                y += 30
                painter.drawText(120, y, f"العنوان: {self.client.address or 'غير محدد'}")
                y += 50

                # المعلومات المالية
                painter.setFont(header_font)
                painter.drawText(100, y, "المعلومات المالية:")
                y += 40

                painter.setFont(normal_font)
                balance = self.client.balance or 0
                painter.drawText(120, y, f"الرصيد الحالي: {balance:.0f} جنيه")
                y += 30
                painter.drawText(120, y, f"حالة الحساب: {self.get_account_status()}")
                y += 30
                painter.drawText(120, y, f"تصنيف العميل: {self.get_client_type()}")
                y += 50

                # معلومات التاريخ
                painter.setFont(header_font)
                painter.drawText(100, y, "معلومات التاريخ:")
                y += 40

                painter.setFont(normal_font)
                if self.client.created_at:
                    painter.drawText(120, y, f"تاريخ الإضافة: {self.client.created_at.strftime('%Y-%m-%d %H:%M')}")
                    y += 30
                painter.drawText(120, y, f"مدة العضوية: {self.get_membership_duration()}")
                y += 30
                painter.drawText(120, y, f"تاريخ الطباعة: {datetime.now().strftime('%Y-%m-%d %H:%M')}")
                y += 50

                # الملاحظات
                if self.client.notes:
                    painter.setFont(header_font)
                    painter.drawText(100, y, "الملاحظات:")
                    y += 40

                    painter.setFont(normal_font)
                    painter.drawText(120, y, self.client.notes[:100] + "..." if len(self.client.notes) > 100 else self.client.notes)

                painter.end()

        except Exception as e:
            from utils import show_error_message
            show_error_message("خطأ", f"فشل في الطباعة: {str(e)}")

    def export_to_pdf(self):
        """تصدير معلومات العميل إلى PDF"""
        try:
            from PyQt5.QtWidgets import QFileDialog
            from PyQt5.QtPrintSupport import QPrinter
            from PyQt5.QtGui import QPainter, QFont, QColor, QPen
            from PyQt5.QtCore import QRect
            from datetime import datetime

            # اختيار مكان الحفظ
            filename, _ = QFileDialog.getSaveFileName(
                self, "تصدير معلومات العميل إلى PDF",
                f"client_{self.client.name}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.pdf",
                "PDF Files (*.pdf)"
            )

            if not filename:
                return

            # إعداد الطابعة للـ PDF
            printer = QPrinter()
            printer.setOutputFormat(QPrinter.PdfFormat)
            printer.setOutputFileName(filename)
            printer.setPageSize(QPrinter.A4)

            # إنشاء الرسام
            painter = QPainter()
            painter.begin(printer)

            # إعداد الخطوط
            title_font = QFont("Arial", 18, QFont.Bold)
            subtitle_font = QFont("Arial", 14, QFont.Bold)
            content_font = QFont("Arial", 12)
            info_font = QFont("Arial", 10)

            # الحصول على أبعاد الصفحة
            page_rect = printer.pageRect()
            margin = 50
            content_rect = QRect(margin, margin,
                               page_rect.width() - 2*margin,
                               page_rect.height() - 2*margin)

            y_pos = content_rect.top()

            # رسم العنوان الرئيسي
            painter.setFont(title_font)
            painter.setPen(QColor(0, 0, 0))
            title_text = f"👤 معلومات العميل: {self.client.name}"
            painter.drawText(content_rect.left(), y_pos, title_text)
            y_pos += 70

            # رسم خط فاصل
            painter.setPen(QPen(QColor(100, 100, 100), 2))
            painter.drawLine(content_rect.left(), y_pos, content_rect.right(), y_pos)
            y_pos += 50

            # المعلومات الأساسية
            painter.setFont(subtitle_font)
            painter.setPen(QColor(0, 0, 0))
            painter.drawText(content_rect.left(), y_pos, "المعلومات الأساسية:")
            y_pos += 40

            painter.setFont(content_font)
            painter.setPen(QColor(50, 50, 50))

            basic_info = [
                f"معرف العميل: {self.client.id}",
                f"الاسم: {self.client.name}",
                f"البريد الإلكتروني: {self.client.email or 'غير محدد'}",
                f"الهاتف: {self.client.phone or 'غير محدد'}",
                f"العنوان: {self.client.address or 'غير محدد'}",
                f"الرصيد: {self.client.balance or 0} جنيه"
            ]

            for line in basic_info:
                painter.drawText(content_rect.left() + 20, y_pos, line)
                y_pos += 30

            y_pos += 20

            # الملاحظات
            if self.client.notes:
                painter.setFont(subtitle_font)
                painter.setPen(QColor(0, 0, 0))
                painter.drawText(content_rect.left(), y_pos, "الملاحظات:")
                y_pos += 40

                painter.setFont(content_font)
                painter.setPen(QColor(50, 50, 50))

                # تقسيم الملاحظات إلى أسطر
                notes_lines = self.client.notes.split('\n')
                for line in notes_lines:
                    if y_pos + 30 > content_rect.bottom():
                        printer.newPage()
                        y_pos = content_rect.top()

                    painter.drawText(content_rect.left() + 20, y_pos, line)
                    y_pos += 30

                y_pos += 20

            # معلومات التصدير
            y_pos += 30
            painter.setPen(QPen(QColor(100, 100, 100), 1))
            painter.drawLine(content_rect.left(), y_pos, content_rect.right(), y_pos)
            y_pos += 30

            painter.setFont(info_font)
            painter.setPen(QColor(100, 100, 100))
            export_info = f"تم التصدير في: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
            painter.drawText(content_rect.left(), y_pos, export_info)

            painter.end()

            from utils import show_info_message
            show_info_message("نجح", f"تم تصدير معلومات العميل إلى PDF بنجاح\n{filename}")

        except Exception as e:
            from utils import show_error_message
            show_error_message("خطأ", f"فشل في تصدير PDF: {str(e)}")

    def customize_title_bar(self):
        """تخصيص شريط العنوان مطابق لنافذة الحذف"""
        try:
            from ui.title_bar_utils import TitleBarStyler
            TitleBarStyler.center_title_text(self, "ℹ️ معلومات - نظام إدارة العملاء المتطور والشامل")
        except Exception as e:
            print(f"تحذير: فشل في تخصيص شريط العنوان: {e}")

    def apply_advanced_title_bar_styling(self):
        """تطبيق تصميم متطور على شريط العنوان"""
        try:
            from ui.title_bar_utils import TitleBarStyler
            TitleBarStyler.apply_advanced_title_bar_styling(self)
        except Exception as e:
            print(f"تحذير: فشل في تطبيق تصميم شريط العنوان: {e}")

    # ═══════════════════════════════════════════════════════════════════════════════════
    # الدوال المرجعية للتصميم - يمكن استخدامها في نوافذ أخرى
    # ═══════════════════════════════════════════════════════════════════════════════════

    @staticmethod
    def get_reference_colors():
        """الحصول على الألوان المرجعية للنظام"""
        return {
            'positive': '#00FF7F',      # أخضر نيون للإيجابي
            'negative': '#FF6B6B',      # أحمر نيون للسلبي
            'neutral': '#E2E8F0',       # رمادي فاتح للمحايد
            'warning': '#FFD700',       # ذهبي للتحذيرات
            'info': '#00BFFF',          # أزرق سماوي للمعلومات
            'vip': '#FFD700',           # ذهبي للمميزين
            'new': '#00FFFF',           # سماوي نيون للجدد
            'normal': '#DA70D6',        # بنفسجي نيون للعاديين
            'high': '#FF4500',          # برتقالي أحمر للعالي
            'medium': '#FFD700',        # ذهبي للمتوسط
            'low': '#C0C0C0',           # فضي للمنخفض
            'default': '#FFFFFF'        # أبيض نقي افتراضي
        }

    @staticmethod
    def get_reference_styling():
        """الحصول على التصميم المرجعي للنوافذ"""
        return """
            QDialog {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #0F172A, stop:0.08 #1E293B, stop:0.15 #334155,
                    stop:0.25 #475569, stop:0.35 #1E40AF, stop:0.45 #2563EB,
                    stop:0.55 #3B82F6, stop:0.65 #60A5FA, stop:0.72 #8B5CF6,
                    stop:0.8 #7C3AED, stop:0.88 #6D28D9, stop:0.95 #5B21B6,
                    stop:1 #4C1D95);
                border: 4px solid rgba(255, 255, 255, 0.25);
                border-radius: 8px;
            }
            QDialog::title {
                color: #ffffff;
                font-weight: bold;
                text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.7);
            }
        """


class AddNoteDialog(QDialog):
    """نافذة ملاحظات بسيطة جداً"""

    def __init__(self, parent=None, client=None, parent_widget=None):
        super().__init__(parent)
        self.client = client
        self.parent_widget = parent_widget
        self.setup_ui()

    def setup_ui(self):
        """إعداد نافذة بسيطة جداً"""
        self.setWindowTitle(f"📝 {self.client.name if self.client.name else 'عميل'}")
        self.setWindowFlags(Qt.Dialog | Qt.WindowTitleHint | Qt.WindowCloseButtonHint)
        self.setModal(True)
        self.resize(450, 350)

        # تخصيص شريط العنوان الخارجي ليكون أسود
        self.customize_title_bar()

        # خلفية متطابقة مع النموذج المرجعي
        self.setStyleSheet(ClientInfoDialog.get_reference_styling())

        # تخطيط بسيط
        layout = QVBoxLayout(self)
        layout.setContentsMargins(15, 15, 15, 15)
        layout.setSpacing(10)

        # شريط العنوان الداخلي مطابق للنموذج المرجعي
        title_frame = QFrame()
        title_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(34, 197, 94, 0.25),
                    stop:0.2 rgba(59, 130, 246, 0.3),
                    stop:0.5 rgba(139, 92, 246, 0.28),
                    stop:0.8 rgba(168, 85, 247, 0.25),
                    stop:1 rgba(236, 72, 153, 0.2));
                border: 3px solid rgba(255, 255, 255, 0.3);
                border-radius: 8px;
                margin: 1px 0px 5px 0px;
                padding: 2px;
                max-height: 50px;
                min-height: 45px;
            }
        """)

        title_layout = QHBoxLayout(title_frame)
        title_layout.setContentsMargins(8, 5, 8, 11)  # رفع النص 3 درجات (8-3=5 للأعلى، 8+3=11 للأسفل)
        title_layout.setSpacing(10)

        # نص العنوان في المنتصف - مكبر ومرفوع
        title_text = QLabel(f"📝 ملاحظة للعميل: {self.client.name}")
        title_text.setAlignment(Qt.AlignCenter)
        title_text.setStyleSheet("""
            QLabel {
                color: #FFFFFF;
                font-size: 20px;
                font-weight: bold;
                text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
                background: transparent;
                border: none;
                padding: 4px 15px;
                margin: 0px;
            }
        """)

        # وضع النص في المنتصف تماماً
        title_layout.addStretch()
        title_layout.addWidget(title_text)
        title_layout.addStretch()
        layout.addWidget(title_frame)

        # محرر النصوص المتطور مطابق للنموذج المرجعي
        self.text_editor = QTextEdit()
        self.text_editor.setStyleSheet("""
            QTextEdit {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.15),
                    stop:1 rgba(248, 250, 252, 0.1));
                color: #FFFFFF;
                font-size: 14px;
                font-family: 'Segoe UI', Arial, sans-serif;
                border: 2px solid rgba(255, 255, 255, 0.3);
                border-radius: 8px;
                padding: 10px;
                selection-background-color: rgba(59, 130, 246, 0.5);
                line-height: 1.4;
            }
            QTextEdit:focus {
                border: 2px solid rgba(255, 215, 0, 0.6);
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.2),
                    stop:1 rgba(248, 250, 252, 0.15));
            }
        """)
        self.text_editor.setPlaceholderText("اكتب ملاحظاتك هنا...\n\nيمكنك كتابة معلومات مفصلة عن العميل، تذكيرات، أو أي ملاحظات مهمة.")
        self.text_editor.setMinimumHeight(180)
        layout.addWidget(self.text_editor)

        # أزرار بسيطة
        self.create_buttons(layout)

        # تحميل النص
        self.load_note()

    def create_buttons(self, layout):
        """أزرار متطورة مطابقة للنموذج المرجعي"""
        # إطار الأزرار مطابق للنموذج المرجعي
        buttons_frame = QFrame()
        buttons_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.08),
                    stop:1 rgba(241, 245, 249, 0.08));
                border: 2px solid rgba(255, 255, 255, 0.2);
                border-radius: 8px;
                padding: 15px;
                margin: 5px 0px;
                max-height: 80px;
            }
        """)

        buttons_layout = QHBoxLayout(buttons_frame)
        buttons_layout.setContentsMargins(10, 10, 10, 10)
        buttons_layout.setSpacing(20)

        # زر الإلغاء أولاً
        cancel_btn = QPushButton("❌ إلغاء")
        cancel_btn.setMinimumWidth(140)
        cancel_btn.setMinimumHeight(45)
        self.apply_reference_button_style(cancel_btn, 'danger')
        cancel_btn.clicked.connect(self.reject)

        # زر الحفظ ثانياً
        save_btn = QPushButton("💾 حفظ")
        save_btn.setMinimumWidth(140)
        save_btn.setMinimumHeight(45)
        self.apply_reference_button_style(save_btn, 'success')
        save_btn.clicked.connect(self.save_note)

        # وضع الأزرار في المنتصف
        buttons_layout.addStretch()
        buttons_layout.addWidget(cancel_btn)
        buttons_layout.addWidget(save_btn)
        buttons_layout.addStretch()

        layout.addWidget(buttons_frame)

    def apply_reference_button_style(self, button, button_type):
        """تطبيق تصميم الأزرار المرجعي المتطور"""
        # استخدام نفس التصميم المتطور من النموذج المرجعي
        if self.parent_widget and hasattr(self.parent_widget, 'style_advanced_button'):
            self.parent_widget.style_advanced_button(button, button_type)
        else:
            # تصميم متطور مطابق للنموذج المرجعي
            colors = {
                'success': {
                    'bg_start': '#064e3b', 'bg_mid': '#047857', 'bg_end': '#065f46', 'bg_bottom': '#10b981',
                    'hover_start': '#10b981', 'hover_mid': '#34d399', 'hover_end': '#6ee7b7', 'hover_bottom': '#a7f3d0',
                    'border': '#10b981', 'shadow': 'rgba(16, 185, 129, 0.6)'
                },
                'danger': {
                    'bg_start': '#7f1d1d', 'bg_mid': '#991b1b', 'bg_end': '#b91c1c', 'bg_bottom': '#dc2626',
                    'hover_start': '#dc2626', 'hover_mid': '#ef4444', 'hover_end': '#f87171', 'hover_bottom': '#fca5a5',
                    'border': '#dc2626', 'shadow': 'rgba(220, 38, 38, 0.6)'
                },
                'info': {
                    'bg_start': '#0c4a6e', 'bg_mid': '#075985', 'bg_end': '#0369a1', 'bg_bottom': '#0284c7',
                    'hover_start': '#0284c7', 'hover_mid': '#0ea5e9', 'hover_end': '#38bdf8', 'hover_bottom': '#7dd3fc',
                    'border': '#0ea5e9', 'shadow': 'rgba(14, 165, 233, 0.6)'
                }
            }

            color_set = colors.get(button_type, colors['success'])

            button.setStyleSheet(f"""
                QPushButton {{
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 {color_set['bg_start']}, stop:0.3 {color_set['bg_mid']},
                        stop:0.7 {color_set['bg_end']}, stop:1 {color_set['bg_bottom']});
                    color: #ffffff;
                    border: 2px solid {color_set['border']};
                    border-radius: 8px;
                    padding: 12px 24px;
                    font-size: 14px;
                    font-weight: bold;
                    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.7);
                    box-shadow: 0 4px 8px {color_set['shadow']};
                    min-width: 120px;
                    min-height: 40px;
                }}
                QPushButton:hover {{
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 {color_set['hover_start']}, stop:0.3 {color_set['hover_mid']},
                        stop:0.7 {color_set['hover_end']}, stop:1 {color_set['hover_bottom']});
                    border: 2px solid rgba(255, 255, 255, 0.4);
                    transform: translateY(-2px);
                    box-shadow: 0 6px 12px {color_set['shadow']};
                }}
                QPushButton:pressed {{
                    background: {color_set['bg_start']};
                    transform: translateY(0px);
                    box-shadow: 0 2px 4px {color_set['shadow']};
                }}
            """)

    def load_note(self):
        """تحميل الملاحظة"""
        if self.client and self.client.notes:
            self.text_editor.setPlainText(self.client.notes)

    def save_note(self):
        """حفظ الملاحظة"""
        try:
            note = self.text_editor.toPlainText().strip()
            self.client.notes = note if note else None

            if self.parent_widget and self.parent_widget.session:
                self.parent_widget.session.commit()
                if hasattr(self.parent_widget, 'refresh_data'):
                    self.parent_widget.refresh_data()

            self.accept()

            from utils import show_info_message
            show_info_message("تم", "حُفظت الملاحظة")

        except Exception as e:
            from utils import show_error_message
            show_error_message("خطأ", f"فشل الحفظ: {str(e)}")


    def customize_title_bar(self):
        """تخصيص شريط العنوان الخارجي ليكون أسود"""
        try:
            from ui.title_bar_utils import TitleBarStyler
            # استخدام التصميم المتطور للشريط
            TitleBarStyler.apply_advanced_title_bar_styling(self)
        except Exception as e:
            # تطبيق تصميم أسود بسيط للشريط
            pass








class DeleteClientDialog(QDialog):
    """نافذة حذف العميل مشابهة لنافذة الإضافة"""

    def __init__(self, parent=None, client=None):
        super().__init__(parent)
        self.client = client
        self.parent_widget = parent
        self.setup_ui()

    def setup_ui(self):
        """إعداد واجهة النافذة مطابقة لنافذة الإضافة"""
        # عنوان النافذة
        self.setWindowTitle("🗑️ حذف - نظام إدارة العملاء المتطور والشامل")

        # إزالة علامة الاستفهام وتحسين شريط العنوان
        self.setWindowFlags(Qt.Dialog | Qt.WindowTitleHint | Qt.WindowCloseButtonHint)

        # تخصيص شريط العنوان مطابق لنافذة الإضافة
        self.customize_title_bar()

        self.setModal(True)
        self.resize(400, 250)  # ثلث العرض ونصف الارتفاع

        # خلفية النافذة مطابقة لنافذة الإضافة
        self.setStyleSheet("""
            QDialog {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #334155,
                    stop:0.3 #475569, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                    stop:0.6 #60A5FA, stop:0.7 #8B5CF6, stop:0.8 #7C3AED,
                    stop:0.9 #6D28D9, stop:1 #5B21B6);
                border: none;
                border-radius: 15px;
            }
        """)

        # التخطيط الرئيسي مضغوط
        layout = QVBoxLayout(self)
        layout.setContentsMargins(15, 15, 15, 15)  # هوامش أصغر
        layout.setSpacing(8)  # مسافات أقل

        # عنوان النافذة الداخلي مضغوط
        title_label = QLabel("🧑‍💼 حذف العميل")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                font-size: 20px;
                font-weight: bold;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(239, 68, 68, 0.2),
                    stop:0.5 rgba(220, 38, 38, 0.3),
                    stop:1 rgba(185, 28, 28, 0.2));
                border: 2px solid rgba(239, 68, 68, 0.5);
                border-radius: 10px;
                padding: 8px;
                margin: 5px 0;
                text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
            }
        """)
        layout.addWidget(title_label)

        # معلومات العميل مضغوطة
        if self.client:
            # عرض المعلومات في سطر واحد مضغوط
            info_text = f"🧑‍💼 {self.client.name} | 📱 {self.client.phone or 'غير محدد'} | 💰 {self.client.balance or 0:.0f} جنيه"

            info_label = QLabel(info_text)
            info_label.setAlignment(Qt.AlignCenter)
            info_label.setStyleSheet("""
                QLabel {
                    color: #ffffff;
                    font-size: 14px;
                    font-weight: bold;
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 rgba(255, 255, 255, 0.1),
                        stop:0.5 rgba(248, 250, 252, 0.15),
                        stop:1 rgba(241, 245, 249, 0.1));
                    border: 1px solid rgba(255, 255, 255, 0.3);
                    border-radius: 8px;
                    padding: 8px;
                    margin: 5px 0;
                    text-shadow: 1px 1px 1px rgba(0, 0, 0, 0.5);
                }
            """)
            layout.addWidget(info_label)

        # سؤال التأكيد مضغوط
        question_label = QLabel("⚠️ هل أنت متأكد من حذف هذا العميل؟")
        question_label.setAlignment(Qt.AlignCenter)
        question_label.setStyleSheet("""
            QLabel {
                color: #fbbf24;
                font-size: 16px;
                font-weight: bold;
                margin: 8px 0;
                text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
            }
        """)
        layout.addWidget(question_label)

        # الأزرار مضغوطة
        buttons_layout = QHBoxLayout()
        buttons_layout.setSpacing(15)

        # زر الإلغاء
        cancel_button = QPushButton("❌ إلغاء")
        self.style_advanced_button(cancel_button, 'info')
        cancel_button.clicked.connect(self.reject)

        # زر التأكيد
        confirm_button = QPushButton("🗑️ تأكيد الحذف")
        self.style_advanced_button(confirm_button, 'danger')
        confirm_button.clicked.connect(self.accept)

        buttons_layout.addWidget(cancel_button)
        buttons_layout.addWidget(confirm_button)

        layout.addLayout(buttons_layout)

    def style_advanced_button(self, button, button_type):
        """تطبيق تصميم متطور على الأزرار مطابق لنافذة الإضافة"""
        try:
            # استخدام دالة التصميم من الوالد إذا كانت متاحة
            if self.parent_widget and hasattr(self.parent_widget, 'style_advanced_button'):
                self.parent_widget.style_advanced_button(button, button_type)
            else:
                # تصميم بسيط كبديل
                colors = {
                    'danger': '#ef4444',
                    'info': '#3b82f6'
                }
                color = colors.get(button_type, '#6b7280')
                button.setStyleSheet(f"""
                    QPushButton {{
                        background-color: {color};
                        color: white;
                        border: none;
                        border-radius: 8px;
                        padding: 12px 30px;
                        font-size: 16px;
                        font-weight: bold;
                        min-width: 120px;
                    }}
                    QPushButton:hover {{
                        background-color: {color}dd;
                        transform: translateY(-2px);
                    }}
                    QPushButton:pressed {{
                        background-color: {color}bb;
                        transform: translateY(0px);
                    }}
                """)
        except Exception as e:
            print(f"خطأ في تصميم الزر: {e}")

    def customize_title_bar(self):
        """تخصيص شريط العنوان مطابق لنافذة الإضافة"""
        try:
            # إنشاء أيقونة مخصصة للحذف
            pixmap = QPixmap(48, 48)
            pixmap.fill(Qt.transparent)

            painter = QPainter(pixmap)
            painter.setRenderHint(QPainter.Antialiasing)
            painter.setRenderHint(QPainter.SmoothPixmapTransform)

            # تدرج أحمر للحذف مطابق لنمط الموردين والعمال
            gradient = QRadialGradient(24, 24, 20)
            gradient.setColorAt(0, QColor(239, 68, 68))    # أحمر فاتح
            gradient.setColorAt(0.7, QColor(220, 38, 38))  # أحمر متوسط
            gradient.setColorAt(1, QColor(185, 28, 28))    # أحمر داكن

            # رسم دائرة متدرجة مطابقة للموردين والعمال
            painter.setBrush(QBrush(gradient))
            painter.setPen(QPen(QColor(255, 255, 255, 180), 2))
            painter.drawEllipse(4, 4, 40, 40)

            # رسم رمز الحذف مطابق للموردين والعمال
            painter.setPen(QPen(QColor(255, 255, 255), 3))
            painter.setFont(QFont("Arial", 16, QFont.Bold))
            painter.drawText(12, 30, "🗑️")

            painter.end()

            # تعيين الأيقونة
            icon = QIcon(pixmap)
            self.setWindowIcon(icon)

            # تطبيق تصميم متطور على شريط العنوان مطابق للموردين والعمال
            self.apply_advanced_title_bar_styling()

            # توسيط النص في شريط العنوان
            self.center_title_text()

        except Exception as e:
            print(f"خطأ في تخصيص شريط العنوان: {e}")

    def apply_advanced_title_bar_styling(self):
        """تطبيق تصميم متطور على شريط العنوان"""
        TitleBarStyler.apply_advanced_title_bar_styling(self)

    def center_title_text(self):
        """تحسين وضع النص في منتصف شريط العنوان"""
        original_title = "🗑️ حذف - نظام إدارة العملاء المتطور والشامل"
        TitleBarStyler.center_title_text(self, original_title)


class EditClientAmountDialog(QDialog):
    """نافذة بسيطة لتعديل مبلغ العميل - إضافة أو تقليل فقط"""

    def __init__(self, parent=None, client=None, session=None):
        super().__init__(parent)
        self.client = client
        self.session = session
        self.parent_widget = parent
        self.setup_ui()

    def setup_ui(self):
        """إعداد واجهة النافذة البسيطة"""
        # إعداد النافذة الأساسي
        self.setWindowTitle(f"💰 تعديل المبلغ - نظام إدارة العملاء المتطور والشامل")
        self.setWindowFlags(Qt.Dialog | Qt.WindowTitleHint | Qt.WindowCloseButtonHint)
        self.setModal(True)
        self.resize(450, 300)

        # تخصيص شريط العنوان ليتطابق مع باقي النوافذ
        self.customize_title_bar()

        # تطبيق نمط النافذة
        self.setStyleSheet("""
            QDialog {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #334155,
                    stop:0.3 #475569, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                    stop:0.6 #60A5FA, stop:0.7 #8B5CF6, stop:0.8 #7C3AED,
                    stop:0.9 #6D28D9, stop:1 #5B21B6);
                border: none;
                border-radius: 15px;
            }
        """)

        # التخطيط الرئيسي
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)

        # عنوان النافذة
        title_label = QLabel(f"💰 تعديل رصيد العميل")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                font-size: 18px;
                font-weight: bold;
                padding: 12px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 rgba(59, 130, 246, 0.6), stop:1 rgba(139, 92, 246, 0.6));
                border-radius: 12px;
                text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.7);
                margin-bottom: 10px;
            }
        """)
        layout.addWidget(title_label)

        # معلومات العميل
        info_frame = QFrame()
        info_frame.setStyleSheet("""
            QFrame {
                background: rgba(255, 255, 255, 0.1);
                border: 2px solid rgba(255, 255, 255, 0.2);
                border-radius: 10px;
                padding: 10px;
            }
        """)
        info_layout = QVBoxLayout(info_frame)

        # اسم العميل
        name_label = QLabel(f"🤝 العميل: {self.client.name}")
        name_label.setStyleSheet("color: #ffffff; font-size: 14px; font-weight: bold;")
        info_layout.addWidget(name_label)

        # الرصيد الحالي
        current_balance = self.client.balance or 0
        from utils import format_currency
        balance_label = QLabel(f"💳 الرصيد الحالي: {format_currency(current_balance)}")
        if current_balance > 0:
            balance_color = "#f87171"  # أحمر للدين
        elif current_balance < 0:
            balance_color = "#34d399"  # أخضر للرصيد الموجب
        else:
            balance_color = "#d1d5db"  # رمادي للصفر
        balance_label.setStyleSheet(f"color: {balance_color}; font-size: 14px; font-weight: bold;")
        info_layout.addWidget(balance_label)

        layout.addWidget(info_frame)

        # حقل تعديل المبلغ
        amount_frame = QFrame()
        amount_frame.setStyleSheet("""
            QFrame {
                background: rgba(255, 255, 255, 0.1);
                border: 2px solid rgba(255, 255, 255, 0.2);
                border-radius: 10px;
                padding: 15px;
            }
        """)
        amount_layout = QFormLayout(amount_frame)

        # تسمية المبلغ
        amount_label = QLabel("المبلغ المراد إضافته أو تقليله:")
        amount_label.setStyleSheet("color: #ffffff; font-weight: bold; font-size: 13px;")

        # حقل المبلغ
        self.amount_edit = QDoubleSpinBox()
        self.amount_edit.setRange(-999999, 999999)
        self.amount_edit.setDecimals(0)
        self.amount_edit.setSingleStep(100)
        self.amount_edit.setValue(0)
        self.amount_edit.setPrefix("💰 ")
        self.amount_edit.setSuffix(" جنيه")
        self.amount_edit.setStyleSheet("""
            QDoubleSpinBox {
                background: rgba(255, 255, 255, 0.9);
                border: 2px solid rgba(255, 255, 255, 0.3);
                border-radius: 8px;
                padding: 8px;
                font-size: 14px;
                font-weight: bold;
                color: #1e293b;
            }
            QDoubleSpinBox:focus {
                border: 3px solid rgba(59, 130, 246, 0.8);
                background: rgba(219, 234, 254, 0.9);
            }
        """)

        amount_layout.addRow(amount_label, self.amount_edit)

        # ملاحظة توضيحية
        note_label = QLabel("💡 القيم الموجبة تزيد الرصيد، والقيم السالبة تقلل الرصيد")
        note_label.setStyleSheet("color: #fbbf24; font-size: 12px; font-style: italic;")
        amount_layout.addRow(note_label)

        layout.addWidget(amount_frame)

        # أزرار التحكم
        buttons_layout = QHBoxLayout()
        buttons_layout.setSpacing(10)

        # زر الإلغاء - استخدام نفس تصميم الشريط الرئيسي
        cancel_button = QPushButton("❌ إلغاء")
        cancel_button.clicked.connect(self.reject)
        cancel_button.setMinimumHeight(45)
        self.style_advanced_button(cancel_button, 'danger')

        # زر الحفظ - استخدام نفس تصميم الشريط الرئيسي
        save_button = QPushButton("💾 حفظ التعديل")
        save_button.clicked.connect(self.save_changes)
        save_button.setMinimumHeight(45)
        self.style_advanced_button(save_button, 'emerald')

        buttons_layout.addWidget(cancel_button)
        buttons_layout.addWidget(save_button)

        layout.addLayout(buttons_layout)

    def save_changes(self):
        """حفظ التعديل على رصيد العميل"""
        try:
            amount = self.amount_edit.value()

            # التحقق من صحة المبلغ
            if amount == 0:
                from utils import show_error_message
                show_error_message("تحذير", "يجب إدخال مبلغ غير صفر")
                return

            # حفظ الرصيد القديم للمقارنة
            old_balance = self.client.balance or 0

            # تحديث رصيد العميل
            self.client.balance = old_balance + amount
            new_balance = self.client.balance

            # حفظ التغييرات في قاعدة البيانات
            self.session.commit()

            # إظهار رسالة نجاح
            if amount > 0:
                operation = "إضافة"
                icon = "➕"
            else:
                operation = "تقليل"
                icon = "➖"

            from utils import show_info_message, format_currency
            show_info_message(
                "تم بنجاح",
                f"{icon} تم {operation} {format_currency(abs(amount))} {operation} رصيد العميل {self.client.name}\n\n"
                f"📊 الرصيد السابق: {format_currency(old_balance)}\n"
                f"📊 الرصيد الجديد: {format_currency(new_balance)}"
            )

            # إغلاق النافذة
            self.accept()

        except Exception as e:
            # التراجع عن التغييرات في حالة الخطأ
            self.session.rollback()
            from utils import show_error_message
            show_error_message("خطأ", f"حدث خطأ أثناء حفظ التعديل:\n{str(e)}")

    def style_advanced_button(self, button, button_type, has_menu=False):
        """تطبيق تصميم متطور للأزرار - مطابق للشريط الرئيسي"""
        try:
            # استخدام دالة التصميم من الوالد إذا كانت متاحة
            if self.parent_widget and hasattr(self.parent_widget, 'style_advanced_button'):
                self.parent_widget.style_advanced_button(button, button_type, has_menu)
            else:
                # تصميم متطور مطابق للشريط الرئيسي
                color_schemes = {
                    'emerald': {
                        'base': '#10B981',
                        'hover': '#059669',
                        'pressed': '#047857',
                        'shadow': 'rgba(16, 185, 129, 0.4)'
                    },
                    'danger': {
                        'base': '#EF4444',
                        'hover': '#DC2626',
                        'pressed': '#B91C1C',
                        'shadow': 'rgba(239, 68, 68, 0.4)'
                    },
                    'info': {
                        'base': '#3B82F6',
                        'hover': '#2563EB',
                        'pressed': '#1D4ED8',
                        'shadow': 'rgba(59, 130, 246, 0.4)'
                    },
                    'orange': {
                        'base': '#F97316',
                        'hover': '#EA580C',
                        'pressed': '#C2410C',
                        'shadow': 'rgba(249, 115, 22, 0.4)'
                    }
                }

                colors = color_schemes.get(button_type, color_schemes['info'])

                # تطبيق التصميم المتطور
                button.setStyleSheet(f"""
                    QPushButton {{
                        background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                            stop:0 {colors['base']}, stop:1 {colors['hover']});
                        color: #ffffff;
                        border: 2px solid rgba(255, 255, 255, 0.2);
                        border-radius: 12px;
                        padding: 12px 24px;
                        font-size: 14px;
                        font-weight: bold;
                        text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
                        min-width: 120px;
                    }}
                    QPushButton:hover {{
                        background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                            stop:0 {colors['hover']}, stop:1 {colors['base']});
                        border: 3px solid rgba(255, 255, 255, 0.4);
                        transform: translateY(-2px);
                        box-shadow: 0 8px 25px {colors['shadow']};
                    }}
                    QPushButton:pressed {{
                        background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                            stop:0 {colors['pressed']}, stop:1 {colors['hover']});
                        transform: translateY(0px);
                        box-shadow: 0 4px 15px {colors['shadow']};
                    }}
                    QPushButton:disabled {{
                        background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                            stop:0 #6B7280, stop:1 #4B5563);
                        color: #9CA3AF;
                        border: 2px solid rgba(255, 255, 255, 0.1);
                    }}
                """)

        except Exception as e:
            print(f"خطأ في تصميم الزر: {e}")

    def customize_title_bar(self):
        """تخصيص شريط العنوان الخارجي ليتطابق مع باقي النوافذ"""
        try:
            from ui.title_bar_utils import TitleBarStyler
            TitleBarStyler.apply_advanced_title_bar_styling(self)
        except Exception as e:
            # تطبيق تصميم بسيط للشريط في حالة الفشل
            print(f"تحذير: فشل في تطبيق تصميم شريط العنوان: {e}")




class ClientStatisticsDialog(QDialog):
    """نافذة إحصائيات العملاء المتطورة"""

    def __init__(self, parent=None, session=None):
        super().__init__(parent)
        self.parent_widget = parent
        self.session = session
        self.setup_ui()

    def setup_ui(self):
        """إعداد واجهة النافذة المتطورة"""
        # إعداد النافذة الأساسي
        self.setWindowTitle("📊 إحصائيات العملاء - نظام إدارة العملاء المتطور والشامل")
        self.setWindowFlags(Qt.Dialog | Qt.WindowTitleHint | Qt.WindowCloseButtonHint)
        self.setModal(True)
        self.resize(600, 420)  # تقليل الارتفاع من 500 إلى 420

        # تخصيص شريط العنوان
        self.customize_title_bar()

        # تطبيق نمط النافذة
        self.setStyleSheet("""
            QDialog {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #334155,
                    stop:0.3 #475569, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                    stop:0.6 #60A5FA, stop:0.7 #8B5CF6, stop:0.8 #7C3AED,
                    stop:0.9 #6D28D9, stop:1 #5B21B6);
                border: none;
                border-radius: 15px;
            }
        """)

        # التخطيط الرئيسي المضغوط
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 15, 20, 15)  # تقليل الهوامش
        layout.setSpacing(12)  # تقليل المسافات من 20 إلى 12

        # العنوان الرئيسي المطور بدون إطار
        title_container = QWidget()
        title_container.setStyleSheet("""
            QWidget {
                background: transparent;
                padding: 10px;
            }
        """)

        title_inner_layout = QVBoxLayout(title_container)
        title_inner_layout.setContentsMargins(0, 0, 0, 0)
        title_inner_layout.setSpacing(8)

        # الأيقونة والعنوان الرئيسي
        main_title = QLabel("📊 إحصائيات العملاء")
        main_title.setAlignment(Qt.AlignCenter)
        main_title.setStyleSheet("""
            QLabel {
                color: #ffffff;
                font-size: 30px;
                font-weight: bold;
                background: transparent;
                text-shadow: 4px 4px 8px rgba(0, 0, 0, 0.9);
                padding: 10px;
            }
        """)

        # العنوان الفرعي التوضيحي
        subtitle = QLabel("تقرير شامل عن حالة العملاء والأرصدة المالية")
        subtitle.setAlignment(Qt.AlignCenter)
        subtitle.setStyleSheet("""
            QLabel {
                color: rgba(255, 255, 255, 0.8);
                font-size: 14px;
                font-weight: normal;
                background: transparent;
                text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
                padding: 5px;
            }
        """)

        title_inner_layout.addWidget(main_title)
        title_inner_layout.addWidget(subtitle)
        layout.addWidget(title_container)

        # حساب الإحصائيات
        self.calculate_statistics()

        # إنشاء قائمة الإحصائيات المضغوطة
        stats_layout = QVBoxLayout()
        stats_layout.setSpacing(8)  # تقليل المسافة أكثر من 15 إلى 8
        stats_layout.setContentsMargins(20, 10, 20, 10)  # تقليل الهوامش أكثر

        # إنشاء قائمة الإحصائيات
        from utils import format_currency

        stats_items = [
            ("👤", "إجمالي العملاء المسجلين في النظام", str(self.total_clients), "#3B82F6", "📊"),
            ("✅", "العملاء النشطين (لديهم أرصدة مستحقة)", str(self.active_clients), "#10B981", "💚"),
            ("⚪", "العملاء العاديين (رصيد متوازن صفر)", str(self.normal_clients), "#F59E0B", "🟡"),
            ("❌", "العملاء المدينين (عليهم مبالغ مستحقة)", str(self.debtor_clients), "#EF4444", "🔴"),
            ("💵", "إجمالي الأرصدة المالية الحالية", format_currency(self.total_balance), "#10B981", "💰")
        ]

        for icon, title, value, color, secondary_icon in stats_items:
            # إنشاء عنصر مضغوط بدون إطارات
            item_widget = QWidget()
            item_widget.setStyleSheet("""
                QWidget {
                    background: transparent;
                    padding: 5px;  /* تقليل من 8px إلى 5px */
                    margin: 1px;   /* تقليل من 2px إلى 1px */
                }
                QWidget:hover {
                    background: rgba(255, 255, 255, 0.05);
                    border-radius: 6px;
                }
            """)

            item_layout = QHBoxLayout(item_widget)
            item_layout.setSpacing(12)  # تقليل من 15 إلى 12
            item_layout.setContentsMargins(12, 5, 12, 5)  # تقليل الهوامش

            # الأيقونة بدون إطارات
            icon_label = QLabel(icon)
            icon_label.setFixedSize(32, 32)  # حجم مناسب لإظهار الأيقونات كاملة
            icon_label.setAlignment(Qt.AlignCenter)
            icon_label.setStyleSheet(f"""
                QLabel {{
                    color: {color};
                    font-size: 18px;  /* تصغير أكثر */
                    font-weight: bold;
                    text-shadow: 3px 3px 6px rgba(0, 0, 0, 0.9);
                    background: transparent;
                    border: none;
                    padding: 5px;
                }}
            """)
            item_layout.addWidget(icon_label)

            # العنوان المطور مع وصف مفصل
            title_label = QLabel(title)
            title_label.setStyleSheet("""
                QLabel {
                    color: #ffffff;
                    font-size: 16px;
                    font-weight: bold;
                    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.9);
                    padding: 4px 8px;
                }
            """)
            item_layout.addWidget(title_label)

            # مساحة فارغة للدفع
            item_layout.addStretch()

            # القيمة بدون إطارات
            value_label = QLabel(value)
            value_label.setStyleSheet(f"""
                QLabel {{
                    color: {color};
                    font-size: 18px;
                    font-weight: bold;
                    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.9);
                    background: transparent;
                    border: none;
                    padding: 6px 12px;
                    min-width: 70px;
                }}
            """)
            value_label.setAlignment(Qt.AlignCenter)
            item_layout.addWidget(value_label)

            stats_layout.addWidget(item_widget)

        layout.addLayout(stats_layout)

        # أزرار التحكم
        buttons_layout = QHBoxLayout()
        buttons_layout.setSpacing(15)

        # زر تصدير PDF
        export_pdf_button = QPushButton("📄 تصدير PDF")
        export_pdf_button.clicked.connect(self.export_statistics_to_pdf)
        export_pdf_button.setMinimumHeight(45)
        self.style_advanced_button(export_pdf_button, 'info')

        # زر الإغلاق
        close_button = QPushButton("❌ إغلاق")
        close_button.clicked.connect(self.accept)
        close_button.setMinimumHeight(45)
        self.style_advanced_button(close_button, 'danger')

        buttons_layout.addWidget(close_button)
        buttons_layout.addWidget(export_pdf_button)

        layout.addLayout(buttons_layout)

    def calculate_statistics(self):
        """حساب الإحصائيات"""
        try:
            self.total_clients = self.session.query(Client).count()
            self.active_clients = self.session.query(Client).filter(Client.balance > 0).count()
            self.normal_clients = self.session.query(Client).filter(Client.balance == 0).count()
            self.debtor_clients = self.session.query(Client).filter(Client.balance < 0).count()
            self.total_balance = sum([c.balance or 0 for c in self.session.query(Client).all()])
        except Exception as e:
            print(f"خطأ في حساب الإحصائيات: {e}")
            self.total_clients = 0
            self.active_clients = 0
            self.normal_clients = 0
            self.debtor_clients = 0
            self.total_balance = 0



    def export_statistics_to_pdf(self):
        """تصدير إحصائيات العملاء إلى ملف PDF"""
        try:
            from utils import show_info_message
            show_info_message("معلومات", "سيتم تطوير ميزة تصدير PDF قريباً...")
        except Exception as e:
            from utils import show_error_message
            show_error_message("خطأ", f"حدث خطأ: {str(e)}")

    def style_advanced_button(self, button, button_type, has_menu=False):
        """تطبيق تصميم متطور للأزرار"""
        try:
            if self.parent_widget and hasattr(self.parent_widget, 'style_advanced_button'):
                self.parent_widget.style_advanced_button(button, button_type, has_menu)
            else:
                # تصميم متطور مطابق للشريط الرئيسي
                color_schemes = {
                    'info': {
                        'base': '#3B82F6',
                        'hover': '#2563EB',
                        'pressed': '#1D4ED8',
                        'shadow': 'rgba(59, 130, 246, 0.4)'
                    },
                    'danger': {
                        'base': '#EF4444',
                        'hover': '#DC2626',
                        'pressed': '#B91C1C',
                        'shadow': 'rgba(239, 68, 68, 0.4)'
                    }
                }

                colors = color_schemes.get(button_type, color_schemes['info'])

                button.setStyleSheet(f"""
                    QPushButton {{
                        background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                            stop:0 {colors['base']}, stop:1 {colors['hover']});
                        color: #ffffff;
                        border: 2px solid rgba(255, 255, 255, 0.2);
                        border-radius: 12px;
                        padding: 12px 24px;
                        font-size: 14px;
                        font-weight: bold;
                        text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
                        min-width: 120px;
                    }}
                    QPushButton:hover {{
                        background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                            stop:0 {colors['hover']}, stop:1 {colors['base']});
                        border: 3px solid rgba(255, 255, 255, 0.4);
                        transform: translateY(-2px);
                        box-shadow: 0 8px 25px {colors['shadow']};
                    }}
                    QPushButton:pressed {{
                        background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                            stop:0 {colors['pressed']}, stop:1 {colors['hover']});
                        transform: translateY(0px);
                        box-shadow: 0 4px 15px {colors['shadow']};
                    }}
                """)

        except Exception as e:
            print(f"خطأ في تصميم الزر: {e}")

    def customize_title_bar(self):
        """تخصيص شريط العنوان"""
        try:
            from ui.title_bar_utils import TitleBarStyler
            TitleBarStyler.apply_advanced_title_bar_styling(self)
        except Exception as e:
            print(f"تحذير: فشل في تطبيق تصميم شريط العنوان: {e}")




class ClientDocumentsDialog(QDialog):
    """نافذة إدارة وثائق العميل - مطابقة للنمط الموحد"""

    def __init__(self, parent=None, client=None, session=None):
        super().__init__(parent)
        self.client = client
        self.session = session
        self.parent_widget = parent
        self.documents = []  # قائمة الوثائق المحلية
        self.setup_ui()

    def setup_ui(self):
        """إعداد واجهة النافذة"""
        client_name = self.client.name if self.client else "عميل"
        self.setWindowTitle(f"📁 إدارة وثائق العميل - {client_name}")
        self.setWindowFlags(Qt.Dialog | Qt.WindowTitleHint | Qt.WindowCloseButtonHint)
        self.setModal(True)
        self.resize(750, 420)  # تقليل الارتفاع أكثر إلى 420

        # تخصيص شريط العنوان
        self.customize_title_bar()

        # خلفية النافذة مطابقة للنمط الموحد
        self.setStyleSheet("""
            QDialog {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #334155,
                    stop:0.3 #475569, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                    stop:0.6 #60A5FA, stop:0.7 #8B5CF6, stop:0.8 #7C3AED,
                    stop:0.9 #6D28D9, stop:1 #5B21B6);
                border: none;
                border-radius: 15px;
            }
        """)

        # تخطيط النافذة (مضغوط جداً لتوفير المساحة)
        layout = QVBoxLayout(self)
        layout.setContentsMargins(10, 10, 10, 10)  # تقليل الهوامش أكثر
        layout.setSpacing(8)  # تقليل المسافات أكثر

        # العنوان الرئيسي المطور بدون إطار (مثل نافذة الإحصائيات)
        title_container = QWidget()
        title_container.setStyleSheet("""
            QWidget {
                background: transparent;
                padding: 5px;
            }
        """)

        title_inner_layout = QVBoxLayout(title_container)
        title_inner_layout.setContentsMargins(0, 0, 0, 0)
        title_inner_layout.setSpacing(5)  # تقليل المسافة

        # الأيقونة والعنوان الرئيسي (مضغوط)
        main_title = QLabel("📁 إدارة وثائق العميل")
        main_title.setAlignment(Qt.AlignCenter)
        main_title.setStyleSheet("""
            QLabel {
                color: #ffffff;
                font-size: 24px;
                font-weight: bold;
                background: transparent;
                text-shadow: 4px 4px 8px rgba(0, 0, 0, 0.9);
                padding: 5px;
            }
        """)

        # العنوان الفرعي التوضيحي (مضغوط)
        subtitle = QLabel(f"إدارة شاملة لوثائق وملفات العميل: {client_name}")
        subtitle.setAlignment(Qt.AlignCenter)
        subtitle.setStyleSheet("""
            QLabel {
                color: rgba(255, 255, 255, 0.8);
                font-size: 12px;
                font-weight: normal;
                background: transparent;
                text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
                padding: 2px;
            }
        """)

        title_inner_layout.addWidget(main_title)
        title_inner_layout.addWidget(subtitle)
        layout.addWidget(title_container)

        # إطار معلومات العميل
        client_info_frame = QFrame()
        client_info_frame.setStyleSheet("""
            QFrame {
                background: rgba(255, 255, 255, 0.1);
                border: 2px solid rgba(255, 255, 255, 0.3);
                border-radius: 12px;
                padding: 15px;
                margin: 5px;
            }
        """)
        client_info_layout = QVBoxLayout(client_info_frame)
        client_info_layout.setContentsMargins(10, 10, 10, 10)

        client_info = QLabel(f"""
📊 معلومات العميل:
• الاسم: {self.client.name}
• الهاتف: {self.client.phone or 'غير محدد'}
• البريد الإلكتروني: {self.client.email or 'غير محدد'}
• العنوان: {self.client.address or 'غير محدد'}
• الرصيد: {format_currency(self.client.balance)}
        """)
        client_info.setStyleSheet("""
            QLabel {
                color: #ffffff;
                font-size: 15px;
                font-weight: bold;
                text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.7);
                line-height: 1.6;
                padding: 5px;
                background: transparent;
            }
        """)
        client_info_layout.addWidget(client_info)
        layout.addWidget(client_info_frame)

        documents_label = QLabel("📄 الوثائق والملفات:")
        documents_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                font-size: 16px;
                font-weight: bold;
                text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.7);
                margin-bottom: 10px;
                padding: 10px;
                background: transparent;
            }
        """)
        layout.addWidget(documents_label)

        self.documents_list = QListWidget()
        self.documents_list.setMinimumHeight(150)  # تقليل الحد الأدنى للارتفاع أكثر
        self.documents_list.setStyleSheet("""
            QListWidget {
                background: rgba(255, 255, 255, 0.1);
                border: 2px solid rgba(255, 255, 255, 0.3);
                border-radius: 8px;
                padding: 8px;
                font-size: 14px;
                color: #ffffff;
                min-height: 150px;
            }
            QListWidget::item {
                padding: 10px;
                border-bottom: 1px solid rgba(255, 255, 255, 0.2);
                border-radius: 4px;
                margin: 3px;
                background: transparent;
                min-height: 25px;
            }
            QListWidget::item:selected {
                background: rgba(59, 130, 246, 0.3);
                border: 2px solid rgba(59, 130, 246, 0.6);
            }
            QListWidget::item:hover {
                background: rgba(255, 255, 255, 0.1);
            }
        """)
        layout.addWidget(self.documents_list, 1)  # إعطاء أولوية التمدد للقائمة

        # إطار واحد لجميع الأزرار
        buttons_frame = QFrame()
        buttons_frame.setStyleSheet("""
            QFrame {
                background: rgba(255, 255, 255, 0.1);
                border: 2px solid rgba(255, 255, 255, 0.3);
                border-radius: 15px;
                padding: 15px;
                margin: 5px;
            }
            QFrame:hover {
                background: rgba(255, 255, 255, 0.15);
                border: 2px solid rgba(255, 255, 255, 0.4);
            }
        """)
        buttons_layout = QVBoxLayout(buttons_frame)
        buttons_layout.setSpacing(10)

        # الصف الأول - أزرار الإضافة
        add_buttons_layout = QHBoxLayout()
        add_buttons_layout.setSpacing(10)

        # زر إضافة بطاقة هوية
        id_card_button = QPushButton("🆔 إضافة بطاقة هوية")
        id_card_button.clicked.connect(lambda: self.add_document("بطاقة هوية"))
        id_card_button.setMinimumHeight(60)  # ارتفاع أكبر بكثير
        self.style_advanced_button(id_card_button, 'emerald')

        # زر إضافة شيك بنكي
        bank_check_button = QPushButton("🏦 إضافة شيك بنكي")
        bank_check_button.clicked.connect(lambda: self.add_document("شيك بنكي"))
        bank_check_button.setMinimumHeight(60)  # ارتفاع أكبر بكثير
        self.style_advanced_button(bank_check_button, 'info')

        # زر إضافة شيك بريدي
        postal_check_button = QPushButton("📮 إضافة شيك بريدي")
        postal_check_button.clicked.connect(lambda: self.add_document("شيك بريدي"))
        postal_check_button.setMinimumHeight(60)  # ارتفاع أكبر بكثير
        self.style_advanced_button(postal_check_button, 'purple')

        add_buttons_layout.addWidget(id_card_button)
        add_buttons_layout.addWidget(bank_check_button)
        add_buttons_layout.addWidget(postal_check_button)

        # الصف الثاني - أزرار الإجراءات
        action_buttons_layout = QHBoxLayout()
        action_buttons_layout.setSpacing(10)

        # زر عرض الوثيقة
        view_button = QPushButton("👁️ عرض الوثيقة")
        view_button.clicked.connect(self.view_document)
        view_button.setMinimumHeight(60)  # ارتفاع أكبر بكثير
        self.style_advanced_button(view_button, 'cyan')

        # زر حذف الوثيقة
        delete_button = QPushButton("🗑️ حذف الوثيقة")
        delete_button.clicked.connect(self.delete_document)
        delete_button.setMinimumHeight(60)  # ارتفاع أكبر بكثير
        self.style_advanced_button(delete_button, 'danger')

        # زر الإغلاق
        close_button = QPushButton("❌ إغلاق")
        close_button.clicked.connect(self.accept)
        close_button.setMinimumHeight(60)  # ارتفاع أكبر بكثير
        self.style_advanced_button(close_button, 'danger')

        action_buttons_layout.addWidget(view_button)
        action_buttons_layout.addWidget(delete_button)
        action_buttons_layout.addWidget(close_button)

        # إضافة الصفوف للإطار
        buttons_layout.addLayout(add_buttons_layout)
        buttons_layout.addLayout(action_buttons_layout)
        layout.addWidget(buttons_frame)

        # تحميل الوثائق الموجودة
        self.load_documents()

    def load_documents(self):
        """تحميل الوثائق من قاعدة البيانات"""
        try:
            self.documents_list.clear()
            self.documents = []  # مسح القائمة المحلية

            # تحميل الوثائق من قاعدة البيانات
            db_documents = self.session.query(Document).filter(
                Document.client_id == self.client.id
            ).order_by(Document.upload_date.desc()).all()

            if not db_documents:
                item = QListWidgetItem("📝 لا توجد وثائق مضافة بعد")
                item.setData(Qt.UserRole, None)
                self.documents_list.addItem(item)
                return

            # تحويل وثائق قاعدة البيانات للقائمة المحلية
            for db_doc in db_documents:
                doc = {
                    'id': db_doc.id,
                    'title': db_doc.title,
                    'description': db_doc.description,
                    'file_path': db_doc.file_path,
                    'type': db_doc.file_type,
                    'client_id': db_doc.client_id,
                    'created_at': db_doc.upload_date
                }
                self.documents.append(doc)

                # تحديد الأيقونة حسب نوع الوثيقة
                if doc['type'] == 'بطاقة هوية':
                    icon = "🆔"
                elif doc['type'] == 'عقد':
                    icon = "📋"
                else:
                    icon = "📄"

                item_text = f"{icon} {doc['title']}"
                if doc['description']:
                    item_text += f" - {doc['description'][:50]}{'...' if len(doc['description']) > 50 else ''}"

                item = QListWidgetItem(item_text)
                item.setData(Qt.UserRole, doc)
                self.documents_list.addItem(item)

        except Exception as e:
            print(f"خطأ في تحميل الوثائق: {e}")
            show_error_message("خطأ", f"حدث خطأ في تحميل الوثائق: {str(e)}")

    def add_document(self, doc_type):
        """إضافة وثيقة جديدة"""
        try:
            from PyQt5.QtWidgets import QFileDialog

            file_path, _ = QFileDialog.getOpenFileName(
                self, f"اختر {doc_type}", "",
                "جميع الملفات (*.*);;ملفات PDF (*.pdf);;ملفات الصور (*.jpg *.jpeg *.png);;ملفات Word (*.doc *.docx)"
            )

            if file_path:
                # إنشاء عنوان تلقائي من اسم الملف
                import os
                file_name = os.path.basename(file_path)
                title = f"{doc_type} - {file_name}"
                description = f"وثيقة {doc_type} للعميل {self.client.name}"

                # حفظ الوثيقة في قاعدة البيانات
                try:
                    # إنشاء وثيقة جديدة
                    new_document = Document(
                        title=title,
                        description=description,
                        file_path=file_path,
                        file_type=doc_type,
                        client_id=self.client.id
                    )

                    # حفظ في قاعدة البيانات
                    self.session.add(new_document)
                    self.session.commit()

                    show_info_message("تم الحفظ", f"تم حفظ {doc_type} بنجاح")
                    self.load_documents()  # إعادة تحميل القائمة

                except Exception as db_error:
                    self.session.rollback()
                    show_error_message("خطأ في الحفظ",
                        f"حدث خطأ في حفظ الوثيقة:\n{str(db_error)}")
                    return

        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ في إضافة {doc_type}: {str(e)}")

    def view_document(self):
        """عرض الوثيقة المحددة"""
        try:
            current_item = self.documents_list.currentItem()
            if not current_item:
                show_error_message("خطأ", "الرجاء اختيار وثيقة من القائمة")
                return

            document = current_item.data(Qt.UserRole)
            if not document:
                show_error_message("خطأ", "لا توجد وثيقة محددة")
                return

            # التحقق من وجود الملف
            import os
            if not os.path.exists(document['file_path']):
                show_error_message("خطأ", "الملف غير موجود في المسار المحدد")
                return

            # فتح الملف بالبرنامج الافتراضي
            import platform
            import subprocess
            if platform.system() == 'Windows':
                os.startfile(document['file_path'])
            elif platform.system() == 'Darwin':  # macOS
                subprocess.call(['open', document['file_path']])
            else:  # Linux
                subprocess.call(['xdg-open', document['file_path']])

        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ في عرض الوثيقة: {str(e)}")

    def delete_document(self):
        """حذف الوثيقة المحددة"""
        try:
            current_item = self.documents_list.currentItem()
            if not current_item:
                show_error_message("خطأ", "الرجاء اختيار وثيقة من القائمة")
                return

            document = current_item.data(Qt.UserRole)
            if not document:
                show_error_message("خطأ", "لا توجد وثيقة محددة")
                return

            # تأكيد الحذف
            from PyQt5.QtWidgets import QMessageBox
            reply = QMessageBox.question(
                self, "تأكيد الحذف",
                f"هل أنت متأكد من حذف الوثيقة:\n{document['title']}؟",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )

            if reply == QMessageBox.Yes:
                try:
                    # حذف من قاعدة البيانات
                    db_document = self.session.query(Document).filter(
                        Document.id == document['id']
                    ).first()

                    if db_document:
                        self.session.delete(db_document)
                        self.session.commit()

                    # حذف من القائمة المحلية
                    self.documents.remove(document)

                    show_info_message("تم الحذف", "تم حذف الوثيقة بنجاح من قاعدة البيانات")
                    self.load_documents()  # إعادة تحميل القائمة

                except Exception as db_error:
                    self.session.rollback()
                    show_error_message("خطأ في الحذف",
                        f"حدث خطأ في حذف الوثيقة من قاعدة البيانات:\n{str(db_error)}")
                    return

        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ في حذف الوثيقة: {str(e)}")

    def style_advanced_button(self, button, button_type):
        """تطبيق تصميم متطور على الأزرار"""
        try:
            if self.parent_widget and hasattr(self.parent_widget, 'style_advanced_button'):
                self.parent_widget.style_advanced_button(button, button_type)
            else:
                # تصميم بسيط كبديل
                colors = {
                    'emerald': '#10b981',
                    'info': '#3b82f6',
                    'warning': '#f59e0b',
                    'danger': '#ef4444',
                    'secondary': '#6b7280',
                    'cyan': '#06b6d4',
                    'purple': '#8b5cf6'  # لون للشيك البريدي
                }
                color = colors.get(button_type, '#6b7280')
                button.setStyleSheet(f"""
                    QPushButton {{
                        background-color: {color};
                        color: white;
                        border: none;
                        border-radius: 8px;
                        padding: 18px 20px;  /* حشو أكبر لمنع قص النصوص */
                        font-size: 14px;
                        font-weight: bold;
                        line-height: 1.4;  /* تحسين ارتفاع السطر */
                        text-align: center;
                        vertical-align: middle;
                    }}
                    QPushButton:hover {{
                        background-color: {color}dd;
                    }}
                    QPushButton:pressed {{
                        background-color: {color}bb;
                    }}
                """)
        except Exception as e:
            print(f"خطأ في تصميم الزر: {e}")

    def customize_title_bar(self):
        """تخصيص شريط العنوان"""
        try:
            TitleBarStyler.apply_advanced_title_bar_styling(self)
        except Exception as e:
            print(f"تحذير: فشل في تطبيق تصميم شريط العنوان: {e}")





class WhatsAppDialog(QDialog):
    """نافذة خيارات الواتساب للعميل"""

    def __init__(self, client, parent=None):
        super().__init__(parent)
        self.client = client
        self.parent_widget = parent
        self.setup_ui()

    def setup_ui(self):
        """إعداد واجهة النافذة"""
        self.setWindowTitle(f"📞 واتساب - {self.client.name}")
        self.setWindowFlags(Qt.Dialog | Qt.WindowTitleHint | Qt.WindowCloseButtonHint)
        self.setModal(True)
        self.resize(500, 400)

        # تطبيق تصميم شريط العنوان
        self.customize_title_bar()

        # تطبيق نفس خلفية نافذة الإحصائيات
        self.setStyleSheet("""
            QDialog {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #334155,
                    stop:0.3 #475569, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                    stop:0.6 #60A5FA, stop:0.7 #8B5CF6, stop:0.8 #7C3AED,
                    stop:0.9 #6D28D9, stop:1 #5B21B6);
                border: none;
                border-radius: 15px;
            }
        """)

        layout = QVBoxLayout()
        layout.setSpacing(15)
        layout.setContentsMargins(20, 20, 20, 20)

        # حاوي العنوان مطابق لنافذة الإحصائيات
        title_container = QWidget()
        title_container.setStyleSheet("background: transparent;")
        title_inner_layout = QVBoxLayout()
        title_inner_layout.setContentsMargins(0, 0, 0, 0)
        title_inner_layout.setSpacing(5)
        title_container.setLayout(title_inner_layout)

        # العنوان الرئيسي
        main_title = QLabel(f"📞 التواصل مع العميل")
        main_title.setAlignment(Qt.AlignCenter)
        main_title.setStyleSheet("""
            QLabel {
                color: #ffffff;
                font-size: 30px;
                font-weight: bold;
                background: transparent;
                text-shadow: 3px 3px 6px rgba(0, 0, 0, 0.8);
                padding: 10px;
            }
        """)

        # العنوان الفرعي التوضيحي
        subtitle = QLabel(f"إدارة الاتصالات والرسائل مع {self.client.name}")
        subtitle.setAlignment(Qt.AlignCenter)
        subtitle.setStyleSheet("""
            QLabel {
                color: rgba(255, 255, 255, 0.8);
                font-size: 14px;
                font-weight: normal;
                background: transparent;
                text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
                padding: 5px;
            }
        """)

        title_inner_layout.addWidget(main_title)
        title_inner_layout.addWidget(subtitle)
        layout.addWidget(title_container)

        # معلومات العميل
        info_layout = QVBoxLayout()
        info_layout.setSpacing(10)

        # عرض أرقام الهاتف
        if self.client.phone:
            phone_numbers = [phone.strip() for phone in self.client.phone.split(",") if phone.strip()]

            for i, phone in enumerate(phone_numbers):
                # إطار رقم الهاتف والأزرار
                phone_frame = QFrame()
                phone_frame.setStyleSheet("""
                    QFrame {
                        background: rgba(255, 255, 255, 0.1);
                        border: 2px solid rgba(255, 255, 255, 0.2);
                        border-radius: 12px;
                        padding: 10px;
                        margin: 3px;
                    }
                    QFrame:hover {
                        background: rgba(255, 255, 255, 0.15);
                        border: 2px solid rgba(255, 255, 255, 0.3);
                    }
                """)

                phone_layout = QHBoxLayout()
                phone_layout.setSpacing(10)

                # رقم الهاتف
                phone_label = QLabel(f"📱 {phone}")
                phone_label.setFont(QFont("Segoe UI", 12, QFont.Bold))
                phone_label.setStyleSheet("""
                    QLabel {
                        color: #ffffff;
                        background: transparent;
                        text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.7);
                        padding: 5px;
                    }
                """)
                phone_layout.addWidget(phone_label)

                # أزرار الإجراءات بالحجم الأصلي
                whatsapp_btn = QPushButton("💬 واتساب")
                self.style_advanced_button(whatsapp_btn, 'emerald')
                whatsapp_btn.clicked.connect(lambda checked, p=phone: self.open_whatsapp(p))

                call_btn = QPushButton("📞 اتصال")
                self.style_advanced_button(call_btn, 'info')
                call_btn.clicked.connect(lambda checked, p=phone: self.make_call(p))

                phone_layout.addWidget(whatsapp_btn)
                phone_layout.addWidget(call_btn)

                phone_frame.setLayout(phone_layout)
                info_layout.addWidget(phone_frame)
        else:
            no_phone_label = QLabel("❌ لا توجد أرقام هاتف مسجلة لهذا العميل")
            no_phone_label.setAlignment(Qt.AlignCenter)
            no_phone_label.setStyleSheet("""
                QLabel {
                    color: #ff6b6b;
                    background: transparent;
                    font-weight: bold;
                    font-size: 14px;
                    padding: 20px;
                    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.7);
                }
            """)
            info_layout.addWidget(no_phone_label)

        layout.addLayout(info_layout)

        # إطار كتابة الرسالة
        message_frame = QFrame()
        message_frame.setStyleSheet("""
            QFrame {
                background: rgba(255, 255, 255, 0.08);
                border: 2px solid rgba(255, 255, 255, 0.25);
                border-radius: 15px;
                padding: 15px;
                margin: 5px;
            }
            QFrame:hover {
                background: rgba(255, 255, 255, 0.12);
                border: 2px solid rgba(255, 255, 255, 0.35);
            }
        """)
        message_layout = QVBoxLayout()
        message_layout.setSpacing(10)

        # عنوان القسم
        message_title = QLabel("✉️ كتابة رسالة واتساب")
        message_title.setAlignment(Qt.AlignCenter)
        message_title.setStyleSheet("""
            QLabel {
                color: #ffffff;
                font-size: 16px;
                font-weight: bold;
                background: transparent;
                text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
                padding: 5px;
            }
        """)
        message_layout.addWidget(message_title)

        # حقل كتابة الرسالة
        self.message_text = QTextEdit()
        self.message_text.setPlainText(f"مرحباً {self.client.name}،\n\nنتواصل معك من شركة Smart Finish.\n\nشكراً لك.")
        self.message_text.setStyleSheet("""
            QTextEdit {
                background: rgba(255, 255, 255, 0.95);
                border: 2px solid rgba(255, 255, 255, 0.3);
                border-radius: 10px;
                padding: 12px;
                font-size: 14px;
                font-weight: bold;
                color: #2d3748;
                min-height: 100px;
                max-height: 120px;
                line-height: 1.4;
            }
            QTextEdit:focus {
                border: 2px solid rgba(34, 197, 94, 0.6);
                background: rgba(255, 255, 255, 1.0);
                box-shadow: 0 0 10px rgba(34, 197, 94, 0.3);
            }
        """)
        message_layout.addWidget(self.message_text)

        # زر إرسال واتساب مع ارتفاع أكبر
        whatsapp_send_btn = QPushButton("📱 إرسال واتساب")
        whatsapp_send_btn.setMinimumHeight(55)  # ارتفاع أكبر
        self.style_advanced_button(whatsapp_send_btn, 'emerald')
        whatsapp_send_btn.clicked.connect(self.send_whatsapp_message)
        message_layout.addWidget(whatsapp_send_btn)

        message_frame.setLayout(message_layout)
        layout.addWidget(message_frame)

        # أزرار التحكم
        buttons_layout = QHBoxLayout()
        buttons_layout.setContentsMargins(20, 10, 20, 10)

        # زر إغلاق كبير ومتوسط مع التصميم الأصلي وارتفاع أكبر
        close_btn = QPushButton("❌ إغلاق")
        close_btn.setMinimumWidth(150)
        close_btn.setMinimumHeight(55)  # ارتفاع أكبر
        self.style_advanced_button(close_btn, 'danger')
        close_btn.clicked.connect(self.close)

        # توسيط الزر
        buttons_layout.addStretch()
        buttons_layout.addWidget(close_btn)
        buttons_layout.addStretch()

        layout.addLayout(buttons_layout)
        self.setLayout(layout)

    def open_whatsapp(self, phone_number):
        """فتح واتساب مع رقم الهاتف"""
        try:
            import webbrowser
            # تنظيف رقم الهاتف من المسافات والرموز
            clean_phone = ''.join(filter(str.isdigit, phone_number))

            # معالجة محسنة للأرقام المصرية
            if clean_phone.startswith('0') and len(clean_phone) == 11:
                # الأرقام المصرية التي تبدأ بـ 0 (مثل 01017760368)
                clean_phone = '2' + clean_phone  # تصبح 201017760368
            elif clean_phone.startswith('1') and len(clean_phone) == 10:
                # الأرقام بدون الصفر (مثل 1017760368)
                clean_phone = '20' + clean_phone  # تصبح 201017760368
            elif not clean_phone.startswith('20') and len(clean_phone) >= 10:
                # أي رقم آخر طويل بما فيه الكفاية
                clean_phone = '20' + clean_phone.lstrip('0')

            # التحقق النهائي من صحة الرقم
            if len(clean_phone) < 12 or not clean_phone.startswith('20'):
                # إذا لم ينجح شيء، نضع 20 في البداية
                original_clean = ''.join(filter(str.isdigit, phone_number))
                clean_phone = '20' + original_clean.lstrip('0')

            # رابط واتساب
            whatsapp_url = f"https://wa.me/{clean_phone}"
            webbrowser.open(whatsapp_url)

        except Exception as e:
            from utils import show_error_message
            show_error_message("خطأ", f"حدث خطأ في فتح واتساب: {str(e)}")

    def make_call(self, phone_number):
        """فتح واتساب مع خيار الاتصال المباشر"""
        try:
            import webbrowser
            import subprocess
            import platform

            # تنظيف رقم الهاتف وإضافة كود مصر
            clean_phone = ''.join(filter(str.isdigit, phone_number))

            # معالجة الأرقام المصرية
            if clean_phone.startswith('0') and len(clean_phone) == 11:
                # الأرقام المصرية التي تبدأ بـ 0 (مثل 01017760368)
                clean_phone = '2' + clean_phone  # تصبح 201017760368
            elif clean_phone.startswith('1') and len(clean_phone) == 10:
                # الأرقام بدون الصفر (مثل 1017760368)
                clean_phone = '20' + clean_phone  # تصبح 201017760368
            elif not clean_phone.startswith('20') and len(clean_phone) >= 10:
                # أي رقم آخر طويل بما فيه الكفاية
                clean_phone = '20' + clean_phone.lstrip('0')

            # التحقق النهائي من صحة الرقم
            if len(clean_phone) < 12 or not clean_phone.startswith('20'):
                # إذا لم ينجح شيء، نضع 20 في البداية
                original_clean = ''.join(filter(str.isdigit, phone_number))
                clean_phone = '20' + original_clean.lstrip('0')

            # محاولة فتح واتساب مع رابط الاتصال المباشر
            system = platform.system().lower()

            try:
                if system == "windows":
                    # في ويندوز - محاولة فتح واتساب مع رابط الاتصال
                    whatsapp_call_url = f"whatsapp://call?phone={clean_phone}"
                    subprocess.run(f'start "" "{whatsapp_call_url}"', shell=True, check=True)
                elif system == "darwin":  # macOS
                    # في ماك - فتح واتساب مع رابط الاتصال
                    whatsapp_call_url = f"whatsapp://call?phone={clean_phone}"
                    subprocess.run(['open', whatsapp_call_url])
                else:
                    # لينكس أو أنظمة أخرى
                    whatsapp_call_url = f"whatsapp://call?phone={clean_phone}"
                    subprocess.run(['xdg-open', whatsapp_call_url])

            except:
                # إذا فشل رابط التطبيق، استخدم واتساب ويب مع رسالة اتصال
                try:
                    whatsapp_web_call = f"https://web.whatsapp.com/send?phone={clean_phone}&text=📞%20طلب%20اتصال"
                    webbrowser.open(whatsapp_web_call)
                except:
                    # كحل أخير، فتح واتساب عادي
                    whatsapp_url = f"https://wa.me/{clean_phone}"
                    webbrowser.open(whatsapp_url)

        except Exception as e:
            from utils import show_error_message
            show_error_message("خطأ", f"حدث خطأ في فتح واتساب: {str(e)}")

    def send_whatsapp_message(self):
        """إرسال رسالة واتساب من حقل النص"""
        try:
            if not self.client.phone:
                from utils import show_error_message
                show_error_message("خطأ", "لا توجد أرقام هاتف مسجلة لهذا العميل")
                return

            # الحصول على أول رقم هاتف
            phone_numbers = [phone.strip() for phone in self.client.phone.split(",") if phone.strip()]
            if not phone_numbers:
                from utils import show_error_message
                show_error_message("خطأ", "لا توجد أرقام هاتف صحيحة")
                return

            phone_number = phone_numbers[0]

            # الحصول على نص الرسالة من حقل النص
            message = self.message_text.toPlainText().strip()
            if not message:
                from utils import show_error_message
                show_error_message("تنبيه", "يرجى كتابة نص الرسالة أولاً")
                return

            # معالجة رقم الهاتف
            import webbrowser
            import urllib.parse

            # تنظيف رقم الهاتف مع معالجة محسنة
            clean_phone = ''.join(filter(str.isdigit, phone_number))

            # معالجة محسنة للأرقام المصرية
            if clean_phone.startswith('0') and len(clean_phone) == 11:
                clean_phone = '2' + clean_phone
            elif clean_phone.startswith('1') and len(clean_phone) == 10:
                clean_phone = '20' + clean_phone
            elif not clean_phone.startswith('20') and len(clean_phone) >= 10:
                clean_phone = '20' + clean_phone.lstrip('0')

            # التحقق النهائي من صحة الرقم
            if len(clean_phone) < 12 or not clean_phone.startswith('20'):
                original_clean = ''.join(filter(str.isdigit, phone_number))
                clean_phone = '20' + original_clean.lstrip('0')

            # ترميز الرسالة
            encoded_message = urllib.parse.quote(message)

            # رابط واتساب مع الرسالة
            whatsapp_url = f"https://wa.me/{clean_phone}?text={encoded_message}"
            webbrowser.open(whatsapp_url)

        except Exception as e:
            from utils import show_error_message
            show_error_message("خطأ", f"حدث خطأ في إرسال الرسالة: {str(e)}")

    def style_advanced_button(self, button, button_type):
        """تطبيق تصميم متطور على الأزرار"""
        try:
            if self.parent_widget and hasattr(self.parent_widget, 'style_advanced_button'):
                self.parent_widget.style_advanced_button(button, button_type)
            else:
                # تصميم بسيط كبديل
                colors = {
                    'emerald': '#10b981',
                    'info': '#3b82f6',
                    'cyan': '#06b6d4',
                    'orange': '#f97316',
                    'danger': '#ef4444'
                }
                color = colors.get(button_type, '#6b7280')
                button.setStyleSheet(f"""
                    QPushButton {{
                        background-color: {color};
                        color: white;
                        border: none;
                        border-radius: 8px;
                        padding: 10px 15px;
                        font-size: 12px;
                        font-weight: bold;
                        min-width: 100px;
                    }}
                    QPushButton:hover {{
                        background-color: {color}dd;
                    }}
                    QPushButton:pressed {{
                        background-color: {color}bb;
                    }}
                """)
        except Exception as e:
            print(f"خطأ في تصميم الزر: {e}")

    def customize_title_bar(self):
        """تخصيص شريط العنوان"""
        try:
            TitleBarStyler.apply_advanced_title_bar_styling(self)
        except Exception as e:
            print(f"تحذير: فشل في تطبيق تصميم شريط العنوان: {e}")





